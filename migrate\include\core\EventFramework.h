/**
 * @file EventFramework.h
 * @brief RSU设备控制系统事件处理框架
 * 
 * 本文件从Delphi uEventFramework.pas迁移而来，提供统一的事件处理机制
 * 支持异步事件处理、事件过滤、优先级管理等功能
 */

#ifndef EVENTFRAMEWORK_H
#define EVENTFRAMEWORK_H

#include <QObject>
#include <QDateTime>
#include <QString>
#include <QVector>
#include <QQueue>
#include <QMutex>
#include <QThread>
#include <QTimer>
#include <QTextStream>
#include <QFile>
#include <memory>
#include <functional>
#include "core/DataStructures.h"
#include "CommonDataStructures.h"

namespace ETC {

/**
 * 事件类型枚举
 * 对应原Delphi代码中的TEventType
 */
enum class EventType {
    // RSU设备相关事件
    RSUError,           // RSU错误事件
    RSUReset,           // RSU重置事件
    RSUHeart,           // RSU心跳事件
    
    // OBU数据获取事件
    GetB0,              // 获取B0信息（RSU状态）
    GetB1,              // 获取B1信息（心跳响应）
    GetOBUID,           // 获取OBU ID（B2信息）
    GetB4,              // 获取B4信息（IC卡属性）
    GetB5,              // 获取B5信息（交易结果）
    GetOBUVehicle,      // 获取OBU车辆信息
    GetCPUInfo,         // 获取CPU卡信息
    GetCPUInfoNew,      // 获取新版CPU卡信息
    
    // OBU交易事件
    OBUSessionEnd,      // OBU会话结束
    OBUConsumed,        // OBU消费事件
    OBUError,           // OBU错误事件
    
    // OBU其他事件
    OBUB7,              // B7事件（历史交易）
    OBUBA,              // BA事件（PSAM签到）
    OBUBB,              // BB事件（PSAM授权结果）
    OBUD0,              // D0事件（坐标信息）
    
    // 超时和认证事件
    PurTimeOut,         // 购买超时事件
    PSAMInit,           // PSAM初始化事件
    PSAMResult          // PSAM认证结果事件
};

/**
 * 事件优先级枚举
 */
enum class EventPriority {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3
};

/**
 * 事件处理结果枚举
 */
enum class EventResult {
    Success,            // 处理成功
    Failed,             // 处理失败
    Retry,              // 需要重试
    Ignored             // 忽略事件
};

/**
 * 基础事件数据类
 */
class EventData {
public:
    EventData(EventType type);
    virtual ~EventData() = default;
    
    QString getEventID() const { return eventID; }
    EventType getEventType() const { return eventType; }
    QDateTime getTimeStamp() const { return timeStamp; }
    EventPriority getPriority() const { return priority; }
    void setPriority(EventPriority p) { priority = p; }
    
    virtual QString toString() const;

protected:
    QString eventID;
    EventType eventType;
    QDateTime timeStamp;
    EventPriority priority;
};

/**
 * RSU错误事件数据
 */
class RSUErrorEventData : public EventData {
public:
    RSUErrorEventData(uint8_t errorStatus);
    
    uint8_t getErrorStatus() const { return errorStatus; }
    QString toString() const override;

private:
    uint8_t errorStatus;
};

/**
 * GetB0事件数据
 */
class GetB0EventData : public EventData {
public:
    GetB0EventData(uint8_t rsctl, const TB0Info& b0Info);
    
    uint8_t getRSCTL() const { return rsctl; }
    const TB0Info& getB0Info() const { return b0Info; }
    QString toString() const override;

private:
    uint8_t rsctl;
    TB0Info b0Info;
};

/**
 * GetOBUID事件数据（B2信息）
 */
class GetOBUIDEventData : public EventData {
public:
    GetOBUIDEventData(uint8_t rsctl, uint32_t obuid, const TB2Info& b2Info);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    const TB2Info& getB2Info() const { return b2Info; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    TB2Info b2Info;
};

/**
 * OBU错误事件数据
 */
class OBUErrorEventData : public EventData {
public:
    OBUErrorEventData(uint8_t rsctl, uint32_t obuid, uint8_t state, uint8_t errorCode);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    uint8_t getState() const { return state; }
    uint8_t getErrorCode() const { return errorCode; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    uint8_t state;
    uint8_t errorCode;
};

/**
 * GetBA事件数据
 */
class GetBAEventData : public EventData {
public:
    GetBAEventData(uint8_t rsctl, const TBAInfo& baInfo);
    
    uint8_t getRSCTL() const { return rsctl; }
    const TBAInfo& getBAInfo() const { return baInfo; }
    QString toString() const override;

private:
    uint8_t rsctl;
    TBAInfo baInfo;
};

/**
 * GetBB事件数据
 */
class GetBBEventData : public EventData {
public:
    GetBBEventData(uint8_t rsctl, uint32_t obuid, uint8_t errorCode, const QVector<TAuthResult>& authResults);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    uint8_t getErrorCode() const { return errorCode; }
    const QVector<TAuthResult>& getAuthResults() const { return authResults; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    uint8_t errorCode;
    QVector<TAuthResult> authResults;
};

/**
 * GetB1事件数据
 */
class GetB1EventData : public EventData {
public:
    GetB1EventData(uint8_t rsctl, const TB1Info& b1Info);
    
    uint8_t getRSCTL() const { return rsctl; }
    const TB1Info& getB1Info() const { return b1Info; }
    QString toString() const override;

private:
    uint8_t rsctl;
    TB1Info b1Info;
};

/**
 * GetB2事件数据
 */
class GetB2EventData : public EventData {
public:
    GetB2EventData(uint8_t rsctl, uint32_t obuid, const TB2Info& b2Info);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    const TB2Info& getB2Info() const { return b2Info; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    TB2Info b2Info;
};

/**
 * GetB4事件数据
 */
class GetB4EventData : public EventData {
public:
    GetB4EventData(uint8_t rsctl, uint32_t obuid, const TB4Info& b4Info);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    const TB4Info& getB4Info() const { return b4Info; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    TB4Info b4Info;
};

/**
 * GetB5事件数据
 */
class GetB5EventData : public EventData {
public:
    GetB5EventData(uint8_t rsctl, uint32_t obuid, const TB5Info& b5Info);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    const TB5Info& getB5Info() const { return b5Info; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    TB5Info b5Info;
};

/**
 * GetOBUVehicle事件数据
 */
class GetOBUVehicleEventData : public EventData {
public:
    GetOBUVehicleEventData(uint8_t rsctl, uint32_t obuid, const QString& plate, 
                          uint8_t plateCode, int vehicleClass, uint8_t userType, 
                          const TVClassConversion& conversion);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    QString getPlate() const { return plate; }
    uint8_t getPlateCode() const { return plateCode; }
    int getVehicleClass() const { return vehicleClass; }
    uint8_t getUserType() const { return userType; }
    const TVClassConversion& getConversion() const { return conversion; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    QString plate;
    uint8_t plateCode;
    int vehicleClass;
    uint8_t userType;
    TVClassConversion conversion;
};

/**
 * GetCPUInfo事件数据
 */
class GetCPUInfoEventData : public EventData {
public:
    GetCPUInfoEventData(uint8_t rsctl, uint32_t obuid, uint32_t remainMoney, 
                       const TIC0012& ic0012, const TIC0015& ic0015);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    uint32_t getRemainMoney() const { return remainMoney; }
    const TIC0012& getIC0012() const { return ic0012; }
    const TIC0015& getIC0015() const { return ic0015; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    uint32_t remainMoney;
    TIC0012 ic0012;
    TIC0015 ic0015;
};

/**
 * GetCPUInfoNew事件数据
 */
class GetCPUInfoNewEventData : public EventData {
public:
    GetCPUInfoNewEventData(uint8_t rsctl, uint32_t obuid, uint8_t obuType, uint8_t antennaId,
                          const TB4Content0and1& content0and1, const TIC0019& ic0019,
                          const TB4Content1Station& station1, const TB4Content2& content2);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    uint8_t getOBUType() const { return obuType; }
    uint8_t getAntennaId() const { return antennaId; }
    const TB4Content0and1& getContent0and1() const { return content0and1; }
    const TIC0019& getIC0019() const { return ic0019; }
    const TB4Content1Station& getStation1() const { return station1; }
    const TB4Content2& getContent2() const { return content2; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    uint8_t obuType;
    uint8_t antennaId;
    TB4Content0and1 content0and1;
    TIC0019 ic0019;
    TB4Content1Station station1;
    TB4Content2 content2;
};

/**
 * OBUSessionEnd事件数据
 */
class OBUSessionEndEventData : public EventData {
public:
    OBUSessionEndEventData(uint8_t rsctl, uint32_t obuid, const QDateTime& sessionTime,
                          uint32_t tac, uint16_t payCardTranSN, uint32_t psamTranSN,
                          const QString& rsuTerminalId, uint8_t keyVersion, uint8_t transType);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    QDateTime getSessionTime() const { return sessionTime; }
    uint32_t getTAC() const { return tac; }
    uint16_t getPayCardTranSN() const { return payCardTranSN; }
    uint32_t getPSAMTranSN() const { return psamTranSN; }
    QString getRSUTerminalId() const { return rsuTerminalId; }
    uint8_t getKeyVersion() const { return keyVersion; }
    uint8_t getTransType() const { return transType; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    QDateTime sessionTime;
    uint32_t tac;
    uint16_t payCardTranSN;
    uint32_t psamTranSN;
    QString rsuTerminalId;
    uint8_t keyVersion;
    uint8_t transType;
};

/**
 * OBUConsumed事件数据
 */
class OBUConsumedEventData : public EventData {
public:
    OBUConsumedEventData(uint8_t rsctl, uint32_t obuid, const QDateTime& purchaseTime,
                        uint32_t iccid, uint32_t tac, uint32_t psamTranSN, uint8_t keyVersion);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    QDateTime getPurchaseTime() const { return purchaseTime; }
    uint32_t getICCID() const { return iccid; }
    uint32_t getTAC() const { return tac; }
    uint32_t getPSAMTranSN() const { return psamTranSN; }
    uint8_t getKeyVersion() const { return keyVersion; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    QDateTime purchaseTime;
    uint32_t iccid;
    uint32_t tac;
    uint32_t psamTranSN;
    uint8_t keyVersion;
};

/**
 * OBUB7事件数据
 */
class OBUB7EventData : public EventData {
public:
    OBUB7EventData(uint8_t rsctl, uint32_t obuid, uint8_t recordNum, const QString& recordList);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    uint8_t getRecordNum() const { return recordNum; }
    QString getRecordList() const { return recordList; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    uint8_t recordNum;
    QString recordList;
};

/**
 * OBUD0事件数据
 */
class OBUD0EventData : public EventData {
public:
    OBUD0EventData(uint8_t rsctl, uint32_t obuid, uint8_t errorCode, int x, int y);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    uint8_t getErrorCode() const { return errorCode; }
    int getX() const { return x; }
    int getY() const { return y; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    uint8_t errorCode;
    int x;
    int y;
};

/**
 * RSUReset事件数据
 */
class RSUResetEventData : public EventData {
public:
    RSUResetEventData(uint8_t rsctl, const QString& terminalId, uint8_t algId, 
                     uint16_t manuId, const QString& rsuId, uint16_t version);
    
    uint8_t getRSCTL() const { return rsctl; }
    QString getTerminalId() const { return terminalId; }
    uint8_t getAlgId() const { return algId; }
    uint16_t getManuId() const { return manuId; }
    QString getRSUId() const { return rsuId; }
    uint16_t getVersion() const { return version; }
    QString toString() const override;

private:
    uint8_t rsctl;
    QString terminalId;
    uint8_t algId;
    uint16_t manuId;
    QString rsuId;
    uint16_t version;
};

/**
 * PurTimeOut事件数据
 */
class PurTimeOutEventData : public EventData {
public:
    PurTimeOutEventData(uint8_t rsctl, uint32_t obuid, uint8_t state, uint8_t errorCode,
                       uint16_t payCardTranSN, uint32_t psamTranSN, 
                       const QString& rsuTerminalId, uint8_t transType);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint32_t getOBUID() const { return obuid; }
    uint8_t getState() const { return state; }
    uint8_t getErrorCode() const { return errorCode; }
    uint16_t getPayCardTranSN() const { return payCardTranSN; }
    uint32_t getPSAMTranSN() const { return psamTranSN; }
    QString getRSUTerminalId() const { return rsuTerminalId; }
    uint8_t getTransType() const { return transType; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint32_t obuid;
    uint8_t state;
    uint8_t errorCode;
    uint16_t payCardTranSN;
    uint32_t psamTranSN;
    QString rsuTerminalId;
    uint8_t transType;
};

/**
 * PSAMInit事件数据
 */
class PSAMInitEventData : public EventData {
public:
    PSAMInitEventData(uint8_t rsctl, const QString& psamNo, uint8_t psamVersion,
                     const QString& areaCode, const QString& randCode);
    
    uint8_t getRSCTL() const { return rsctl; }
    QString getPSAMNo() const { return psamNo; }
    uint8_t getPSAMVersion() const { return psamVersion; }
    QString getAreaCode() const { return areaCode; }
    QString getRandCode() const { return randCode; }
    QString toString() const override;

private:
    uint8_t rsctl;
    QString psamNo;
    uint8_t psamVersion;
    QString areaCode;
    QString randCode;
};

/**
 * PSAMResult事件数据
 */
class PSAMResultEventData : public EventData {
public:
    PSAMResultEventData(uint8_t rsctl, uint16_t sw1sw2);
    
    uint8_t getRSCTL() const { return rsctl; }
    uint16_t getSW1SW2() const { return sw1sw2; }
    QString toString() const override;

private:
    uint8_t rsctl;
    uint16_t sw1sw2;
};

/**
 * 事件处理器基类
 */
class BaseEventHandler : public QObject {
    Q_OBJECT

public:
    BaseEventHandler(const QString& name, const QVector<EventType>& supportedEvents, 
                     EventPriority priority = EventPriority::Normal, QObject* parent = nullptr);
    virtual ~BaseEventHandler() = default;
    
    QString getName() const { return handlerName; }
    EventPriority getPriority() const { return handlerPriority; }
    bool canHandle(EventType eventType) const;
    
    // 处理事件的主入口
    EventResult handleEvent(std::shared_ptr<EventData> eventData);
    
    // 启用/禁用处理器
    void setEnabled(bool enabled) { isEnabled = enabled; }
    bool getEnabled() const { return isEnabled; }
    
    // 统计信息
    int getProcessedCount() const { return processedCount; }
    int getSuccessCount() const { return successCount; }
    int getFailedCount() const { return failedCount; }
    void resetStatistics();

protected:
    // 子类需要实现的核心处理逻辑
    virtual EventResult doHandleEvent(std::shared_ptr<EventData> eventData) = 0;
    
    // 工具方法
    void writeLog(const QString& message, int level = 0);
    void handleError(const QString& errorMessage, const std::exception& e);

private:
    QString handlerName;
    QVector<EventType> supportedEventTypes;
    EventPriority handlerPriority;
    bool isEnabled;
    
    // 统计信息
    int processedCount;
    int successCount;
    int failedCount;
    QDateTime lastProcessTime;
};

/**
 * 事件管理器类
 * 负责事件的分发、处理器管理、性能监控等
 */
class EventManager : public QObject {
    Q_OBJECT

public:
    static EventManager* getInstance();
    
    // 注册和管理事件处理器
    void registerHandler(std::shared_ptr<BaseEventHandler> handler);
    void unregisterHandler(const QString& handlerName);
    std::shared_ptr<BaseEventHandler> getHandler(const QString& handlerName);
    QVector<std::shared_ptr<BaseEventHandler>> getHandlersForEvent(EventType eventType);
    
    // 事件分发
    void dispatchEvent(std::shared_ptr<EventData> eventData);
    void dispatchEventAsync(std::shared_ptr<EventData> eventData);
    
    // 配置管理
    void setMaxQueueSize(int size) { maxQueueSize = size; }
    void setRetryCount(int count) { maxRetryCount = count; }
    void setRetryInterval(int interval) { retryInterval = interval; }
    
    // 日志管理
    void writeLog(const QString& message, int level = 0);
    void setLogFile(const QString& logFile);
    void setLogLevel(int level) { logLevel = level; }
    
    // 性能监控
    void enablePerformanceMonitoring(bool enable) { performanceMonitoringEnabled = enable; }
    QString getPerformanceReport() const;
    void resetPerformanceCounters();
    
    // 启动和停止
    void start();
    void stop();
    bool isRunning() const { return running; }

signals:
    void eventProcessed(EventType eventType, EventResult result);
    void errorOccurred(const QString& errorMessage);

private slots:
    void processEventQueue();
    void handleRetryTimer();

private:
    EventManager(QObject* parent = nullptr);
    ~EventManager();
    
    // 单例实例
    static EventManager* instance;
    static QMutex instanceMutex;
    
    // 处理器管理
    QVector<std::shared_ptr<BaseEventHandler>> handlers;
    QMutex handlersMutex;
    
    // 事件队列
    QQueue<std::shared_ptr<EventData>> eventQueue;
    QMutex queueMutex;
    int maxQueueSize;
    
    // 重试机制
    struct RetryItem {
        std::shared_ptr<EventData> eventData;
        int retryCount;
        QDateTime nextRetryTime;
    };
    QVector<RetryItem> retryQueue;
    QMutex retryMutex;
    int maxRetryCount;
    int retryInterval;  // milliseconds
    
    // 定时器
    QTimer* processTimer;
    QTimer* retryTimer;
    
    // 日志管理
    QString logFileName;
    QFile* logFile;
    QTextStream* logStream;
    QMutex logMutex;
    int logLevel;
    
    // 性能监控
    bool performanceMonitoringEnabled;
    QDateTime startTime;
    int totalEventsProcessed;
    int totalEventsSucceeded;
    int totalEventsFailed;
    mutable QMutex performanceMutex;
    
    // 运行状态
    bool running;
    
    // 内部方法
    void processEventInternal(std::shared_ptr<EventData> eventData);
    void addToRetryQueue(std::shared_ptr<EventData> eventData, int retryCount);
    void initializeLog();
    void cleanupLog();
};

} // namespace ETC

// 公共数据结构现在已移至 CommonDataStructures.h 文件中
// 包括：TPSAMInfo, TB0Info, TBAPSAMInfo, TB2Info, TBAInfo, TAuthResult

#endif // EVENTFRAMEWORK_H
