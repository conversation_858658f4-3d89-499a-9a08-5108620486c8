/**
 * @file OBUDevice.h
 * @brief ETC系统OBU设备管理模块
 * 
 * 本文件从Delphi uOBUNew.pas迁移而来，提供OBU设备的完整操作接口
 * 支持IC卡数据读写、车辆信息获取、交易处理、PSAM认证等功能
 */

#ifndef OBUDEVICE_H
#define OBUDEVICE_H

#include <QObject>
#include <QDateTime>
#include <QString>
#include <QByteArray>
#include <QTimer>
#include <QMutex>
#include <QThread>
#include <QSerialPort>
#include <QTcpSocket>
#include <QUdpSocket>
#include <QMap>
#include <QVector>
#include <cstdint>
#include <memory>
#include "core/DataStructures.h"
#include "CommonDataStructures.h"
#include "network/TCPCommunication.h"

namespace ETC {

// 常量定义
const quint8 JY_ENTRY = 0x03;
const quint8 JY_EXIT = 0x04;

// CRC16计算表
extern const quint16 CRCTable16[256];

/**
 * IC卡数据结构定义
 */

// IC0015卡片信息（储值卡/记账卡基础信息）
//struct TIC0015 {
//    char cardLabel[8];          // 卡片标签
//    quint8 cardType;           // 22储值卡 23记账卡
//    quint8 cardVer;            // 升级用
//    quint8 cardNetwork[2];     // BCD码，联网之前，只处理编号为3600的卡片
//    quint8 cardNo[8];          // BCD码
//    quint8 issueDate[4];       // BCD码，发行日期
//    quint8 expireDate[4];      // BCD码，到期日期
//    quint8 fci[2];             // BCD码，0没有折扣 1000 全折 20表示98折
//    char vehiclePlate[16];     // 如果车牌不为空，则该车牌与OBU车牌必须一致
//    quint16 spare;
//    quint8 spare2[7];          // 新增7字节长度
    
//    TIC0015() { memset(this, 0, sizeof(TIC0015)); }
//};

// IC0015新版卡片信息
//struct TIC0015New {
//    char cardLabel[8];         // CPU卡一级分散因子
//    quint8 cardType;          // 22储值卡 23记账卡
//    quint8 cardVer;           // 升级用 0X10代表支持复合消费
//    quint8 cardNetwork[2];    // BCD码，江西新发国标赣通卡内存储的网络编号为3601
//    quint8 cardNo[8];         // BCD码
//    quint8 issueDate[4];      // BCD码
//    quint8 expireDate[4];     // BCD码
//    char vehiclePlate[12];    // 如果车牌不为空，则该车牌与OBU车牌必须一致
//    quint8 userType;          // 0 - 普通用户 6 - 公务车用户
//    char plateColor[2];       // 车牌颜色
//    quint8 spare2[7];         // 新增7字节长度
    
//    TIC0015New() { memset(this, 0, sizeof(TIC0015New)); }
//};

// IC0012卡片结构（入口信息）
//struct TIC0012 {
//    quint16 entryNetwork;      // 3600
//    quint16 entryStation;      // 收费站编号去掉36后的整数
//    quint8 entryLane;          // 入口车道
//    qint32 entryTime;          // 采用UNIX时间，从2000年01月01日零时开始
//    quint32 operatorID;        // 操作员ID
//    quint8 vClass;             // 车型
//    quint8 cardStatus;         // 卡片状态
//    quint8 entryLaneType;      // 1MTC入口 2MTC出口 3ETC入口 4ETC出口
//    quint8 entryWasteSN;       // 入口流水序号
//    quint32 entryAxisType;     // 入口轴型
//    char vehiclePlate[16];     // 车牌号码
//    quint32 entryPSAMID;       // 入口PSAM ID
//    quint8 labelStations[9];   // 标识站点
//    quint16 checkBit;          // 校验位
    
//    TIC0012() { memset(this, 0, sizeof(TIC0012)); }
//};

//using TCardStru = TIC0012;

// IC0019复合交易记录
//struct TIC0019 {
//    quint8 complexLabel;       // 0XAA
//    quint8 fileLength;         // 0X29
//    quint8 lockedLabel;        // 0X00
//    quint16 entryNetwork;      // 3600
//    quint16 entryStation;      // 收费站编号去掉36后的整数
//    quint8 entryLane;
//    qint32 entryTime;          // 采用UNIX时间，从2000年01月01日零时开始
//    quint8 vClass;
//    quint8 cardStatus;         // 卡片状态
//    char vehiclePlate[14];
//    quint8 operatorID[3];
//    quint8 entryWasteSN;       // 入口流水序号
//    quint32 entryAxisType;
//    quint32 entryPSAMID;
//    quint8 spare;
//    quint16 checkBit;
//    quint8 spare2[20];         // 新增20字节长度
    
//    TIC0019() { memset(this, 0, sizeof(TIC0019)); }
//};

// 国标IC0019结构
//struct TIC0019GB {
//    quint8 complexLabel;       // 0XAA
//    quint8 fileLength;         // 0X29
//    quint8 lockedLabel;        // 0X00
//    quint16 entryNetwork;      // 3600
//    quint16 entryStation;      // 收费站编号
//    quint8 entryLane;
//    qint32 entryTime;          // 采用UNIX时间
//    quint8 vClass;
//    quint8 entryType;          // 01-封闭MTC入口，02-封闭MTC出口，03-封闭ETC入口等
//    quint8 etcNo[3];           // ETC门架编号
//    qint32 passTime;           // 通行门架时间
//    char vehiclePlate[12];     // 车牌号码
//    quint8 plateColor;         // 车牌颜色
//    quint8 aixNum;             // 车轴数
//    quint8 totalWeight[3];     // 固定值：0xBB 0xBB 0xBB
//    quint8 vehStatus;          // 车辆状态标识 默认0xFF
//    quint8 spare[4];
    
//    TIC0019GB() { memset(this, 0, sizeof(TIC0019GB)); }
//};

// 认证信息结构
//struct TAuthInfo {
//    quint8 psamChannel;        // PSAM通道
//    quint8 authStatus;         // 认证状态
//    quint8 authData[13];       // 认证数据
    
//    TAuthInfo() { memset(this, 0, sizeof(TAuthInfo)); }
//};

/**
 * OBU设备类型枚举
 */
enum class OBUDeviceType {
    SerialPort,    // 串口连接
    TCPClient,     // TCP客户端连接
    UDPClient      // UDP客户端连接
};

/**
 * OBU设备状态枚举
 */
enum class OBUDeviceStatus {
    Disconnected,  // 未连接
    Connecting,    // 连接中
    Connected,     // 已连接
    Error,         // 错误状态
    Busy           // 忙碌状态
};

/**
 * OBU设备配置结构
 */
struct OBUDeviceConfig {
    QString deviceId;              // 设备ID
    OBUDeviceType deviceType;      // 设备类型
    
    // 串口配置
    QString serialPortName;        // 串口名称
    int baudRate;                  // 波特率
    int dataBits;                  // 数据位
    int stopBits;                  // 停止位
    QString parity;                // 校验位
    
    // 网络配置
    QString serverIP;              // 服务器IP
    int serverPort;                // 服务器端口
    int timeoutMs;                 // 超时时间(毫秒)
    
    // 设备参数
    quint8 rsctl;                  // RSU控制字节
    QString terminalId;            // 终端ID
    bool autoReconnect;            // 自动重连
    int reconnectInterval;         // 重连间隔(毫秒)
    
    OBUDeviceConfig() 
        : deviceType(OBUDeviceType::SerialPort)
        , baudRate(115200)
        , dataBits(8)
        , stopBits(1)
        , parity("None")
        , serverPort(8000)
        , timeoutMs(5000)
        , rsctl(0)
        , autoReconnect(true)
        , reconnectInterval(5000)
    {}
};

/**
 * OBU设备主类
 * 对应原Delphi代码中的TOBUNew
 */
class OBUDevice : public QObject {
    Q_OBJECT

public:
    explicit OBUDevice(const OBUDeviceConfig& config, QObject* parent = nullptr);
    ~OBUDevice();

    // 设备管理
    bool openDevice();
    bool closeDevice();
    bool isConnected() const;
    OBUDeviceStatus getStatus() const;
    QString getStatusText() const;
    
    // 设备配置
    const OBUDeviceConfig& getConfig() const { return config; }
    void setConfig(const OBUDeviceConfig& newConfig);
    
    // RSU操作
    bool rsuReset();
    bool rsuHeart();
    bool getB0Info(quint8 rsctl);
    bool getB1Info(quint8 rsctl);
    
    // OBU操作
    bool getOBUID(quint8 rsctl);
    bool getB4Info(quint8 rsctl, quint32 obuId);
    bool getB5Info(quint8 rsctl, quint32 obuId);
    bool getOBUVehicleInfo(quint8 rsctl, quint32 obuId);
    bool getCPUInfo(quint8 rsctl, quint32 obuId);
    bool getCPUInfoNew(quint8 rsctl, quint32 obuId);
    
    // IC卡操作
    bool readIC0012(quint8 rsctl, quint32 obuId, TIC0012& ic0012);
    bool writeIC0012(quint8 rsctl, quint32 obuId, const TIC0012& ic0012);
    bool readIC0015(quint8 rsctl, quint32 obuId, TIC0015& ic0015);
    bool writeIC0015(quint8 rsctl, quint32 obuId, const TIC0015& ic0015);
    bool readIC0019(quint8 rsctl, quint32 obuId, TIC0019& ic0019);
    bool writeIC0019(quint8 rsctl, quint32 obuId, const TIC0019& ic0019);
    
    // 交易操作
    bool startTransaction(quint8 rsctl, quint32 obuId, quint32 fee);
    bool endTransaction(quint8 rsctl, quint32 obuId);
    bool cancelTransaction(quint8 rsctl, quint32 obuId);
    
    // PSAM操作
    bool psamInit(quint8 rsctl, quint8 psamNo);
    bool psamAuth(quint8 rsctl, quint8 psamNo, const QByteArray& authData);
    bool getPSAMInfo(quint8 rsctl, quint8 psamNo);
    
    // 数据发送
    bool sendCommand(const QByteArray& command);
    bool sendData(const char* data, int length);
    
    // 工具方法
    static quint16 calculateCRC16(const QByteArray& data);
    static QByteArray formatCommand(quint8 cmd, const QByteArray& data = QByteArray());
    static QString formatICCardInfo(const TIC0015& ic0015);
    static QString formatICCardInfo(const TIC0012& ic0012);

signals:
    // 连接状态信号
    void deviceConnected();
    void deviceDisconnected();
    void deviceError(const QString& error);
    void statusChanged(OBUDeviceStatus status);
    
    // RSU事件信号
    void rsuError(quint8 errorStatus);
    void rsuReset(quint8 rsctl, const QString& terminalId, quint8 algId, quint16 manuId, 
                  const QString& rsuId, quint16 version);
    
    // 数据获取信号
    void getB0(quint8 rsctl, const TB0Info& b0Info);
    void getB1(quint8 rsctl, const TB1Info& b1Info);
    void getOBUID(quint8 rsctl, quint32 obuId, const TB2Info& b2Info);
    void getB4(quint8 rsctl, quint32 obuId, const TB4Info& b4Info);
    void getB5(quint8 rsctl, quint32 obuId, const TB5Info& b5Info);
    void getOBUVehicle(quint8 rsctl, quint32 obuId, const QString& plate, quint8 plateCode,
                       int vehicleClass, quint8 userType, const TVClassConversion& conversion);
    void getCPUInfo(quint8 rsctl, quint32 obuId, quint32 remainMoney, 
                    const TIC0012& ic0012, const TIC0015& ic0015);
    void getCPUInfoNew(quint8 rsctl, quint32 obuId, quint8 obuType, quint8 antennaId,
                       const TB4Content0and1& content0and1, const TIC0019& ic0019,
                       const TB4Content1Station& station1, const TB4Content2& content2);
    
    // 交易信号
    void obuSessionEnd(quint8 rsctl, quint32 obuId, const QDateTime& sessionTime,
                       quint32 tac, quint16 payCardTranSN, quint32 psamTranSN,
                       const QString& rsuTerminalId, quint8 keyVersion, quint8 transType);
    void obuConsumed(quint8 rsctl, quint32 obuId, const QDateTime& purchaseTime,
                     quint32 iccid, quint32 tac, quint32 psamTranSN, quint8 keyVersion);
    void purTimeOut(quint8 rsctl, quint32 obuId, quint8 state, quint8 errorCode,
                    quint16 payCardTranSN, quint32 psamTranSN, const QString& rsuTerminalId,
                    quint8 transType);
    
    // OBU状态信号
    void obuError(quint8 rsctl, quint32 obuId, quint8 state, quint8 errorCode);
    void obuB7(quint8 rsctl, quint32 obuId, quint8 recordNum, const QString& recordList);
    void obuBA(quint8 rsctl, const TBAInfo& baInfo);
    void obuBB(quint8 rsctl, quint32 obuId, quint8 errorCode, const QVector<TAuthResult>& authResults);
    void obuD0(quint8 rsctl, quint32 obuId, quint8 errorCode, int x, int y);
    
    // PSAM信号
    void psamInit(quint8 rsctl, const QString& psamNo, quint8 psamVersion,
                  const QString& areaCode, const QString& randCode);
    void psamResult(quint8 rsctl, quint16 sw1sw2);

private slots:
    void onSerialDataReceived();
    void onSerialError();
    void onTcpDataReceived(const QByteArray& data);
    void onTcpConnected();
    void onTcpDisconnected();
    void onTcpError(const QString& error);
    void onUdpDataReceived();
    void onReconnectTimer();

private:
    void setupSerialPort();
    void setupTcpConnection();
    void setupUdpConnection();
    void processReceivedData(const QByteArray& data);
    void parseMessage(const QByteArray& message);
    void handleRSUMessage(quint8 cmd, const QByteArray& data);
    void handleOBUMessage(quint8 cmd, const QByteArray& data);
    void handlePSAMMessage(quint8 cmd, const QByteArray& data);
    void writeLog(const QString& message, int level=1);
    void setStatus(OBUDeviceStatus newStatus);
    
    OBUDeviceConfig config;
    OBUDeviceStatus status;
    
    // 通信接口
    QSerialPort* serialPort;
    DevTCPClient* tcpClient;
    QUdpSocket* udpSocket;
    
    // 数据缓冲
    QByteArray receiveBuffer;
    QMutex bufferMutex;
    
    // 定时器
    QTimer* reconnectTimer;
    QTimer* heartbeatTimer;
    
    // 统计信息
    qint64 bytesSent;
    qint64 bytesReceived;
    int commandCount;
    QDateTime lastActivityTime;
    
    static const int MAX_BUFFER_SIZE = 10240;
    static const int HEARTBEAT_INTERVAL = 30000;  // 30秒心跳间隔
};

/**
 * OBU设备工厂类
 */
class OBUDeviceFactory {
public:
    static std::shared_ptr<OBUDevice> createDevice(const OBUDeviceConfig& config);
    static OBUDeviceConfig createSerialConfig(const QString& portName, int baudRate = 115200);
    static OBUDeviceConfig createTcpConfig(const QString& serverIP, int port);
    static OBUDeviceConfig createUdpConfig(const QString& serverIP, int port);
    
private:
    static void validateConfig(const OBUDeviceConfig& config);
};

} // namespace ETC

Q_DECLARE_METATYPE(ETC::OBUDeviceStatus)
Q_DECLARE_METATYPE(ETC::TIC0015)
Q_DECLARE_METATYPE(ETC::TIC0012)
Q_DECLARE_METATYPE(ETC::TIC0019)

#endif // OBUDEVICE_H
