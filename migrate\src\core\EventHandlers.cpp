/**
 * @file EventHandlers.cpp
 * @brief RSU设备控制系统具体事件处理器实现
 */

#include "core/EventHandlers.h"
#include <QDebug>
#include <QDateTime>
#include <QDir>
#include <QStandardPaths>
#include <QTextStream>
#include <QFile>
#include <algorithm>

namespace ETC {

// EnhancedDeviceManagerHandler 实现
EnhancedDeviceManagerHandler::EnhancedDeviceManagerHandler(
    ShowInfoCallback showInfoCallback,
    WriteErrorLogCallback writeErrorLogCallback,
    UpdateDeviceStatusCallback updateDeviceStatusCallback,
    QObject* parent)
    : BaseEventHandler("增强设备管理处理器", 
                      {EventType::RSUError, EventType::RSUReset, EventType::RSUHeart}, 
                      EventPriority::High, parent)
    , maxRetryCount(3)
    , retryInterval(1000)
    , showInfoCallback(showInfoCallback)
    , writeErrorLogCallback(writeErrorLogCallback)
    , updateDeviceStatusCallback(updateDeviceStatusCallback)
{
}

EventResult EnhancedDeviceManagerHandler::doHandleEvent(std::shared_ptr<EventData> eventData) {
    switch (eventData->getEventType()) {
        case EventType::RSUError:
            return handleRSUError(std::dynamic_pointer_cast<RSUErrorEventData>(eventData));
        case EventType::RSUReset:
            return handleRSUReset(eventData);
        case EventType::RSUHeart:
            return handleRSUHeart(eventData);
        default:
            return EventResult::Ignored;
    }
}

EventResult EnhancedDeviceManagerHandler::handleRSUError(std::shared_ptr<RSUErrorEventData> eventData) {
    if (!eventData) {
        return EventResult::Failed;
    }

    try {
        // 构建错误信息 - 完全按照原始代码的格式
        QString errorMessage = QString("RSU初始化失败:0x%1").arg(eventData->getErrorStatus(), 2, 16, QChar('0'));
        
        // 调用原始代码的ShowInfo功能
        if (showInfoCallback) {
            showInfoCallback(errorMessage);
        }
        
        // 调用原始代码的WriteErrorLog功能
        if (writeErrorLogCallback) {
            writeErrorLogCallback(errorMessage);
        }
        
        // 设置设备状态为异常 - 对应原始代码的 RSUDevicesParams[0].RSUStatus := 0;
        if (updateDeviceStatusCallback) {
            updateDeviceStatusCallback(0, 0); // 设备0，状态0(异常)
        }
        
        // 记录详细的日志信息
        writeLog(QString("RSU设备错误详情 - 状态码: 0x%1").arg(eventData->getErrorStatus(), 2, 16, QChar('0')));
        
        // 根据错误严重程度决定处理策略
        if ((eventData->getErrorStatus() & 0xF0) > 0) {  // 严重错误
            writeLog("严重错误，需要人工干预");
            return EventResult::Failed;
        } else {
            writeLog("一般错误，尝试自动恢复");
            return EventResult::Retry;
        }
        
    } catch (const std::exception& e) {
        handleError("处理RSU错误事件时发生异常", e);
        return EventResult::Failed;
    }
}

EventResult EnhancedDeviceManagerHandler::handleRSUReset(std::shared_ptr<EventData> eventData) {
    try {
        writeLog("RSU设备重置完成");
        
        // 重置后恢复设备状态
        if (updateDeviceStatusCallback) {
            updateDeviceStatusCallback(0, 1); // 设备0，状态1(正常)
        }
        
        if (showInfoCallback) {
            showInfoCallback("RSU设备重置完成");
        }
        
        return EventResult::Success;
    } catch (const std::exception& e) {
        handleError("处理RSU重置事件时发生异常", e);
        return EventResult::Failed;
    }
}

EventResult EnhancedDeviceManagerHandler::handleRSUHeart(std::shared_ptr<EventData> eventData) {
    try {
        writeLog("收到RSU心跳信号", 2);  // 低级别日志
        
        // 更新心跳时间 - 这里可以扩展为更新LastHeartbeat
        // 心跳正常，确保设备状态为正常
        if (updateDeviceStatusCallback) {
            updateDeviceStatusCallback(0, 1); // 设备0，状态1(正常)
        }
        
        return EventResult::Success;
    } catch (const std::exception& e) {
        handleError("处理RSU心跳事件时发生异常", e);
        return EventResult::Failed;
    }
}

// EnhancedOBUErrorHandler 实现
EnhancedOBUErrorHandler::EnhancedOBUErrorHandler(ProcessOBUErrorCallback processOBUErrorCallback, QObject* parent)
    : BaseEventHandler("增强OBU错误处理器", {EventType::OBUError}, EventPriority::High, parent)
    , processOBUErrorCallback(processOBUErrorCallback)
{
}

EventResult EnhancedOBUErrorHandler::doHandleEvent(std::shared_ptr<EventData> eventData) {
    if (eventData->getEventType() == EventType::OBUError) {
        return handleOBUError(std::dynamic_pointer_cast<OBUErrorEventData>(eventData));
    }
    return EventResult::Ignored;
}

EventResult EnhancedOBUErrorHandler::handleOBUError(std::shared_ptr<OBUErrorEventData> eventData) {
    if (!eventData) {
        return EventResult::Failed;
    }

    try {
        writeLog("收到OBU错误事件，调用原始业务逻辑处理");
        
        writeLog(QString("OBU错误详情 - OBUID: %1, 状态: 0x%2, 错误码: 0x%3")
                .arg(eventData->getOBUID())
                .arg(eventData->getState(), 2, 16, QChar('0'))
                .arg(eventData->getErrorCode(), 2, 16, QChar('0')));
        
        // 调用原始业务逻辑处理
        if (processOBUErrorCallback) {
            processOBUErrorCallback(eventData->getRSCTL(), eventData->getOBUID(), 
                                  eventData->getState(), eventData->getErrorCode());
        }
        
        writeLog("OBU错误事件处理完成");
        return EventResult::Success;
        
    } catch (const std::exception& e) {
        handleError("处理OBU错误事件时发生异常", e);
        return EventResult::Failed;
    }
}

// EnhancedGetB0Handler 实现
EnhancedGetB0Handler::EnhancedGetB0Handler(ProcessGetB0Callback processGetB0Callback, QObject* parent)
    : BaseEventHandler("增强B0处理器", {EventType::GetB0}, EventPriority::High, parent)
    , processGetB0Callback(processGetB0Callback)
{
}

EventResult EnhancedGetB0Handler::doHandleEvent(std::shared_ptr<EventData> eventData) {
    if (eventData->getEventType() == EventType::GetB0) {
        return handleGetB0(std::dynamic_pointer_cast<GetB0EventData>(eventData));
    }
    return EventResult::Ignored;
}

EventResult EnhancedGetB0Handler::handleGetB0(std::shared_ptr<GetB0EventData> eventData) {
    if (!eventData) {
        return EventResult::Failed;
    }

    try {
        writeLog("收到B0事件，调用原始PSAM授权逻辑处理");
        
        writeLog(QString("B0事件详情 - RSCTL: %1, PSAM数量: %2, 错误码: %3")
                .arg(eventData->getRSCTL())
                .arg(eventData->getB0Info().psamNum1)
                .arg(eventData->getB0Info().errorCode));
        
        // 调用原始业务逻辑处理
        if (processGetB0Callback) {
            processGetB0Callback(eventData->getRSCTL(), eventData->getB0Info());
        }
        
        writeLog("B0事件处理完成");
        return EventResult::Success;
        
    } catch (const std::exception& e) {
        handleError("处理B0事件时发生异常", e);
        return EventResult::Failed;
    }
}

// EnhancedGetBAHandler 实现
EnhancedGetBAHandler::EnhancedGetBAHandler(ProcessGetBACallback processGetBACallback, QObject* parent)
    : BaseEventHandler("增强BA处理器", {EventType::OBUBA}, EventPriority::High, parent)
    , processGetBACallback(processGetBACallback)
{
}

EventResult EnhancedGetBAHandler::doHandleEvent(std::shared_ptr<EventData> eventData) {
    if (eventData->getEventType() == EventType::OBUBA) {
        return handleGetBA(std::dynamic_pointer_cast<GetBAEventData>(eventData));
    }
    return EventResult::Ignored;
}

EventResult EnhancedGetBAHandler::handleGetBA(std::shared_ptr<GetBAEventData> eventData) {
    if (!eventData) {
        return EventResult::Failed;
    }

    try {
        writeLog("收到BA事件，调用原始PSAM签到授权逻辑处理");
        
        writeLog(QString("BA事件详情 - RSCTL: %1, PSAM数量: %2")
                .arg(eventData->getRSCTL())
                .arg(eventData->getBAInfo().psamCount));
        
        // 调用原始业务逻辑处理
        if (processGetBACallback) {
            processGetBACallback(eventData->getRSCTL(), eventData->getBAInfo());
        }
        
        writeLog("BA事件处理完成");
        return EventResult::Success;
        
    } catch (const std::exception& e) {
        handleError("处理BA事件时发生异常", e);
        return EventResult::Failed;
    }
}

// EnhancedGetBBHandler 实现
EnhancedGetBBHandler::EnhancedGetBBHandler(ProcessGetBBCallback processGetBBCallback, QObject* parent)
    : BaseEventHandler("增强BB处理器", {EventType::OBUBB}, EventPriority::High, parent)
    , processGetBBCallback(processGetBBCallback)
{
}

EventResult EnhancedGetBBHandler::doHandleEvent(std::shared_ptr<EventData> eventData) {
    if (eventData->getEventType() == EventType::OBUBB) {
        return handleGetBB(std::dynamic_pointer_cast<GetBBEventData>(eventData));
    }
    return EventResult::Ignored;
}

EventResult EnhancedGetBBHandler::handleGetBB(std::shared_ptr<GetBBEventData> eventData) {
    if (!eventData) {
        return EventResult::Failed;
    }

    try {
        writeLog("收到BB事件，调用原始PSAM授权结果逻辑处理");
        
        writeLog(QString("BB事件详情 - RSCTL: %1, OBUID: %2, ErrorCode: %3, AuthResult数量: %4")
                .arg(eventData->getRSCTL())
                .arg(eventData->getOBUID())
                .arg(eventData->getErrorCode())
                .arg(eventData->getAuthResults().size()));
        
        // 调用原始业务逻辑处理
        if (processGetBBCallback) {
            processGetBBCallback(eventData->getRSCTL(), eventData->getOBUID(), 
                                eventData->getErrorCode(), eventData->getAuthResults());
        }
        
        writeLog("BB事件处理完成");
        return EventResult::Success;
        
    } catch (const std::exception& e) {
        handleError("处理BB事件时发生异常", e);
        return EventResult::Failed;
    }
}

// EnhancedGetB2Handler 实现
EnhancedGetB2Handler::EnhancedGetB2Handler(ProcessGetB2Callback processGetB2Callback, QObject* parent)
    : BaseEventHandler("增强B2处理器", {EventType::GetOBUID}, EventPriority::High, parent)
    , processGetB2Callback(processGetB2Callback)
{
}

EventResult EnhancedGetB2Handler::doHandleEvent(std::shared_ptr<EventData> eventData) {
    if (eventData->getEventType() == EventType::GetOBUID) {
        return handleGetB2(std::dynamic_pointer_cast<GetB2EventData>(eventData));
    }
    return EventResult::Ignored;
}

EventResult EnhancedGetB2Handler::handleGetB2(std::shared_ptr<GetB2EventData> eventData) {
    if (!eventData) {
        return EventResult::Failed;
    }

    try {
        writeLog("收到B2事件，调用原始OBU识别逻辑处理");
        
        writeLog(QString("B2事件详情 - RSCTL: %1, OBUID: 0x%2, 状态: 0x%3")
                .arg(eventData->getRSCTL())
                .arg(eventData->getOBUID(), 8, 16, QChar('0'))
                .arg(eventData->getB2Info().status, 2, 16, QChar('0')));
        
        // 调用原始业务逻辑处理
        if (processGetB2Callback) {
            processGetB2Callback(eventData->getRSCTL(), eventData->getOBUID(), eventData->getB2Info());
        }
        
        writeLog("B2事件处理完成");
        return EventResult::Success;
        
    } catch (const std::exception& e) {
        handleError("处理B2事件时发生异常", e);
        return EventResult::Failed;
    }
}

// DeviceManagerHandler 实现
DeviceManagerHandler::DeviceManagerHandler(QObject* parent)
    : BaseEventHandler("设备管理处理器", 
                      {EventType::RSUError, EventType::RSUReset, EventType::RSUHeart}, 
                      EventPriority::High, parent)
    , maxRetryCount(3)
    , retryInterval(1000)
{
}

EventResult DeviceManagerHandler::doHandleEvent(std::shared_ptr<EventData> eventData) {
    switch (eventData->getEventType()) {
        case EventType::RSUError:
            return handleRSUError(std::dynamic_pointer_cast<RSUErrorEventData>(eventData));
        case EventType::RSUReset:
            return handleRSUReset(eventData);
        case EventType::RSUHeart:
            return handleRSUHeart(eventData);
        default:
            return EventResult::Ignored;
    }
}

EventResult DeviceManagerHandler::handleRSUError(std::shared_ptr<RSUErrorEventData> eventData) {
    if (!eventData) {
        return EventResult::Failed;
    }

    try {
        QString errorMessage = QString("RSU初始化失败: 0x%1").arg(eventData->getErrorStatus(), 2, 16, QChar('0'));
        writeLog(QString("检测到RSU设备错误，状态码: 0x%1").arg(eventData->getErrorStatus(), 2, 16, QChar('0')));
        
        // 解析错误状态
        QStringList errorDesc;
        if ((eventData->getErrorStatus() & 0x01) > 0)
            errorDesc << "设备处在工作状态";
        if ((eventData->getErrorStatus() & 0x08) > 0)
            errorDesc << "IO状态变化";
        if ((eventData->getErrorStatus() & 0x10) > 0)
            errorDesc << "内存错误";
        if ((eventData->getErrorStatus() & 0x20) > 0)
            errorDesc << "SAM卡错误";
        if ((eventData->getErrorStatus() & 0x40) > 0)
            errorDesc << "发送模块错误";
        if ((eventData->getErrorStatus() & 0x80) > 0)
            errorDesc << "接收模块错误";
        
        writeLog(QString("错误详情: %1").arg(errorDesc.join("; ")));
        
        // 根据错误类型进行相应处理
        if ((eventData->getErrorStatus() & 0x10) > 0) {
            writeLog("检测到内存错误，尝试重启设备");
        } else if ((eventData->getErrorStatus() & 0x20) > 0) {
            writeLog("检测到SAM卡错误，尝试重新初始化");
        }
        
        // 根据错误严重程度决定重试策略
        if ((eventData->getErrorStatus() & 0xF0) > 0) {  // 严重错误
            writeLog("严重错误，需要人工干预");
            return EventResult::Failed;
        } else {
            writeLog("一般错误，尝试自动恢复");
            return EventResult::Retry;
        }
        
    } catch (const std::exception& e) {
        handleError("处理RSU错误事件时发生异常", e);
        return EventResult::Failed;
    }
}

EventResult DeviceManagerHandler::handleRSUReset(std::shared_ptr<EventData> eventData) {
    try {
        writeLog("RSU设备重置完成");
        return EventResult::Success;
    } catch (const std::exception& e) {
        handleError("处理RSU重置事件时发生异常", e);
        return EventResult::Failed;
    }
}

EventResult DeviceManagerHandler::handleRSUHeart(std::shared_ptr<EventData> eventData) {
    try {
        writeLog("收到RSU心跳信号", 2);  // 低级别日志
        return EventResult::Success;
    } catch (const std::exception& e) {
        handleError("处理RSU心跳事件时发生异常", e);
        return EventResult::Failed;
    }
}

// DataAcquisitionHandler 实现
DataAcquisitionHandler::DataAcquisitionHandler(QObject* parent)
    : BaseEventHandler("数据获取处理器", 
                      {EventType::GetB0, EventType::GetB1, EventType::GetOBUID, EventType::GetB4, 
                       EventType::GetB5, EventType::GetOBUVehicle, EventType::GetCPUInfo, EventType::GetCPUInfoNew}, 
                      EventPriority::Normal, parent)
    , cacheEnabled(true)
    , maxCacheSize(1000)
{
}

DataAcquisitionHandler::~DataAcquisitionHandler() {
}

EventResult DataAcquisitionHandler::doHandleEvent(std::shared_ptr<EventData> eventData) {
    switch (eventData->getEventType()) {
        case EventType::GetB0:
            return handleGetB0(std::dynamic_pointer_cast<GetB0EventData>(eventData));
        case EventType::GetOBUID:
            return handleGetOBUID(std::dynamic_pointer_cast<GetOBUIDEventData>(eventData));
        case EventType::GetB4:
            return handleGetB4(eventData);
        case EventType::GetB5:
            return handleGetB5(eventData);
        case EventType::GetOBUVehicle:
            return handleGetOBUVehicle(eventData);
        case EventType::GetCPUInfo:
        case EventType::GetCPUInfoNew:
            return handleGetCPUInfo(eventData);
        default:
            return EventResult::Ignored;
    }
}

EventResult DataAcquisitionHandler::handleGetB0(std::shared_ptr<GetB0EventData> eventData) {
    if (!eventData) {
        return EventResult::Failed;
    }

    try {
        writeLog(QString("获取到B0信息，RSCTL: %1, 错误码: %2")
                .arg(eventData->getRSCTL())
                .arg(eventData->getB0Info().errorCode));
        
        if (eventData->getB0Info().errorCode == 0) {
            // 格式化B0信息
            QString b0InfoStr = QString("PSAM数量: %1, RSU算法ID: %2, RSU厂商ID: %3, RSU版本: %4")
                               .arg(eventData->getB0Info().psamNum1)
                               .arg(eventData->getB0Info().rsuAlgId)
                               .arg(eventData->getB0Info().rsuManuID)
                               .arg(eventData->getB0Info().rsuVersion);
            
            writeLog(QString("B0详细信息: %1").arg(b0InfoStr));
            
            // 缓存数据
            if (cacheEnabled) {
                addToCache(QString("B0_%1").arg(eventData->getEventID()), b0InfoStr);
            }
            
            return EventResult::Success;
        } else {
            writeLog(QString("B0信息获取失败，错误码: %1").arg(eventData->getB0Info().errorCode));
            return EventResult::Failed;
        }
        
    } catch (const std::exception& e) {
        handleError("处理B0数据获取事件时发生异常", e);
        return EventResult::Failed;
    }
}

EventResult DataAcquisitionHandler::handleGetOBUID(std::shared_ptr<GetOBUIDEventData> eventData) {
    if (!eventData) {
        return EventResult::Failed;
    }

    try {
        writeLog(QString("获取到OBU ID信息，RSCTL: %1, OBUID: %2, 错误码: %3")
                .arg(eventData->getRSCTL())
                .arg(eventData->getOBUID())
                .arg(eventData->getB2Info().errorCode));
        
        if (eventData->getB2Info().errorCode == 0) {
            // 格式化OBU信息
            QString obuInfoStr = QString("天线ID: %1, 设备类型: %2, 合同类型: %3, 合同版本: %4, 设备状态: %5")
                                 .arg(eventData->getB2Info().antennaID)
                                 .arg(eventData->getB2Info().deviceType)
                                 .arg(eventData->getB2Info().contractType)
                                 .arg(eventData->getB2Info().contractVersion)
                                 .arg(eventData->getB2Info().status);
            
            writeLog(QString("OBU详细信息: %1").arg(obuInfoStr));
            writeLog(QString("发行日期: %1, 过期日期: %2")
                    .arg(eventData->getB2Info().dateofIssue)
                    .arg(eventData->getB2Info().dateofExpire));
            
            // 缓存数据
            if (cacheEnabled) {
                addToCache(QString("OBUID_%1").arg(eventData->getOBUID()), obuInfoStr);
            }
            
            return EventResult::Success;
        } else {
            writeLog(QString("OBU ID信息获取失败，错误码: %1").arg(eventData->getB2Info().errorCode));
            return EventResult::Failed;
        }
        
    } catch (const std::exception& e) {
        handleError("处理OBU ID获取事件时发生异常", e);
        return EventResult::Failed;
    }
}

EventResult DataAcquisitionHandler::handleGetB4(std::shared_ptr<EventData> eventData) {
    try {
        writeLog("处理B4数据获取事件");
        return EventResult::Success;
    } catch (const std::exception& e) {
        handleError("处理B4数据获取事件时发生异常", e);
        return EventResult::Failed;
    }
}

EventResult DataAcquisitionHandler::handleGetB5(std::shared_ptr<EventData> eventData) {
    try {
        writeLog("处理B5数据获取事件");
        return EventResult::Success;
    } catch (const std::exception& e) {
        handleError("处理B5数据获取事件时发生异常", e);
        return EventResult::Failed;
    }
}

EventResult DataAcquisitionHandler::handleGetOBUVehicle(std::shared_ptr<EventData> eventData) {
    try {
        writeLog("处理OBU车辆信息获取事件");
        return EventResult::Success;
    } catch (const std::exception& e) {
        handleError("处理OBU车辆信息获取事件时发生异常", e);
        return EventResult::Failed;
    }
}

EventResult DataAcquisitionHandler::handleGetCPUInfo(std::shared_ptr<EventData> eventData) {
    try {
        writeLog("处理CPU卡信息获取事件");
        return EventResult::Success;
    } catch (const std::exception& e) {
        handleError("处理CPU卡信息获取事件时发生异常", e);
        return EventResult::Failed;
    }
}

void DataAcquisitionHandler::addToCache(const QString& key, const QString& value) {
    if (!cacheEnabled) {
        return;
    }
    
    // 检查缓存大小限制
    if (dataCache.size() >= maxCacheSize) {
        clearCache();
    }
    
    dataCache.append(QString("%1=%2").arg(key).arg(value));
}

QString DataAcquisitionHandler::getFromCache(const QString& key) {
    if (!cacheEnabled) {
        return QString();
    }
    
    for (const QString& item : dataCache) {
        if (item.startsWith(key + "=")) {
            return item.mid(key.length() + 1);
        }
    }
    
    return QString();
}

void DataAcquisitionHandler::clearCache() {
    writeLog(QString("清理数据缓存，当前缓存项目数: %1").arg(dataCache.size()));
    dataCache.clear();
}

// TransactionHandler 实现
TransactionHandler::TransactionHandler(QObject* parent)
    : BaseEventHandler("交易处理器", 
                      {EventType::OBUSessionEnd, EventType::OBUConsumed, EventType::PurTimeOut}, 
                      EventPriority::High, parent)
    , autoSaveInterval(5)  // 5分钟
    , lastSaveTime(QDateTime::currentDateTime())
    , transactionCount(0)
{
    autoSaveTimer = new QTimer(this);
    autoSaveTimer->setInterval(autoSaveInterval * 60 * 1000);  // 转换为毫秒
    connect(autoSaveTimer, &QTimer::timeout, this, &TransactionHandler::checkAutoSave);
    autoSaveTimer->start();
}

TransactionHandler::~TransactionHandler() {
    saveTransactionLog();
}

EventResult TransactionHandler::doHandleEvent(std::shared_ptr<EventData> eventData) {
    switch (eventData->getEventType()) {
        case EventType::OBUSessionEnd:
            return handleOBUSessionEnd(eventData);
        case EventType::OBUConsumed:
            return handleOBUConsumed(eventData);
        case EventType::PurTimeOut:
            return handlePurTimeOut(eventData);
        default:
            return EventResult::Ignored;
    }
}

EventResult TransactionHandler::handleOBUSessionEnd(std::shared_ptr<EventData> eventData) {
    try {
        writeLog("处理OBU会话结束事件");
        
        QString transactionInfo = formatTransactionInfo(eventData);
        logTransaction(transactionInfo);
        
        transactionCount++;
        
        return EventResult::Success;
    } catch (const std::exception& e) {
        handleError("处理OBU会话结束事件时发生异常", e);
        return EventResult::Failed;
    }
}

EventResult TransactionHandler::handleOBUConsumed(std::shared_ptr<EventData> eventData) {
    try {
        writeLog("处理OBU消费事件");
        
        QString transactionInfo = formatTransactionInfo(eventData);
        logTransaction(transactionInfo);
        
        transactionCount++;
        
        return EventResult::Success;
    } catch (const std::exception& e) {
        handleError("处理OBU消费事件时发生异常", e);
        return EventResult::Failed;
    }
}

EventResult TransactionHandler::handlePurTimeOut(std::shared_ptr<EventData> eventData) {
    try {
        writeLog("处理购买超时事件");
        
        logTransaction(QString("购买超时事件 - 事件ID: %1, 时间: %2")
                      .arg(eventData->getEventID())
                      .arg(eventData->getTimeStamp().toString("yyyy-MM-dd hh:mm:ss")));
        
        return EventResult::Success;
    } catch (const std::exception& e) {
        handleError("处理购买超时事件时发生异常", e);
        return EventResult::Failed;
    }
}

void TransactionHandler::logTransaction(const QString& transactionInfo) {
    transactionLog.append(QString("[%1] %2")
                         .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"))
                         .arg(transactionInfo));
}

void TransactionHandler::saveTransactionLog() {
    if (transactionLog.isEmpty()) {
        return;
    }
    
    QString logFileName = QString("%1/transaction_log_%2.txt")
                         .arg(QStandardPaths::writableLocation(QStandardPaths::AppDataLocation))
                         .arg(QDate::currentDate().toString("yyyyMMdd"));
    
    QDir logDir = QFileInfo(logFileName).absoluteDir();
    if (!logDir.exists()) {
        logDir.mkpath(".");
    }
    
    QFile file(logFileName);
    if (file.open(QIODevice::WriteOnly | QIODevice::Append)) {
        QTextStream stream(&file);
        stream.setCodec("UTF-8");
        
        for (const QString& entry : transactionLog) {
            stream << entry << endl;
        }
        
        file.close();
        writeLog(QString("交易日志已保存到: %1").arg(logFileName));
        lastSaveTime = QDateTime::currentDateTime();
        transactionLog.clear();
    }
}

void TransactionHandler::saveLogNow() {
    saveTransactionLog();
}

void TransactionHandler::checkAutoSave() {
    QDateTime now = QDateTime::currentDateTime();
    if (lastSaveTime.secsTo(now) >= autoSaveInterval * 60) {
        saveTransactionLog();
    }
}

QString TransactionHandler::formatTransactionInfo(std::shared_ptr<EventData> eventData) {
    return QString("事件类型: %1, 事件ID: %2, 时间戳: %3")
           .arg(static_cast<int>(eventData->getEventType()))
           .arg(eventData->getEventID())
           .arg(eventData->getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// MonitoringHandler 实现
MonitoringHandler::MonitoringHandler(QObject* parent)
    : BaseEventHandler("监控统计处理器", 
                      {EventType::GetB0, EventType::GetB1, EventType::GetOBUID, 
                       EventType::GetB4, EventType::GetB5, EventType::GetOBUVehicle,
                       EventType::GetCPUInfo, EventType::GetCPUInfoNew, EventType::OBUSessionEnd,
                       EventType::OBUConsumed, EventType::OBUB7, EventType::OBUBA, 
                       EventType::OBUBB, EventType::OBUD0, EventType::PurTimeOut}, 
                      EventPriority::Low, parent)
    , monitoringEnabled(true)
    , reportInterval(60)  // 默认60分钟报告间隔
    , reportTimer(new QTimer(this))
{
    lastReportTime = QDateTime::currentDateTime();
    
    // 设置报告定时器
    reportTimer->setSingleShot(false);
    reportTimer->setInterval(reportInterval * 60 * 1000); // 转换为毫秒
    connect(reportTimer, &QTimer::timeout, this, &MonitoringHandler::checkReportGeneration);
    
    if (monitoringEnabled) {
        reportTimer->start();
    }
    
    writeLog("监控统计处理器初始化完成");
}

MonitoringHandler::~MonitoringHandler() {
    if (reportTimer) {
        reportTimer->stop();
    }
    writeLog("监控统计处理器已销毁");
}

EventResult MonitoringHandler::doHandleEvent(std::shared_ptr<EventData> eventData) {
    if (!monitoringEnabled) {
        return EventResult::Ignored;
    }
    
    try {
        updateStatistics(eventData);
        return EventResult::Success;
    } catch (const std::exception& e) {
        handleError("监控统计处理失败", e);
        return EventResult::Failed;
    }
}

void MonitoringHandler::checkReportGeneration() {
    QDateTime now = QDateTime::currentDateTime();
    qint64 elapsed = lastReportTime.secsTo(now);
    
    if (elapsed >= reportInterval * 60) {
        writeLog("生成定期监控报告");
        generatePerformanceReport();
        lastReportTime = now;
    }
}

void MonitoringHandler::updateStatistics(std::shared_ptr<EventData> eventData) {
    QString eventTypeStr = getEventStatistics(eventData->getEventType());
    QString timestamp = eventData->getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString priority = QString::number(static_cast<int>(eventData->getPriority()));
    
    QString statEntry = QString("[%1] 事件类型: %2, 优先级: %3, ID: %4")
                       .arg(timestamp)
                       .arg(eventTypeStr)
                       .arg(priority)
                       .arg(eventData->getEventID());
    
    statistics.append(statEntry);
    
    // 限制统计数据大小，保留最近的1000条记录
    if (statistics.size() > 1000) {
        statistics.removeFirst();
    }
}

void MonitoringHandler::generatePerformanceReport() {
    QDateTime now = QDateTime::currentDateTime();
    QString reportHeader = QString("=== ETC系统性能监控报告 ===\n")
                          + QString("报告生成时间: %1\n").arg(now.toString("yyyy-MM-dd hh:mm:ss"))
                          + QString("统计数据条数: %1\n").arg(statistics.size())
                          + QString("监控间隔: %1分钟\n\n").arg(reportInterval);
    
    QString reportContent = reportHeader;
    
    // 添加最近的统计信息
    int recentCount = qMin(50, statistics.size());
    if (recentCount > 0) {
        reportContent += "最近事件统计:\n";
        for (int i = statistics.size() - recentCount; i < statistics.size(); ++i) {
            reportContent += statistics[i] + "\n";
        }
    }
    
    // 写入日志
    writeLog("监控报告已生成");
    writeLog(reportContent);
}

QString MonitoringHandler::getEventStatistics(EventType eventType) {
    switch (eventType) {
        case EventType::GetB0: return "GetB0(RSU状态)";
        case EventType::GetB1: return "GetB1(心跳响应)";
        case EventType::GetOBUID: return "GetOBUID(获取OBU ID)";
        case EventType::GetB4: return "GetB4(IC卡属性)";
        case EventType::GetB5: return "GetB5(交易结果)";
        case EventType::GetOBUVehicle: return "GetOBUVehicle(车辆信息)";
        case EventType::GetCPUInfo: return "GetCPUInfo(CPU卡信息)";
        case EventType::GetCPUInfoNew: return "GetCPUInfoNew(新版CPU卡信息)";
        case EventType::OBUSessionEnd: return "OBUSessionEnd(会话结束)";
        case EventType::OBUConsumed: return "OBUConsumed(OBU消费)";
        case EventType::OBUB7: return "OBUB7(历史交易)";
        case EventType::OBUBA: return "OBUBA(PSAM签到)";
        case EventType::OBUBB: return "OBUBB(PSAM授权结果)";
        case EventType::OBUD0: return "OBUD0(坐标信息)";
        case EventType::PurTimeOut: return "PurTimeOut(购买超时)";
        default: return "Unknown";
    }
}

void MonitoringHandler::resetStatistics() {
    statistics.clear();
    lastReportTime = QDateTime::currentDateTime();
    writeLog("监控统计数据已重置");
}

void MonitoringHandler::saveStatisticsReport(const QString& reportFile) {
    QFile file(reportFile);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        writeLog(QString("无法创建统计报告文件: %1").arg(reportFile));
        return;
    }
    
    QTextStream out(&file);
    out.setCodec("UTF-8");
    
    QDateTime now = QDateTime::currentDateTime();
    out << "=== ETC系统监控统计报告 ===\n";
    out << QString("生成时间: %1\n").arg(now.toString("yyyy-MM-dd hh:mm:ss"));
    out << QString("统计条目数: %1\n").arg(statistics.size());
    out << QString("监控状态: %1\n").arg(monitoringEnabled ? "启用" : "禁用");
    out << QString("报告间隔: %1分钟\n\n").arg(reportInterval);
    
    out << "详细统计数据:\n";
    for (const QString& entry : statistics) {
        out << entry << "\n";
    }
    
    file.close();
    writeLog(QString("统计报告已保存: %1").arg(reportFile));
}

// ErrorHandler 实现
ErrorHandler::ErrorHandler(QObject* parent)
    : BaseEventHandler("错误处理器", {EventType::OBUError}, EventPriority::High, parent)
    , errorCount(0)
    , maxErrorLogSize(1000)
{
}

ErrorHandler::~ErrorHandler() {
}

EventResult ErrorHandler::doHandleEvent(std::shared_ptr<EventData> eventData) {
    if (eventData->getEventType() == EventType::OBUError) {
        return handleOBUError(std::dynamic_pointer_cast<OBUErrorEventData>(eventData));
    }
    return EventResult::Ignored;
}

EventResult ErrorHandler::handleOBUError(std::shared_ptr<OBUErrorEventData> eventData) {
    if (!eventData) {
        return EventResult::Failed;
    }

    try {
        QString errorInfo = QString("OBU错误 - OBUID: %1, 状态: 0x%2, 错误码: 0x%3")
                           .arg(eventData->getOBUID())
                           .arg(eventData->getState(), 2, 16, QChar('0'))
                           .arg(eventData->getErrorCode(), 2, 16, QChar('0'));
        
        logError(errorInfo);
        updateErrorStatistics("OBU错误");
        
        writeLog(errorInfo);
        
        return EventResult::Success;
        
    } catch (const std::exception& e) {
        handleError("处理OBU错误事件时发生异常", e);
        return EventResult::Failed;
    }
}

void ErrorHandler::logError(const QString& errorInfo) {
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    QString logEntry = QString("[%1] %2").arg(timestamp).arg(errorInfo);
    
    errorLog.append(logEntry);
    errorCount++;
    
    // 限制错误日志大小
    if (errorLog.size() > maxErrorLogSize) {
        errorLog.removeFirst();
    }
}

void ErrorHandler::updateErrorStatistics(const QString& errorType) {
    QString statEntry = QString("%1: %2").arg(errorType).arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    errorStatistics.append(statEntry);
    
    // 限制统计数据大小
    if (errorStatistics.size() > maxErrorLogSize) {
        errorStatistics.removeFirst();
    }
}

QString ErrorHandler::getErrorRecoveryAction(std::shared_ptr<EventData> eventData) {
    Q_UNUSED(eventData)
    return QString("自动重试");
}

void ErrorHandler::generateErrorReport(const QString& reportFile) {
    QFile file(reportFile);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        writeLog(QString("无法创建错误报告文件: %1").arg(reportFile));
        return;
    }
    
    QTextStream out(&file);
    out.setCodec("UTF-8");
    
    QDateTime now = QDateTime::currentDateTime();
    out << "=== ETC系统错误报告 ===\n";
    out << QString("生成时间: %1\n").arg(now.toString("yyyy-MM-dd hh:mm:ss"));
    out << QString("错误总数: %1\n").arg(errorCount);
    out << QString("错误日志条目数: %1\n\n").arg(errorLog.size());
    
    out << "错误日志:\n";
    for (const QString& entry : errorLog) {
        out << entry << "\n";
    }
    
    out << "\n错误统计:\n";
    for (const QString& stat : errorStatistics) {
        out << stat << "\n";
    }
    
    file.close();
    writeLog(QString("错误报告已保存: %1").arg(reportFile));
}

// AuthenticationHandler 实现
AuthenticationHandler::AuthenticationHandler(QObject* parent)
    : BaseEventHandler("认证管理处理器", {EventType::GetB0, EventType::OBUBA, EventType::OBUBB}, 
                      EventPriority::High, parent)
    , securityLevel(1)
    , maxFailedAttempts(3)
    , failedAttempts(0)
{
}

AuthenticationHandler::~AuthenticationHandler() {
}

EventResult AuthenticationHandler::doHandleEvent(std::shared_ptr<EventData> eventData) {
    switch (eventData->getEventType()) {
        case EventType::GetB0:
            return handlePSAMInit(eventData);
        case EventType::OBUBA:
        case EventType::OBUBB:
            return handlePSAMResult(eventData);
        default:
            return EventResult::Ignored;
    }
}

EventResult AuthenticationHandler::handlePSAMInit(std::shared_ptr<EventData> eventData) {
    if (!eventData) {
        return EventResult::Failed;
    }

    try {
        writeLog("开始处理PSAM初始化认证事件");
        
        // 验证安全级别
        if (!validateSecurityLevel(eventData)) {
            writeLog("安全级别验证失败");
            handleFailedAuthentication();
            return EventResult::Failed;
        }
        
        QString authInfo = QString("PSAM初始化认证 - 事件ID: %1, 时间: %2, 安全级别: %3")
                          .arg(eventData->getEventID())
                          .arg(eventData->getTimeStamp().toString("yyyy-MM-dd hh:mm:ss"))
                          .arg(securityLevel);
        
        logAuthEvent(authInfo);
        
        writeLog("PSAM初始化认证成功");
        failedAttempts = 0;  // 重置失败计数
        
        return EventResult::Success;
        
    } catch (const std::exception& e) {
        handleError("处理PSAM初始化认证事件时发生异常", e);
        handleFailedAuthentication();
        return EventResult::Failed;
    }
}

EventResult AuthenticationHandler::handlePSAMResult(std::shared_ptr<EventData> eventData) {
    if (!eventData) {
        return EventResult::Failed;
    }

    try {
        QString eventTypeName = (eventData->getEventType() == EventType::OBUBA) ? "PSAM签到" : "PSAM授权结果";
        writeLog(QString("开始处理%1认证事件").arg(eventTypeName));
        
        // 验证安全级别
        if (!validateSecurityLevel(eventData)) {
            writeLog(QString("%1安全级别验证失败").arg(eventTypeName));
            handleFailedAuthentication();
            return EventResult::Failed;
        }
        
        QString authInfo = QString("%1认证 - 事件ID: %2, 时间: %3, 优先级: %4")
                          .arg(eventTypeName)
                          .arg(eventData->getEventID())
                          .arg(eventData->getTimeStamp().toString("yyyy-MM-dd hh:mm:ss"))
                          .arg(static_cast<int>(eventData->getPriority()));
        
        logAuthEvent(authInfo);
        
        writeLog(QString("%1认证成功").arg(eventTypeName));
        failedAttempts = 0;  // 重置失败计数
        
        return EventResult::Success;
        
    } catch (const std::exception& e) {
        handleError("处理PSAM认证事件时发生异常", e);
        handleFailedAuthentication();
        return EventResult::Failed;
    }
}

void AuthenticationHandler::logAuthEvent(const QString& authInfo) {
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString logEntry = QString("[%1] %2").arg(timestamp).arg(authInfo);
    
    authLog.append(logEntry);
    
    // 限制认证日志大小，保留最近的500条记录
    if (authLog.size() > 500) {
        authLog.removeFirst();
    }
    
    writeLog(QString("认证事件已记录: %1").arg(authInfo));
}

bool AuthenticationHandler::validateSecurityLevel(std::shared_ptr<EventData> eventData) {
    Q_UNUSED(eventData)
    
    // 基本的安全级别验证逻辑
    if (securityLevel < 1 || securityLevel > 5) {
        writeLog(QString("无效的安全级别: %1").arg(securityLevel));
        return false;
    }
    
    // 这里可以根据具体的安全要求添加更复杂的验证逻辑
    writeLog(QString("安全级别验证通过: %1").arg(securityLevel));
    return true;
}

void AuthenticationHandler::handleFailedAuthentication() {
    failedAttempts++;
    
    QString failureInfo = QString("认证失败 - 当前失败次数: %1/%2")
                         .arg(failedAttempts)
                         .arg(maxFailedAttempts);
    
    writeLog(failureInfo);
    logAuthEvent(failureInfo);
    
    if (failedAttempts >= maxFailedAttempts) {
        writeLog("认证失败次数超过限制，需要人工干预");
        // 这里可以添加额外的安全措施，如发送警报、锁定系统等
    }
}

}// namespace ETC
