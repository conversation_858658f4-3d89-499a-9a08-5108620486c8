/**
 * @file EventFramework.cpp
 * @brief RSU设备控制系统事件处理框架实现
 */

#include "core/EventFramework.h"
#include <QDebug>
#include <QDir>
#include <QStandardPaths>
#include <QCoreApplication>
#include <QMutexLocker>
#include <algorithm>

namespace ETC {

// 静态成员初始化
EventManager* EventManager::instance = nullptr;
QMutex EventManager::instanceMutex;

// EventData 实现
EventData::EventData(EventType type)
    : eventType(type)
    , timeStamp(QDateTime::currentDateTime())
    , priority(EventPriority::Normal)
{
    // 生成唯一的事件ID
    eventID = QString("%1_%2").arg(static_cast<int>(type)).arg(timeStamp.toMSecsSinceEpoch());
}

QString EventData::toString() const {
    return QString("Event[ID:%1, Type:%2, Time:%3, Priority:%4]")
           .arg(eventID)
           .arg(static_cast<int>(eventType))
           .arg(timeStamp.toString("yyyy-MM-dd hh:mm:ss.zzz"))
           .arg(static_cast<int>(priority));
}

// RSUErrorEventData 实现
RSUErrorEventData::RSUErrorEventData(uint8_t errorStatus)
    : EventData(EventType::RSUError)
    , errorStatus(errorStatus)
{
    setPriority(EventPriority::High);
}

QString RSUErrorEventData::toString() const {
    return QString("RSUErrorEvent[ID:%1, ErrorStatus:0x%2, Time:%3]")
           .arg(getEventID())
           .arg(errorStatus, 2, 16, QChar('0'))
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// GetB0EventData 实现
GetB0EventData::GetB0EventData(uint8_t rsctl, const TB0Info& b0Info)
    : EventData(EventType::GetB0)
    , rsctl(rsctl)
    , b0Info(b0Info)
{
    setPriority(EventPriority::Normal);
}

QString GetB0EventData::toString() const {
    return QString("GetB0Event[ID:%1, RSCTL:%2, ErrorCode:%3, Time:%4]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(b0Info.errorCode)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// GetOBUIDEventData 实现
GetOBUIDEventData::GetOBUIDEventData(uint8_t rsctl, uint32_t obuid, const TB2Info& b2Info)
    : EventData(EventType::GetOBUID)
    , rsctl(rsctl)
    , obuid(obuid)
    , b2Info(b2Info)
{
    setPriority(EventPriority::Normal);
}

QString GetOBUIDEventData::toString() const {
    return QString("GetOBUIDEvent[ID:%1, RSCTL:%2, OBUID:0x%3, DeviceType:%4, Time:%5]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(b2Info.deviceType)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// OBUErrorEventData 实现
OBUErrorEventData::OBUErrorEventData(uint8_t rsctl, uint32_t obuid, uint8_t state, uint8_t errorCode)
    : EventData(EventType::OBUError)
    , rsctl(rsctl)
    , obuid(obuid)
    , state(state)
    , errorCode(errorCode)
{
    setPriority(EventPriority::High);
}

QString OBUErrorEventData::toString() const {
    return QString("OBUErrorEvent[ID:%1, RSCTL:%2, OBUID:0x%3, State:0x%4, ErrorCode:0x%5, Time:%6]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(state, 2, 16, QChar('0'))
           .arg(errorCode, 2, 16, QChar('0'))
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// GetBAEventData 实现
GetBAEventData::GetBAEventData(uint8_t rsctl, const TBAInfo& baInfo)
    : EventData(EventType::OBUBA)
    , rsctl(rsctl)
    , baInfo(baInfo)
{
    setPriority(EventPriority::Normal);
}

QString GetBAEventData::toString() const {
    return QString("GetBAEvent[ID:%1, RSCTL:%2, PSAMCount:%3, Time:%4]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(baInfo.psamCount)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// GetBBEventData 实现
GetBBEventData::GetBBEventData(uint8_t rsctl, uint32_t obuid, uint8_t errorCode, const QVector<TAuthResult>& authResults)
    : EventData(EventType::OBUBB)
    , rsctl(rsctl)
    , obuid(obuid)
    , errorCode(errorCode)
    , authResults(authResults)
{
    setPriority(EventPriority::Normal);
}

QString GetBBEventData::toString() const {
    return QString("GetBBEvent[ID:%1, RSCTL:%2, OBUID:0x%3, ErrorCode:%4, AuthCount:%5, Time:%6]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(errorCode)
           .arg(authResults.size())
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// GetB1EventData 实现
GetB1EventData::GetB1EventData(uint8_t rsctl, const TB1Info& b1Info)
    : EventData(EventType::GetB1)
    , rsctl(rsctl)
    , b1Info(b1Info)
{
    setPriority(EventPriority::Normal);
}

QString GetB1EventData::toString() const {
    return QString("GetB1Event[ID:%1, RSCTL:%2, RSUControlStatus1:%3, PSAMNum1:%4, Time:%5]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(b1Info.rsuControlStatus1)
           .arg(b1Info.psamNum1)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// GetB2EventData 实现
GetB2EventData::GetB2EventData(uint8_t rsctl, uint32_t obuid, const TB2Info& b2Info)
    : EventData(EventType::GetOBUID)
    , rsctl(rsctl)
    , obuid(obuid)
    , b2Info(b2Info)
{
    setPriority(EventPriority::Normal);
}

QString GetB2EventData::toString() const {
    return QString("GetB2Event[ID:%1, RSCTL:%2, OBUID:0x%3, DeviceType:%4, Status:0x%5, Time:%6]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(b2Info.deviceType)
           .arg(b2Info.status, 4, 16, QChar('0'))
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// GetB4EventData 实现
GetB4EventData::GetB4EventData(uint8_t rsctl, uint32_t obuid, const TB4Info& b4Info)
    : EventData(EventType::GetB4)
    , rsctl(rsctl)
    , obuid(obuid)
    , b4Info(b4Info)
{
    setPriority(EventPriority::Normal);
}

QString GetB4EventData::toString() const {
    return QString("GetB4Event[ID:%1, RSCTL:%2, OBUID:0x%3, Time:%4]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// GetB5EventData 实现
GetB5EventData::GetB5EventData(uint8_t rsctl, uint32_t obuid, const TB5Info& b5Info)
    : EventData(EventType::GetB5)
    , rsctl(rsctl)
    , obuid(obuid)
    , b5Info(b5Info)
{
    setPriority(EventPriority::Normal);
}

QString GetB5EventData::toString() const {
    return QString("GetB5Event[ID:%1, RSCTL:%2, OBUID:0x%3, Time:%4]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// GetOBUVehicleEventData 实现
GetOBUVehicleEventData::GetOBUVehicleEventData(uint8_t rsctl, uint32_t obuid, const QString& plate, 
                      uint8_t plateCode, int vehicleClass, uint8_t userType, 
                      const TVClassConversion& conversion)
    : EventData(EventType::GetOBUVehicle)
    , rsctl(rsctl)
    , obuid(obuid)
    , plate(plate)
    , plateCode(plateCode)
    , vehicleClass(vehicleClass)
    , userType(userType)
    , conversion(conversion)
{
    setPriority(EventPriority::Normal);
}

QString GetOBUVehicleEventData::toString() const {
    return QString("GetOBUVehicleEvent[ID:%1, RSCTL:%2, OBUID:0x%3, Plate:%4, VehicleClass:%5, Time:%6]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(plate)
           .arg(vehicleClass)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// GetCPUInfoEventData 实现
GetCPUInfoEventData::GetCPUInfoEventData(uint8_t rsctl, uint32_t obuid, uint32_t remainMoney, 
                   const TIC0012& ic0012, const TIC0015& ic0015)
    : EventData(EventType::GetCPUInfo)
    , rsctl(rsctl)
    , obuid(obuid)
    , remainMoney(remainMoney)
    , ic0012(ic0012)
    , ic0015(ic0015)
{
    setPriority(EventPriority::Normal);
}

QString GetCPUInfoEventData::toString() const {
    return QString("GetCPUInfoEvent[ID:%1, RSCTL:%2, OBUID:0x%3, RemainMoney:%4, Time:%5]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(remainMoney)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// GetCPUInfoNewEventData 实现
GetCPUInfoNewEventData::GetCPUInfoNewEventData(uint8_t rsctl, uint32_t obuid, uint8_t obuType, uint8_t antennaId,
                          const TB4Content0and1& content0and1, const TIC0019& ic0019,
                          const TB4Content1Station& station1, const TB4Content2& content2)
    : EventData(EventType::GetCPUInfoNew)
    , rsctl(rsctl)
    , obuid(obuid)
    , obuType(obuType)
    , antennaId(antennaId)
    , content0and1(content0and1)
    , ic0019(ic0019)
    , station1(station1)
    , content2(content2)
{
    setPriority(EventPriority::Normal);
}

QString GetCPUInfoNewEventData::toString() const {
    return QString("GetCPUInfoNewEvent[ID:%1, RSCTL:%2, OBUID:0x%3, OBUType:%4, AntennaId:%5, Time:%6]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(obuType)
           .arg(antennaId)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// OBUSessionEndEventData 实现
OBUSessionEndEventData::OBUSessionEndEventData(uint8_t rsctl, uint32_t obuid, const QDateTime& sessionTime,
                         uint32_t tac, uint16_t payCardTranSN, uint32_t psamTranSN,
                         const QString& rsuTerminalId, uint8_t keyVersion, uint8_t transType)
    : EventData(EventType::OBUSessionEnd)
    , rsctl(rsctl)
    , obuid(obuid)
    , sessionTime(sessionTime)
    , tac(tac)
    , payCardTranSN(payCardTranSN)
    , psamTranSN(psamTranSN)
    , rsuTerminalId(rsuTerminalId)
    , keyVersion(keyVersion)
    , transType(transType)
{
    setPriority(EventPriority::High);
}

QString OBUSessionEndEventData::toString() const {
    return QString("OBUSessionEndEvent[ID:%1, RSCTL:%2, OBUID:0x%3, TAC:%4, Time:%5]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(tac)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// OBUConsumedEventData 实现
OBUConsumedEventData::OBUConsumedEventData(uint8_t rsctl, uint32_t obuid, const QDateTime& purchaseTime,
                     uint32_t iccid, uint32_t tac, uint32_t psamTranSN, uint8_t keyVersion)
    : EventData(EventType::OBUConsumed)
    , rsctl(rsctl)
    , obuid(obuid)
    , purchaseTime(purchaseTime)
    , iccid(iccid)
    , tac(tac)
    , psamTranSN(psamTranSN)
    , keyVersion(keyVersion)
{
    setPriority(EventPriority::High);
}

QString OBUConsumedEventData::toString() const {
    return QString("OBUConsumedEvent[ID:%1, RSCTL:%2, OBUID:0x%3, ICCID:%4, TAC:%5, Time:%6]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(iccid)
           .arg(tac)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// OBUB7EventData 实现
OBUB7EventData::OBUB7EventData(uint8_t rsctl, uint32_t obuid, uint8_t recordNum, const QString& recordList)
    : EventData(EventType::OBUB7)
    , rsctl(rsctl)
    , obuid(obuid)
    , recordNum(recordNum)
    , recordList(recordList)
{
    setPriority(EventPriority::Normal);
}

QString OBUB7EventData::toString() const {
    return QString("OBUB7Event[ID:%1, RSCTL:%2, OBUID:0x%3, RecordNum:%4, Time:%5]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(recordNum)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// OBUD0EventData 实现
OBUD0EventData::OBUD0EventData(uint8_t rsctl, uint32_t obuid, uint8_t errorCode, int x, int y)
    : EventData(EventType::OBUD0)
    , rsctl(rsctl)
    , obuid(obuid)
    , errorCode(errorCode)
    , x(x)
    , y(y)
{
    setPriority(EventPriority::High);
}

QString OBUD0EventData::toString() const {
    return QString("OBUD0Event[ID:%1, RSCTL:%2, OBUID:0x%3, ErrorCode:%4, X:%5, Y:%6, Time:%7]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(errorCode)
           .arg(x)
           .arg(y)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// RSUResetEventData 实现
RSUResetEventData::RSUResetEventData(uint8_t rsctl, const QString& terminalId, uint8_t algId, 
                 uint16_t manuId, const QString& rsuId, uint16_t version)
    : EventData(EventType::RSUReset)
    , rsctl(rsctl)
    , terminalId(terminalId)
    , algId(algId)
    , manuId(manuId)
    , rsuId(rsuId)
    , version(version)
{
    setPriority(EventPriority::Normal);
}

QString RSUResetEventData::toString() const {
    return QString("RSUResetEvent[ID:%1, RSCTL:%2, TerminalId:%3, RSUId:%4, Version:%5, Time:%6]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(terminalId)
           .arg(rsuId)
           .arg(version)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// PurTimeOutEventData 实现
PurTimeOutEventData::PurTimeOutEventData(uint8_t rsctl, uint32_t obuid, uint8_t state, uint8_t errorCode,
                       uint16_t payCardTranSN, uint32_t psamTranSN, 
                       const QString& rsuTerminalId, uint8_t transType)
    : EventData(EventType::PurTimeOut)
    , rsctl(rsctl)
    , obuid(obuid)
    , state(state)
    , errorCode(errorCode)
    , payCardTranSN(payCardTranSN)
    , psamTranSN(psamTranSN)
    , rsuTerminalId(rsuTerminalId)
    , transType(transType)
{
    setPriority(EventPriority::High);
}

QString PurTimeOutEventData::toString() const {
    return QString("PurTimeOutEvent[ID:%1, RSCTL:%2, OBUID:0x%3, State:%4, ErrorCode:%5, PayCardTranSN:%6, PSAMTranSN:%7, TerminalId:%8, TransType:%9, Time:%10]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(obuid, 8, 16, QChar('0'))
           .arg(state)
           .arg(errorCode)
           .arg(payCardTranSN)
           .arg(psamTranSN)
           .arg(rsuTerminalId)
           .arg(transType)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// PSAMInitEventData 实现
PSAMInitEventData::PSAMInitEventData(uint8_t rsctl, const QString& psamNo, uint8_t psamVersion,
                 const QString& areaCode, const QString& randCode)
    : EventData(EventType::PSAMInit)
    , rsctl(rsctl)
    , psamNo(psamNo)
    , psamVersion(psamVersion)
    , areaCode(areaCode)
    , randCode(randCode)
{
    setPriority(EventPriority::Normal);
}

QString PSAMInitEventData::toString() const {
    return QString("PSAMInitEvent[ID:%1, RSCTL:%2, PSAMNo:%3, Version:%4, AreaCode:%5, Time:%6]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(psamNo)
           .arg(psamVersion)
           .arg(areaCode)
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// PSAMResultEventData 实现
PSAMResultEventData::PSAMResultEventData(uint8_t rsctl, uint16_t sw1sw2)
    : EventData(EventType::PSAMResult)
    , rsctl(rsctl)
    , sw1sw2(sw1sw2)
{
    setPriority(EventPriority::Normal);
}

QString PSAMResultEventData::toString() const {
    return QString("PSAMResultEvent[ID:%1, RSCTL:%2, SW1SW2:0x%3, Time:%4]")
           .arg(getEventID())
           .arg(rsctl)
           .arg(sw1sw2, 4, 16, QChar('0'))
           .arg(getTimeStamp().toString("yyyy-MM-dd hh:mm:ss.zzz"));
}

// BaseEventHandler 实现
BaseEventHandler::BaseEventHandler(const QString& name, const QVector<EventType>& supportedEvents, 
                                   EventPriority priority, QObject* parent)
    : QObject(parent)
    , handlerName(name)
    , supportedEventTypes(supportedEvents)
    , handlerPriority(priority)
    , isEnabled(true)
    , processedCount(0)
    , successCount(0)
    , failedCount(0)
{
}

bool BaseEventHandler::canHandle(EventType eventType) const {
    return supportedEventTypes.contains(eventType);
}

EventResult BaseEventHandler::handleEvent(std::shared_ptr<EventData> eventData) {
    if (!isEnabled) {
        return EventResult::Ignored;
    }
    
    if (!canHandle(eventData->getEventType())) {
        return EventResult::Ignored;
    }
    
    processedCount++;
    lastProcessTime = QDateTime::currentDateTime();
    
    try {
        EventResult result = doHandleEvent(eventData);
        
        if (result == EventResult::Success) {
            successCount++;
        } else {
            failedCount++;
        }
        
        return result;
    }
    catch (const std::exception& e) {
        failedCount++;
        handleError(QString("处理事件时发生异常"), e);
        return EventResult::Failed;
    }
}

void BaseEventHandler::resetStatistics() {
    processedCount = 0;
    successCount = 0;
    failedCount = 0;
    lastProcessTime = QDateTime();
}

void BaseEventHandler::writeLog(const QString& message, int level) {
    QString logMessage = QString("[%1] %2: %3")
                        .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                        .arg(handlerName)
                        .arg(message);
    
    if (level >= 1) {  // 错误级别
        qCritical() << logMessage;
    } else {
        qDebug() << logMessage;
    }
    
    // 通过事件管理器记录日志
    EventManager::getInstance()->writeLog(logMessage, level);
}

void BaseEventHandler::handleError(const QString& errorMessage, const std::exception& e) {
    QString fullMessage = QString("%1: %2").arg(errorMessage).arg(e.what());
    writeLog(fullMessage, 1);
}

// EventManager 实现
EventManager::EventManager(QObject* parent)
    : QObject(parent)
    , maxQueueSize(10000)
    , maxRetryCount(3)
    , retryInterval(1000)
    , logFile(nullptr)
    , logStream(nullptr)
    , logLevel(0)
    , performanceMonitoringEnabled(true)
    , totalEventsProcessed(0)
    , totalEventsSucceeded(0)
    , totalEventsFailed(0)
    , running(false)
{
    processTimer = new QTimer(this);
    processTimer->setInterval(10); // 10ms处理间隔
    connect(processTimer, &QTimer::timeout, this, &EventManager::processEventQueue);
    
    retryTimer = new QTimer(this);
    retryTimer->setInterval(1000); // 1秒检查重试
    connect(retryTimer, &QTimer::timeout, this, &EventManager::handleRetryTimer);
    
    initializeLog();
}

EventManager::~EventManager() {
    stop();
    cleanupLog();
}

EventManager* EventManager::getInstance() {
    QMutexLocker locker(&instanceMutex);
    if (!instance) {
        instance = new EventManager();
    }
    return instance;
}

void EventManager::registerHandler(std::shared_ptr<BaseEventHandler> handler) {
    if (!handler) {
        return;
    }
    
    QMutexLocker locker(&handlersMutex);
    
    // 检查是否已经注册了同名的处理器
    auto it = std::find_if(handlers.begin(), handlers.end(),
        [&handler](const std::shared_ptr<BaseEventHandler>& h) {
            return h->getName() == handler->getName();
        });
    
    if (it != handlers.end()) {
        writeLog(QString("警告：处理器 '%1' 已存在，将被替换").arg(handler->getName()), 1);
        handlers.erase(it);
    }
    
    handlers.append(handler);
    
    // 按优先级排序
    std::sort(handlers.begin(), handlers.end(),
        [](const std::shared_ptr<BaseEventHandler>& a, const std::shared_ptr<BaseEventHandler>& b) {
            return a->getPriority() > b->getPriority();
        });
    
    writeLog(QString("注册事件处理器: %1 (优先级: %2)")
             .arg(handler->getName())
             .arg(static_cast<int>(handler->getPriority())));
}

void EventManager::unregisterHandler(const QString& handlerName) {
    QMutexLocker locker(&handlersMutex);
    
    auto it = std::find_if(handlers.begin(), handlers.end(),
        [&handlerName](const std::shared_ptr<BaseEventHandler>& h) {
            return h->getName() == handlerName;
        });
    
    if (it != handlers.end()) {
        writeLog(QString("注销事件处理器: %1").arg(handlerName));
        handlers.erase(it);
    }
}

std::shared_ptr<BaseEventHandler> EventManager::getHandler(const QString& handlerName) {
    QMutexLocker locker(&handlersMutex);
    
    auto it = std::find_if(handlers.begin(), handlers.end(),
        [&handlerName](const std::shared_ptr<BaseEventHandler>& h) {
            return h->getName() == handlerName;
        });
    
    return (it != handlers.end()) ? *it : nullptr;
}

QVector<std::shared_ptr<BaseEventHandler>> EventManager::getHandlersForEvent(EventType eventType) {
    QMutexLocker locker(&handlersMutex);
    
    QVector<std::shared_ptr<BaseEventHandler>> result;
    for (const auto& handler : handlers) {
        if (handler->canHandle(eventType) && handler->getEnabled()) {
            result.append(handler);
        }
    }
    
    return result;
}

void EventManager::dispatchEvent(std::shared_ptr<EventData> eventData) {
    if (!eventData) {
        return;
    }
    
    writeLog(QString("分发事件: %1").arg(eventData->toString()));
    processEventInternal(eventData);
}

void EventManager::dispatchEventAsync(std::shared_ptr<EventData> eventData) {
    if (!eventData) {
        return;
    }
    
    QMutexLocker locker(&queueMutex);
    
    // 检查队列大小限制
    if (eventQueue.size() >= maxQueueSize) {
        writeLog(QString("事件队列已满，丢弃事件: %1").arg(eventData->toString()), 1);
        return;
    }
    
    eventQueue.enqueue(eventData);
    writeLog(QString("事件已入队: %1 (队列大小: %2)").arg(eventData->toString()).arg(eventQueue.size()));
}

void EventManager::processEventInternal(std::shared_ptr<EventData> eventData) {
    if (performanceMonitoringEnabled) {
        QMutexLocker perfLocker(&performanceMutex);
        totalEventsProcessed++;
    }
    
    QVector<std::shared_ptr<BaseEventHandler>> eventHandlers = getHandlersForEvent(eventData->getEventType());
    
    if (eventHandlers.isEmpty()) {
        writeLog(QString("没有找到处理器处理事件: %1").arg(eventData->toString()), 1);
        return;
    }
    
    bool anySuccess = false;
    bool shouldRetry = false;
    
    for (const auto& handler : eventHandlers) {
        EventResult result = handler->handleEvent(eventData);
        
        switch (result) {
            case EventResult::Success:
                anySuccess = true;
                writeLog(QString("事件处理成功: %1 by %2").arg(eventData->toString()).arg(handler->getName()));
                break;
                
            case EventResult::Failed:
                writeLog(QString("事件处理失败: %1 by %2").arg(eventData->toString()).arg(handler->getName()), 1);
                break;
                
            case EventResult::Retry:
                shouldRetry = true;
                writeLog(QString("事件需要重试: %1 by %2").arg(eventData->toString()).arg(handler->getName()));
                break;
                
            case EventResult::Ignored:
                writeLog(QString("事件被忽略: %1 by %2").arg(eventData->toString()).arg(handler->getName()));
                break;
        }
    }
    
    if (anySuccess) {
        if (performanceMonitoringEnabled) {
            QMutexLocker perfLocker(&performanceMutex);
            totalEventsSucceeded++;
        }
        emit eventProcessed(eventData->getEventType(), EventResult::Success);
    } else if (shouldRetry) {
        addToRetryQueue(eventData, 0);
    } else {
        if (performanceMonitoringEnabled) {
            QMutexLocker perfLocker(&performanceMutex);
            totalEventsFailed++;
        }
        emit eventProcessed(eventData->getEventType(), EventResult::Failed);
    }
}

void EventManager::addToRetryQueue(std::shared_ptr<EventData> eventData, int retryCount) {
    if (retryCount >= maxRetryCount) {
        writeLog(QString("事件重试次数超限，放弃处理: %1").arg(eventData->toString()), 1);
        return;
    }
    
    QMutexLocker locker(&retryMutex);
    
    RetryItem item;
    item.eventData = eventData;
    item.retryCount = retryCount;
    item.nextRetryTime = QDateTime::currentDateTime().addMSecs(retryInterval * (retryCount + 1));
    
    retryQueue.append(item);
    writeLog(QString("事件已加入重试队列: %1 (重试次数: %2)").arg(eventData->toString()).arg(retryCount));
}

void EventManager::processEventQueue() {
    QMutexLocker locker(&queueMutex);
    
    while (!eventQueue.isEmpty()) {
        auto eventData = eventQueue.dequeue();
        locker.unlock();
        
        processEventInternal(eventData);
        
        locker.relock();
    }
}

void EventManager::handleRetryTimer() {
    QMutexLocker locker(&retryMutex);
    
    QDateTime now = QDateTime::currentDateTime();
    QVector<RetryItem> itemsToRetry;
    
    auto it = retryQueue.begin();
    while (it != retryQueue.end()) {
        if (it->nextRetryTime <= now) {
            itemsToRetry.append(*it);
            it = retryQueue.erase(it);
        } else {
            ++it;
        }
    }
    
    locker.unlock();
    
    for (const auto& item : itemsToRetry) {
        writeLog(QString("重试处理事件: %1 (第%2次重试)").arg(item.eventData->toString()).arg(item.retryCount + 1));
        
        // 重新处理事件
        QVector<std::shared_ptr<BaseEventHandler>> eventHandlers = getHandlersForEvent(item.eventData->getEventType());
        bool shouldRetryAgain = false;
        
        for (const auto& handler : eventHandlers) {
            EventResult result = handler->handleEvent(item.eventData);
            if (result == EventResult::Retry) {
                shouldRetryAgain = true;
                break;
            } else if (result == EventResult::Success) {
                shouldRetryAgain = false;
                break;
            }
        }
        
        if (shouldRetryAgain) {
            addToRetryQueue(item.eventData, item.retryCount + 1);
        }
    }
}

void EventManager::writeLog(const QString& message, int level) {
    if (level < logLevel) {
        return;
    }
    
    QMutexLocker locker(&logMutex);
    
    QString logMessage = QString("[%1] %2")
                        .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                        .arg(message);
    
    if (logStream) {
        *logStream << logMessage << endl;
        logStream->flush();
    }
    
    // 同时输出到控制台
    if (level >= 1) {
        qCritical() << logMessage;
    } else {
        qDebug() << logMessage;
    }
}

void EventManager::setLogFile(const QString& logFile) {
    QMutexLocker locker(&logMutex);
    
    cleanupLog();
    logFileName = logFile;
    initializeLog();
}

void EventManager::initializeLog() {
    if (logFileName.isEmpty()) {
        logFileName = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs/EventManager.log";
    }
    
    QDir logDir = QFileInfo(logFileName).absoluteDir();
    if (!logDir.exists()) {
        logDir.mkpath(".");
    }
    
    logFile = new QFile(logFileName);
    if (logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
        logStream = new QTextStream(logFile);
        logStream->setCodec("UTF-8");
    }
}

void EventManager::cleanupLog() {
    if (logStream) {
        delete logStream;
        logStream = nullptr;
    }
    
    if (logFile) {
        logFile->close();
        delete logFile;
        logFile = nullptr;
    }
}

QString EventManager::getPerformanceReport() const {
    QMutexLocker locker(&performanceMutex);
    
    QDateTime now = QDateTime::currentDateTime();
    qint64 runningTime = startTime.msecsTo(now);
    
    QString report;
    QTextStream stream(&report);
    
    stream << "=== EventManager 性能报告 ===" << endl;
    stream << "运行时间: " << runningTime / 1000.0 << " 秒" << endl;
    stream << "总处理事件数: " << totalEventsProcessed << endl;
    stream << "成功处理数: " << totalEventsSucceeded << endl;
    stream << "失败处理数: " << totalEventsFailed << endl;
    
    if (runningTime > 0) {
        double eventsPerSecond = totalEventsProcessed * 1000.0 / runningTime;
        stream << "平均处理速度: " << QString::number(eventsPerSecond, 'f', 2) << " 事件/秒" << endl;
    }
    
    if (totalEventsProcessed > 0) {
        double successRate = totalEventsSucceeded * 100.0 / totalEventsProcessed;
        stream << "成功率: " << QString::number(successRate, 'f', 2) << "%" << endl;
    }
    
    stream << "当前队列大小: " << eventQueue.size() << endl;
    stream << "重试队列大小: " << retryQueue.size() << endl;
    stream << "注册处理器数量: " << handlers.size() << endl;
    
    return report;
}

void EventManager::resetPerformanceCounters() {
    QMutexLocker locker(&performanceMutex);
    
    startTime = QDateTime::currentDateTime();
    totalEventsProcessed = 0;
    totalEventsSucceeded = 0;
    totalEventsFailed = 0;
}

void EventManager::start() {
    if (running) {
        return;
    }
    
    running = true;
    startTime = QDateTime::currentDateTime();
    
    processTimer->start();
    retryTimer->start();
    
    writeLog("EventManager 已启动");
}

void EventManager::stop() {
    if (!running) {
        return;
    }
    
    running = false;
    
    processTimer->stop();
    retryTimer->stop();
    
    // 处理剩余的事件
    processEventQueue();
    
    writeLog("EventManager 已停止");
}

} // namespace ETC

