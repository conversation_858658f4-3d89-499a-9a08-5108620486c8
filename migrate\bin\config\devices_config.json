{"devices": [{"id": "OBU001", "name": "主OBU设备", "type": "OBU_JINYI_TCP", "enabled": true, "connection": {"type": "tcp", "host": "*************", "port": 8080, "timeout_ms": 3000, "auto_reconnect": true, "reconnect_interval_s": 5, "max_reconnect_attempts": 10}, "parameters": {"channel": 1, "power": 30, "work_mode": 1, "antenna_id": 1, "rsu_id": "RSU001", "terminal_id": "TERM001"}, "cache": {"max_obu_count": 20, "cache_timeout_s": 300, "lru_enabled": true}}, {"id": "OBU002", "name": "备用OBU设备", "type": "OBU_JINYI_TCP", "enabled": false, "connection": {"type": "tcp", "host": "*************", "port": 8080, "timeout_ms": 3000, "auto_reconnect": true, "reconnect_interval_s": 5, "max_reconnect_attempts": 10}, "parameters": {"channel": 2, "power": 30, "work_mode": 1, "antenna_id": 2, "rsu_id": "RSU002", "terminal_id": "TERM002"}, "cache": {"max_obu_count": 20, "cache_timeout_s": 300, "lru_enabled": true}}], "global_settings": {"gantry": {"gb_id": "3601001", "gb_hex": "360100", "name": "测试门架", "location": "测试路段"}, "psam": {"channels": [{"id": 1, "enabled": true, "terminal_id": "000001000001", "version": 1}, {"id": 2, "enabled": false, "terminal_id": "000001000002", "version": 1}]}, "statistics": {"daily_reset_time": "00:00:00", "auto_backup": true, "backup_interval_hours": 24}, "alerts": {"enable_sound": true, "enable_popup": true, "error_threshold": 10, "warning_threshold": 5}}, "monitoring": {"heartbeat_interval_s": 30, "status_check_interval_s": 10, "connection_timeout_s": 60, "performance_monitoring": true}}