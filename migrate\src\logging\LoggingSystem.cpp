/**
 * @file LoggingSystem.cpp
 * @brief ETC系统日志记录模块实现
 */

#include "logging/LoggingSystem.h"
#include <QDebug>
#include <QCoreApplication>
#include <QStandardPaths>
#include <QMutexLocker>
#include <QFileInfo>
#include <QDirIterator>
#include <QRegularExpression>
#include <QTimeZone>
#include <QJsonDocument>
#include <QJsonObject>
#include <algorithm>

namespace ETC {

// LogThread 实现
LogThread::LogThread(const LoggingConfig& config, QObject* parent)
    : QThread(parent)
    , config(config)
    , directoryReady(false)
    , cacheMode(true)  // 默认启用缓存模式
    , logFile(nullptr)
    , logStream(nullptr)
    , currentFileSize(0)
    , interval(DEFAULT_INTERVAL)
    , cycleInterval(DEFAULT_CYCLE_INTERVAL)
    , cycleCount(0)
    , stopRequested(false)
    , totalLogsProcessed(0)
    , totalBytesWritten(0)
{
    // 延迟创建日志目录，先启用缓存模式
    // ensureLogDirectory(); // 注释掉，改为延迟创建
    
    // 创建刷新定时器
    flushTimer = new QTimer();
    flushTimer->setInterval(config.flushInterval);
    flushTimer->moveToThread(this);
    connect(flushTimer, &QTimer::timeout, this, &LogThread::onFlushTimer);
    
    qRegisterMetaType<LogRecord>("LogRecord");
}

LogThread::~LogThread() {
    stopLogging();
    
    if (logStream) {
        delete logStream;
    }
    
    if (logFile) {
        logFile->close();
        delete logFile;
    }
    
    if (flushTimer) {
        delete flushTimer;
    }
}

void LogThread::pushLog(const LogRecord& record) {
    QMutexLocker locker(&queueMutex);
    
    // 检查队列大小限制
    if (logQueue.size() >= config.maxQueueSize) {
        // 移除最旧的日志记录
        logQueue.dequeue();
    }
    
    logQueue.enqueue(record);
    emit queueSizeChanged(logQueue.size());
    
    // 唤醒等待的线程
    queueCondition.wakeOne();
}

bool LogThread::popLog(LogRecord& record) {
    QMutexLocker locker(&queueMutex);
    
    if (logQueue.isEmpty()) {
        return false;
    }
    
    record = logQueue.dequeue();
    emit queueSizeChanged(logQueue.size());
    return true;
}

int LogThread::getQueueSize() const {
    QMutexLocker locker(&queueMutex);
    return logQueue.size();
}

void LogThread::setInterval(int intervalMs) {
    interval = intervalMs;
}

void LogThread::setCycleInterval(int intervalMs) {
    cycleInterval = intervalMs;
}

void LogThread::stopLogging() {
    stopRequested = true;
    queueCondition.wakeAll();
    
    if (isRunning()) {
        quit();
        if (!wait(5000)) {
            terminate();
            wait(1000);
        }
    }
}

void LogThread::updateConfig(const LoggingConfig& newConfig) {
    QMutexLocker locker(&configMutex);
    config = newConfig;
    
    if (flushTimer) {
        flushTimer->setInterval(config.flushInterval);
    }
}

LoggingConfig LogThread::getConfig() const {
    QMutexLocker locker(&configMutex);
    return config;
}

void LogThread::run() {
    // 启动刷新定时器
    if (flushTimer) {
        flushTimer->start();
    }
    
    exec();  // 进入事件循环
    
    // 停止定时器
    if (flushTimer) {
        flushTimer->stop();
    }
    
    // 处理剩余的日志
    doWork();
    
    // 关闭文件
    if (logStream) {
        logStream->flush();
    }
    if (logFile) {
        logFile->close();
    }
}

void LogThread::onFlushTimer() {
    doWork();
    
    // 刷新文件缓冲
    if (logStream) {
        logStream->flush();
    }
    if (logFile) {
        logFile->flush();
    }
    
    lastFlushTime = QDateTime::currentDateTime();
}

void LogThread::doWork() {
    LogRecord record;
    
    while (popLog(record)) {
        processLogRecord(record);
        totalLogsProcessed++;
        cycleCount++;
        
        emit logProcessed(record);
        
        // 检查是否需要停止
        if (stopRequested) {
            break;
        }
    }
}

void LogThread::processLogRecord(const LogRecord& record) {
    try {
        // 检查日志级别过滤
        if (record.level < config.minLevel) {
            return;
        }
        
        // 检查是否需要缓存日志
        addToCacheIfNeeded(record);
        
        // 如果目录已准备好且不在缓存模式，正常处理日志
        if (directoryReady && !cacheMode) {
            // 写入文件
            if (config.enableFileLogging) {
                writeToFile(record);
            }
            
            // 写入控制台
            if (config.enableConsoleLogging) {
                writeToConsole(record);
            }
        } else {
            // 在缓存模式下，只输出到控制台
            if (config.enableConsoleLogging) {
                writeToConsole(record);
            }
        }
        
    } catch (const std::exception& e) {
        emit errorOccurred(QString("处理日志记录时发生错误: %1").arg(e.what()));
    }
}

void LogThread::writeToFile(const LogRecord& record) {
    // 生成文件名
    QString fileName = config.logFilePattern.arg(QDate::currentDate().toString("yyyy-MM-dd"));
    QString fullPath = QDir(config.logDirectory).absoluteFilePath(fileName);
    
    // 检查是否需要轮转文件
    if (currentLogFile != fullPath || !logFile || !logFile->isOpen()) {
        if (logStream) {
            delete logStream;
            logStream = nullptr;
        }
        if (logFile) {
            logFile->close();
            delete logFile;
        }
        
        currentLogFile = fullPath;
        logFile = new QFile(currentLogFile);
        
        if (logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
            logStream = new QTextStream(logFile);
            logStream->setCodec("UTF-8");
            currentFileSize = logFile->size();
        } else {
            emit errorOccurred(QString("无法打开日志文件: %1").arg(currentLogFile));
            return;
        }
    }
    
    // 检查文件大小是否需要轮转
    if (currentFileSize > config.maxFileSize * 1024 * 1024) {
        rotateLogFile();
    }
    
    // 写入日志
    if (logStream) {
        QString logMessage = formatLogMessage(record);
        *logStream << logMessage << endl;
        
        qint64 messageSize = logMessage.toUtf8().size() + 1;  // +1 for newline
        currentFileSize += messageSize;
        totalBytesWritten += messageSize;
    }
}

void LogThread::writeToConsole(const LogRecord& record) {
    QString message = formatLogMessage(record);
    
    // 根据日志级别使用不同的输出方式
    switch (record.level) {
        case LogLevel::Debug:
        case LogLevel::Info:
            qDebug().noquote() << message;
            break;
        case LogLevel::Warning:
            qWarning().noquote() << message;
            break;
        case LogLevel::Error:
        case LogLevel::Critical:
            qCritical().noquote() << message;
            break;
    }
}

void LogThread::rotateLogFile() {
    if (!logFile) {
        return;
    }
    
    QString baseName = QFileInfo(currentLogFile).completeBaseName();
    QString extension = QFileInfo(currentLogFile).suffix();
    QString dirPath = QFileInfo(currentLogFile).absolutePath();
    
    // 关闭当前文件
    if (logStream) {
        delete logStream;
        logStream = nullptr;
    }
    logFile->close();
    
    // 重命名当前文件
    QString timestamp = QDateTime::currentDateTime().toString("hhmmss");
    QString rotatedName = QString("%1_%2.%3").arg(baseName).arg(timestamp).arg(extension);
    QString rotatedPath = QDir(dirPath).absoluteFilePath(rotatedName);
    
    QFile::rename(currentLogFile, rotatedPath);
    
    // 重新打开新文件
    if (logFile->open(QIODevice::WriteOnly | QIODevice::Append)) {
        logStream = new QTextStream(logFile);
        logStream->setCodec("UTF-8");
        currentFileSize = 0;
    }
    
    // 清理旧文件
    cleanupOldFiles();
}

void LogThread::cleanupOldFiles() {
    QDir logDir(config.logDirectory);
    QStringList nameFilters;
    nameFilters << "*.log";
    
    QFileInfoList files = logDir.entryInfoList(nameFilters, QDir::Files, QDir::Time);
    
    // 删除超过最大数量的文件
    while (files.size() > config.maxFileCount) {
        QFile::remove(files.takeLast().absoluteFilePath());
    }
}

QString LogThread::formatLogMessage(const LogRecord& record) const {
    QString message;
    
    // 时间戳
    if (config.enableTimestamp) {
        message += record.logTime.toString(config.timeFormat);
        message += " ";
    }
    
    // 日志级别
    message += QString("[%1]").arg(getLevelString(record.level));
    
    // 线程信息
    if (config.enableThreadInfo && !record.threadId.isEmpty()) {
        message += QString("[%1]").arg(record.threadId);
    }
    
    // 分类信息
    if (!record.category.isEmpty()) {
        message += QString("[%1]").arg(record.category);
    }
    
    // 源文件信息
    if (config.enableSourceInfo && !record.sourceFile.isEmpty()) {
        QString fileName = QFileInfo(record.sourceFile).baseName();
        message += QString("[%1:%2]").arg(fileName).arg(record.sourceLine);
    }
    
    message += " ";
    message += record.logInfo;
    
    // 附加整数信息
    if (record.logInt != 0) {
        message += QString(" (Int: %1)").arg(record.logInt);
    }
    
    return message;
}

QString LogThread::getLevelString(LogLevel level) const {
    switch (level) {
        case LogLevel::Debug: return "DEBUG";
        case LogLevel::Info: return "INFO";
        case LogLevel::Warning: return "WARN";
        case LogLevel::Error: return "ERROR";
        case LogLevel::Critical: return "CRIT";
        default: return "UNKNOWN";
    }
}

void LogThread::ensureLogDirectory() {
    QDir dir;
    if (!dir.exists(config.logDirectory)) {
        dir.mkpath(config.logDirectory);
    }
}

void LogThread::mySleep(int ms) {
    QThread::msleep(ms);
}

/**
 * @brief 启用缓存模式
 * 在日志目录创建之前，所有日志将被缓存
 */
void LogThread::enableCacheMode() {
    QMutexLocker locker(&cacheMutex);
    cacheMode = true;
    directoryReady = false;
}

/**
 * @brief 禁用缓存模式并写入缓存的日志
 * 创建日志目录后调用，将缓存的日志写入文件
 */
void LogThread::disableCacheMode() {
    // 首先确保日志目录存在
    ensureLogDirectory();
    directoryReady = true;
    
    // 刷新缓存的日志
    flushCachedLogs();
    
    // 禁用缓存模式
    QMutexLocker locker(&cacheMutex);
    cacheMode = false;
}

/**
 * @brief 刷新缓存的日志到文件
 * 将所有缓存的日志记录写入文件
 */
void LogThread::flushCachedLogs() {
    QMutexLocker locker(&cacheMutex);
    
    while (!cachedLogs.isEmpty()) {
        LogRecord record = cachedLogs.dequeue();
        
        // 写入文件（如果启用）
        if (config.enableFileLogging) {
            writeToFile(record);
        }
        
        // 更新统计信息
        totalLogsProcessed++;
        
        // 发送信号通知日志已处理
        emit logProcessed(record);
    }
}

/**
 * @brief 根据需要添加到缓存
 * 如果处于缓存模式，将日志记录添加到缓存队列
 */
void LogThread::addToCacheIfNeeded(const LogRecord& record) {
    QMutexLocker locker(&cacheMutex);
    
    if (cacheMode && !directoryReady) {
        cachedLogs.enqueue(record);
    }
}

// Logger 静态成员初始化
QMap<QString, Logger*> Logger::loggerInstances;
QMutex Logger::instanceMutex;
LoggingConfig Logger::globalConfig;

// Logger 实现
Logger::Logger(const QString& name, QObject* parent)
    : QObject(parent)
    , loggerName(name)
    , logThread(nullptr)
    , maxMemoryLogs(1000)  // 初始化最大内存日志数量
    , currentConfig(globalConfig)
{
    initializeLogThread();
}

Logger::~Logger() {
    stop();
}

Logger* Logger::getInstance(const QString& name) {
    QMutexLocker locker(&instanceMutex);
    
    if (!loggerInstances.contains(name)) {
        loggerInstances[name] = new Logger(name);
    }
    
    return loggerInstances[name];
}

void Logger::setGlobalConfig(const LoggingConfig& config) {
    QMutexLocker locker(&instanceMutex);
    globalConfig = config;
    
    // 更新所有现有实例的配置
    for (Logger* logger : loggerInstances.values()) {
        logger->setConfig(config);
    }
}

LoggingConfig Logger::getGlobalConfig() {
    QMutexLocker locker(&instanceMutex);
    return globalConfig;
}

void Logger::setConfig(const LoggingConfig& config) {
    currentConfig = config;
    
    if (logThread) {
        logThread->updateConfig(config);
    }
}

LoggingConfig Logger::getConfig() const {
    return currentConfig;
}

void Logger::log(LogLevel level, const QString& message, const QString& category) {
    if (!logThread) {
        return;
    }
    
    LogRecord record;
    record.logTime = QDateTime::currentDateTime();
    record.logInfo = message;
    record.level = level;
    record.category = category;
    record.threadId = QString::number(reinterpret_cast<quintptr>(QThread::currentThreadId()));
    
    logThread->pushLog(record);
    emit logRecorded(record);
}

void Logger::debug(const QString& message, const QString& category) {
    log(LogLevel::Debug, message, category);
}

void Logger::info(const QString& message, const QString& category) {
    log(LogLevel::Info, message, category);
}

void Logger::warning(const QString& message, const QString& category) {
    log(LogLevel::Warning, message, category);
}

void Logger::error(const QString& message, const QString& category) {
    log(LogLevel::Error, message, category);
}

void Logger::critical(const QString& message, const QString& category) {
    log(LogLevel::Critical, message, category);
}

void Logger::logWithSource(LogLevel level, const QString& message, const char* file, int line, const QString& category) {
    if (!logThread) {
        return;
    }
    
    LogRecord record;
    record.logTime = QDateTime::currentDateTime();
    record.logInfo = message;
    record.level = level;
    record.category = category;
    record.sourceFile = QString::fromUtf8(file);
    record.sourceLine = line;
    record.threadId = QString::number(reinterpret_cast<quintptr>(QThread::currentThreadId()));
    
    logThread->pushLog(record);
    emit logRecorded(record);
}

void Logger::start() {
    if (logThread && !logThread->isRunning()) {
        logThread->start();
    }
}

void Logger::stop() {
    if (logThread) {
        logThread->stopLogging();
        logThread = nullptr;
    }
}

bool Logger::isRunning() const {
    return logThread && logThread->isRunning();
}

void Logger::flush() {
    if (logThread) {
        // 触发立即刷新
        QMetaObject::invokeMethod(logThread, "onFlushTimer", Qt::QueuedConnection);
    }
}

int Logger::getQueueSize() const {
    return logThread ? logThread->getQueueSize() : 0;
}

qint64 Logger::getTotalLogsProcessed() const {
    return logThread ? logThread->getTotalLogsProcessed() : 0;
}

qint64 Logger::getTotalBytesWritten() const {
    return logThread ? logThread->getTotalBytesWritten() : 0;
}

/**
 * @brief 启用日志缓存模式
 * 在日志目录创建之前调用，所有日志将被缓存
 */
void Logger::enableLogCache() {
    if (logThread) {
        logThread->enableCacheMode();
    }
}

/**
 * @brief 禁用日志缓存并写入缓存的日志
 * 在日志目录创建之后调用，将缓存的日志写入文件
 */
void Logger::disableLogCache() {
    if (logThread) {
        logThread->disableCacheMode();
    }
}

void Logger::onLogProcessed(const LogRecord& record) {
    // 更新内存日志缓存
    updateMemoryLogs(record);
}

void Logger::onLogThreadError(const QString& error) {
    emit errorOccurred(error);
}

void Logger::initializeLogThread() {
    if (logThread) {
        logThread->stopLogging();
    }
    
    logThread = new LogThread(currentConfig, this);
    connect(logThread, &LogThread::logProcessed, this, &Logger::onLogProcessed);
    connect(logThread, &LogThread::errorOccurred, this, &Logger::onLogThreadError);
    
    logThread->start();
}

// 添加内存日志相关方法的实现
void Logger::updateMemoryLogs(const LogRecord& record) {
    QMutexLocker locker(&memoryMutex);
    
    // 格式化日志消息
    QString message = QString("[%1] [%2] %3")
                     .arg(record.logTime.toString("hh:mm:ss.zzz"))
                     .arg([](LogLevel level) {
                         switch (level) {
                             case LogLevel::Debug: return "DEBUG";
                             case LogLevel::Info: return "INFO";
                             case LogLevel::Warning: return "WARN";
                             case LogLevel::Error: return "ERROR";
                             case LogLevel::Critical: return "CRITICAL";
                             default: return "UNKNOWN";
                         }
                     }(record.level))
                     .arg(record.logInfo);
    
    // 添加到内存日志缓存
    memoryLogs.append(message);
    
    // 限制缓存大小
    if (memoryLogs.size() > maxMemoryLogs) {
        memoryLogs.removeFirst();
    }
}

QStringList Logger::getRecentLogs(int maxCount) const {
    QMutexLocker locker(&memoryMutex);
    
    int count = qMin(maxCount, memoryLogs.size());
    if (count <= 0) {
        return QStringList();
    }
    
    // 返回最近的count条日志
    int start = memoryLogs.size() - count;
    return memoryLogs.mid(start, count);
}

void Logger::clearMemoryLogs() {
    QMutexLocker locker(&memoryMutex);
    memoryLogs.clear();
}

// TimeUtils 实现
qint32 TimeUtils::dateTimeToUnixDate(const QDateTime& dateTime, int timeZone) {
    // 基准日期：2000年1月1日（对应原Delphi代码）
    QDateTime baseDate(QDate(2000, 1, 1), QTime(0, 0, 0), Qt::UTC);
    QDateTime adjustedTime = dateTime.addSecs(-timeZone * 3600);
    
    qint64 secondsSinceBase = baseDate.secsTo(adjustedTime);
    return static_cast<qint32>(secondsSinceBase);
}

QDateTime TimeUtils::unixDateToDateTime(qint32 unixDate, int timeZone) {
    // 基准日期：2000年1月1日
    QDateTime baseDate(QDate(2000, 1, 1), QTime(0, 0, 0), Qt::UTC);
    QDateTime result = baseDate.addSecs(unixDate + timeZone * 3600);
    return result;
}

qint64 TimeUtils::dateTimeToUnixTimestamp(const QDateTime& dateTime) {
    return dateTime.toSecsSinceEpoch();
}

QDateTime TimeUtils::unixTimestampToDateTime(qint64 timestamp) {
    return QDateTime::fromSecsSinceEpoch(timestamp);
}

QString TimeUtils::formatDateTime(const QDateTime& dateTime, const QString& format) {
    return dateTime.toString(format);
}

QString TimeUtils::formatDuration(qint64 milliseconds) {
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    qint64 hours = minutes / 60;
    
    seconds %= 60;
    minutes %= 60;
    
    if (hours > 0) {
        return QString("%1h %2m %3s").arg(hours).arg(minutes).arg(seconds);
    } else if (minutes > 0) {
        return QString("%1m %2s").arg(minutes).arg(seconds);
    } else {
        return QString("%1s").arg(seconds);
    }
}

QDateTime TimeUtils::toBeijingTime(const QDateTime& utcTime) {
    return utcTime.addSecs(8 * 3600);  // UTC+8
}

QDateTime TimeUtils::toBeiJingTime(const QDateTime& localTime) {
    return localTime.toTimeZone(QTimeZone("Asia/Shanghai"));
}

QDateTime TimeUtils::toShanghaiTime(const QDateTime& utcTime) {
    return utcTime.addSecs(8 * 3600);  // UTC+8
}

// LogFilter 实现
LogFilter::LogFilter()
    : levelFilterEnabled(false)
    , minLevel(LogLevel::Debug)
    , maxLevel(LogLevel::Critical)
    , categoryFilterEnabled(false)
    , timeRangeFilterEnabled(false)
    , textFilterEnabled(false)
    , caseSensitive(false)
    , threadFilterEnabled(false)
{
}

void LogFilter::setLevelFilter(LogLevel minLevel, LogLevel maxLevel) {
    this->minLevel = minLevel;
    this->maxLevel = maxLevel;
    levelFilterEnabled = true;
}

void LogFilter::setCategoryFilter(const QStringList& categories) {
    allowedCategories = categories;
    categoryFilterEnabled = !categories.isEmpty();
}

void LogFilter::setTimeRangeFilter(const QDateTime& startTime, const QDateTime& endTime) {
    this->startTime = startTime;
    this->endTime = endTime;
    timeRangeFilterEnabled = true;
}

void LogFilter::setTextFilter(const QString& text, bool caseSensitive) {
    filterText = text;
    this->caseSensitive = caseSensitive;
    textFilterEnabled = !text.isEmpty();
}

void LogFilter::setThreadFilter(const QStringList& threadIds) {
    allowedThreads = threadIds;
    threadFilterEnabled = !threadIds.isEmpty();
}

bool LogFilter::matchRecord(const LogRecord& record) const {
    // 检查级别过滤
    if (levelFilterEnabled) {
        if (record.level < minLevel || record.level > maxLevel) {
            return false;
        }
    }
    
    // 检查分类过滤
    if (categoryFilterEnabled) {
        if (!allowedCategories.contains(record.category)) {
            return false;
        }
    }
    
    // 检查时间范围过滤
    if (timeRangeFilterEnabled) {
        if (record.logTime < startTime || record.logTime > endTime) {
            return false;
        }
    }
    
    // 检查文本过滤
    if (textFilterEnabled) {
        Qt::CaseSensitivity cs = caseSensitive ? Qt::CaseSensitive : Qt::CaseInsensitive;
        if (!record.logInfo.contains(filterText, cs)) {
            return false;
        }
    }
    
    // 检查线程过滤
    if (threadFilterEnabled) {
        if (!allowedThreads.contains(record.threadId)) {
            return false;
        }
    }
    
    return true;
}

void LogFilter::clearFilters() {
    levelFilterEnabled = false;
    categoryFilterEnabled = false;
    timeRangeFilterEnabled = false;
    textFilterEnabled = false;
    threadFilterEnabled = false;
    
    allowedCategories.clear();
    allowedThreads.clear();
    filterText.clear();
}

LogFilter LogFilter::createErrorFilter() {
    LogFilter filter;
    filter.setLevelFilter(LogLevel::Error, LogLevel::Critical);
    return filter;
}

LogFilter LogFilter::createCategoryFilter(const QString& category) {
    LogFilter filter;
    filter.setCategoryFilter(QStringList() << category);
    return filter;
}

LogFilter LogFilter::createTimeRangeFilter(const QDateTime& start, const QDateTime& end) {
    LogFilter filter;
    filter.setTimeRangeFilter(start, end);
    return filter;
}

// LogManager 静态成员初始化
LogManager* LogManager::instance = nullptr;
QMutex LogManager::instanceMutex;

// LogManager 实现
LogManager::LogManager(QObject* parent)
    : QObject(parent)
{
}

LogManager::~LogManager() {
    stopAllLoggers();
    qDeleteAll(loggers.values());
}

LogManager* LogManager::getInstance() {
    QMutexLocker locker(&instanceMutex);
    if (!instance) {
        instance = new LogManager();
    }
    return instance;
}

void LogManager::setGlobalConfig(const LoggingConfig& config) {
    globalConfig = config;
    Logger::setGlobalConfig(config);
    emit configChanged(config);
}

LoggingConfig LogManager::getGlobalConfig() const {
    return globalConfig;
}

Logger* LogManager::getLogger(const QString& name) {
    QMutexLocker locker(&loggersMutex);
    
    if (!loggers.contains(name)) {
        loggers[name] = new Logger(name, this);
        emit loggerAdded(name);
    }
    
    return loggers[name];
}

QStringList LogManager::getLoggerNames() const {
    QMutexLocker locker(&loggersMutex);
    return loggers.keys();
}

void LogManager::removeLogger(const QString& name) {
    QMutexLocker locker(&loggersMutex);
    
    if (loggers.contains(name)) {
        Logger* logger = loggers.take(name);
        logger->stop();
        delete logger;
        emit loggerRemoved(name);
    }
}

void LogManager::startAllLoggers() {
    QMutexLocker locker(&loggersMutex);
    
    for (Logger* logger : loggers.values()) {
        logger->start();
    }
}

void LogManager::stopAllLoggers() {
    QMutexLocker locker(&loggersMutex);
    
    for (Logger* logger : loggers.values()) {
        logger->stop();
    }
}

void LogManager::flushAllLoggers() {
    QMutexLocker locker(&loggersMutex);
    
    for (Logger* logger : loggers.values()) {
        logger->flush();
    }
}

QMap<QString, qint64> LogManager::getLoggingStatistics() const {
    QMutexLocker locker(&loggersMutex);
    
    QMap<QString, qint64> stats;
    
    for (auto it = loggers.begin(); it != loggers.end(); ++it) {
        const QString& name = it.key();
        const Logger* logger = it.value();
        
        stats[name + "_processed"] = logger->getTotalLogsProcessed();
        stats[name + "_bytes"] = logger->getTotalBytesWritten();
        stats[name + "_queue"] = logger->getQueueSize();
    }
    
    return stats;
}

QString LogManager::generateStatusReport() const {
    QMutexLocker locker(&loggersMutex);
    
    QString report;
    report += "=== 日志系统状态报告 ===\n";
    report += QString("生成时间: %1\n").arg(QDateTime::currentDateTime().toString());
    report += QString("日志记录器数量: %1\n\n").arg(loggers.size());
    
    for (auto it = loggers.begin(); it != loggers.end(); ++it) {
        const QString& name = it.key();
        const Logger* logger = it.value();
        
        report += QString("记录器: %1\n").arg(name);
        report += QString("  状态: %1\n").arg(logger->isRunning() ? "运行中" : "已停止");
        report += QString("  已处理日志: %1\n").arg(logger->getTotalLogsProcessed());
        report += QString("  已写入字节: %1\n").arg(logger->getTotalBytesWritten());
        report += QString("  队列大小: %1\n").arg(logger->getQueueSize());
        report += "\n";
    }
    
    return report;
}

void LogManager::loadConfig(const QString& configFile) {
    QFile file(configFile);
    if (!file.open(QIODevice::ReadOnly)) {
        return;
    }
    
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
    if (!doc.isObject()) {
        return;
    }
    
    QJsonObject obj = doc.object();
    LoggingConfig config;
    
    // 从JSON加载配置
    config.enableFileLogging = obj["enableFileLogging"].toBool(true);
    config.enableConsoleLogging = obj["enableConsoleLogging"].toBool(true);
    config.enableMemoryLogging = obj["enableMemoryLogging"].toBool(false);
    config.minLevel = static_cast<LogLevel>(obj["minLevel"].toInt(1));
    config.logDirectory = obj["logDirectory"].toString("logs");
    config.logFilePattern = obj["logFilePattern"].toString("app_%1.log");
    config.maxFileSize = obj["maxFileSize"].toInt(10);
    config.maxFileCount = obj["maxFileCount"].toInt(10);
    config.flushInterval = obj["flushInterval"].toInt(1000);
    config.maxQueueSize = obj["maxQueueSize"].toInt(10000);
    config.enableTimestamp = obj["enableTimestamp"].toBool(true);
    config.enableThreadInfo = obj["enableThreadInfo"].toBool(true);
    config.enableSourceInfo = obj["enableSourceInfo"].toBool(false);
    config.timeFormat = obj["timeFormat"].toString("yyyy-MM-dd hh:mm:ss.zzz");
    
    setGlobalConfig(config);
}

void LogManager::saveConfig(const QString& configFile) const {
    QJsonObject obj;
    
    // 保存配置到JSON
    obj["enableFileLogging"] = globalConfig.enableFileLogging;
    obj["enableConsoleLogging"] = globalConfig.enableConsoleLogging;
    obj["enableMemoryLogging"] = globalConfig.enableMemoryLogging;
    obj["minLevel"] = static_cast<int>(globalConfig.minLevel);
    obj["logDirectory"] = globalConfig.logDirectory;
    obj["logFilePattern"] = globalConfig.logFilePattern;
    obj["maxFileSize"] = globalConfig.maxFileSize;
    obj["maxFileCount"] = globalConfig.maxFileCount;
    obj["flushInterval"] = globalConfig.flushInterval;
    obj["maxQueueSize"] = globalConfig.maxQueueSize;
    obj["enableTimestamp"] = globalConfig.enableTimestamp;
    obj["enableThreadInfo"] = globalConfig.enableThreadInfo;
    obj["enableSourceInfo"] = globalConfig.enableSourceInfo;
    obj["timeFormat"] = globalConfig.timeFormat;
    
    QJsonDocument doc(obj);
    
    QFile file(configFile);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
    }
}

} // namespace ETC
