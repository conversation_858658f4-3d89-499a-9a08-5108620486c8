# RSU设备控制系统 - Qt版本 qmake项目文件
# 开发平台: Qt 5.12 + C++17
# 编码: UTF-8

# Qt版本要求 (控制台应用程序)
QT += core network serialport concurrent
QT -= gui widgets

# 项目配置
TARGET = RSU
TEMPLATE = app
VERSION = 1.0.0

# C++标准设置
CONFIG += c++17

# 编译配置 (控制台应用程序)
CONFIG += warn_on
CONFIG += qt thread
CONFIG += console

# 发布版本优化
CONFIG(release, debug|release) {
    CONFIG += optimize_full
    DEFINES += QT_NO_DEBUG_OUTPUT
}

# 调试版本配置
CONFIG(debug, debug|release) {
    CONFIG += debug_and_release
    DEFINES += QT_MESSAGELOGCONTEXT
}

# 编译器定义
DEFINES += QT_DEPRECATED_WARNINGS
DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000
# DEFINES += QT_NO_KEYWORDS  # 控制台应用程序不需要禁用Qt关键字

# Windows平台特定设置
win32 {
    DEFINES += WIN32_LEAN_AND_MEAN
    DEFINES += NOMINMAX
    DEFINES += _CRT_SECURE_NO_WARNINGS
    
    # 链接库
    LIBS += -lws2_32 -lwinmm
    
    # 资源文件
    RC_FILE = resources/app.rc
    
    # 输出目录
    DESTDIR = $$PWD/bin
    OBJECTS_DIR = $$PWD/build/obj
    MOC_DIR = $$PWD/build/moc
    UI_DIR = $$PWD/build/ui
    RCC_DIR = $$PWD/build/rcc
}

# Linux平台特定设置
unix:!macx {
    DESTDIR = $$PWD/bin
    OBJECTS_DIR = $$PWD/build/obj
    MOC_DIR = $$PWD/build/moc
    UI_DIR = $$PWD/build/ui
    RCC_DIR = $$PWD/build/rcc
}

# 包含路径
INCLUDEPATH += $$PWD/include

# 头文件
HEADERS += \
    include/CommonDataStructures.h \
    include/common.h \
    include/core/DataStructures.h \
    include/core/ConfigManager.h \
    include/core/EventFramework.h \
    include/core/EventHandlers.h \
    include/core/OBUDevice.h \
    include/core/OBUEventConnector.h \
    include/network/TCPCommunication.h \
    include/logging/LoggingSystem.h \
    include/security/SecurityModule.h
    # include/MainWindow.h  # 控制台应用程序不使用MainWindow

# 源文件
SOURCES += \
    src/main.cpp \
    src/core/DataStructures.cpp \
    src/core/ConfigManager.cpp \
    src/core/EventFramework.cpp \
    src/core/EventHandlers.cpp \
    src/core/OBUDevice.cpp \
    src/core/OBUEventConnector.cpp \
    # src/core/MainWindow.cpp \  # 控制台应用程序不使用MainWindow
    src/network/TCPCommunication.cpp \
    src/logging/LoggingSystem.cpp \
    src/security/SecurityModule.cpp

# UI文件 (控制台应用程序不使用UI文件)
# FORMS += \
#     ui/MainWindow.ui

# 资源文件
#RESOURCES += \
#    resourapp.qrcces/

# 翻译文件
TRANSLATIONS += \
    translations/RSU_zh_CN.ts \
    translations/RSU_en_US.ts

# 安装配置
target.path = /usr/local/bin
config.path = /usr/local/bin/config
config.files = config/*.json

INSTALLS += target config

# 自定义编译后处理
win32 {
    # Windows部署Qt库
    QMAKE_POST_LINK += echo "部署Qt库..." $$escape_expand(\\n\\t)
    
    # 创建必要目录
    QMAKE_POST_LINK += if not exist \"$$DESTDIR\\config\" mkdir \"$$DESTDIR\\config\" $$escape_expand(\\n\\t)
    QMAKE_POST_LINK += if not exist \"$$DESTDIR\\logs\" mkdir \"$$DESTDIR\\logs\" $$escape_expand(\\n\\t)
    
    # 复制配置文件
    QMAKE_POST_LINK += copy \"$$PWD\\config\\system_config.json\" \"$$DESTDIR\\config\\\" $$escape_expand(\\n\\t)
    QMAKE_POST_LINK += copy \"$$PWD\\config\\devices_config.json\" \"$$DESTDIR\\config\\\" $$escape_expand(\\n\\t)
}

unix {
    # Linux安装后处理
    QMAKE_POST_LINK += mkdir -p $$DESTDIR/config $$escape_expand(\\n\\t)
    QMAKE_POST_LINK += mkdir -p $$DESTDIR/logs $$escape_expand(\\n\\t)
    QMAKE_POST_LINK += cp $$PWD/config/*.json $$DESTDIR/config/ $$escape_expand(\\n\\t)
}

# 输出构建信息
message("=== RSU ETC系统qmake构建配置 ===")
message("项目名称: $$TARGET")
message("项目版本: $$VERSION")
message("Qt版本: $$QT_VERSION")
message("构建模式: $$CONFIG")
message("输出目录: $$DESTDIR")
message("包含路径: $$INCLUDEPATH")
message("============================")
