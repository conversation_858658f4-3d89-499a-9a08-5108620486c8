/**
 * @file common.h
 * @brief RSU设备控制系统通用头文件
 * <AUTHOR>
 * @date 2024年12月
 * @encoding UTF-8
 * 
 * 包含系统中常用的数据类型定义、常量定义和工具宏
 */

#ifndef COMMON_H
#define COMMON_H

#include <QtCore>
#include <QtWidgets>
#include <QtNetwork>
#include <QtSerialPort>

#include <memory>
#include <functional>
#include <chrono>
#include <thread>
#include <mutex>
#include <atomic>
#include <future>

// 版本信息
#define RSU_VERSION_MAJOR    1
#define RSU_VERSION_MINOR    0
#define RSU_VERSION_PATCH    0
#define RSU_VERSION_STRING   "1.0.0"

// 系统常量
namespace RSUConstants {
    // 网络通信
    const int DEFAULT_TCP_PORT = 8000;
    const int DEFAULT_UDP_PORT = 8001;
    const int MAX_CONNECTIONS = 100;
    const int NETWORK_TIMEOUT_MS = 3000;
    
    // OBU设备
    const int MAX_OBU_DEVICES = 32;
    const int OBU_CACHE_SIZE = 20;
    const int OBU_TIMEOUT_MS = 5000;
    
    // 事件系统
    const int EVENT_QUEUE_SIZE = 1000;
    const int MAX_EVENT_HANDLERS = 50;
    
    // 日志系统
    const int LOG_BUFFER_SIZE = 1024;
    const int MAX_LOG_FILE_SIZE_MB = 50;
    const int MAX_LOG_FILES = 10;
    
    // 界面
    const int MIN_WINDOW_WIDTH = 800;
    const int MIN_WINDOW_HEIGHT = 600;
    const int DEFAULT_WINDOW_WIDTH = 1200;
    const int DEFAULT_WINDOW_HEIGHT = 800;
}

// 通用数据类型
using ByteArray = QByteArray;
using String = QString;
using StringList = QStringList;
using Variant = QVariant;
using VariantMap = QVariantMap;
using DateTime = QDateTime;
using Timer = QTimer;

// 智能指针类型
template<typename T>
using UniquePtr = std::unique_ptr<T>;

template<typename T>
using SharedPtr = std::shared_ptr<T>;

template<typename T>
using WeakPtr = std::weak_ptr<T>;

// 创建智能指针的便利函数
template<typename T, typename... Args>
UniquePtr<T> makeUnique(Args&&... args) {
    return std::make_unique<T>(std::forward<Args>(args)...);
}

template<typename T, typename... Args>
SharedPtr<T> makeShared(Args&&... args) {
    return std::make_shared<T>(std::forward<Args>(args)...);
}

// 枚举类：设备状态
enum class DeviceStatus {
    Unknown = 0,        // 未知状态
    Disconnected,       // 未连接
    Connecting,         // 连接中
    Connected,          // 已连接
    Working,            // 工作中
    Error,              // 错误状态
    Timeout             // 超时
};

// 枚举类：事件类型
enum class EventType {
    // 设备管理类事件
    DeviceConnected = 100,
    DeviceDisconnected,
    DeviceError,
    DeviceStatusChanged,
    
    // RSU设备事件
    RSUError = 200,
    RSUReset,
    RSUHeartbeat,
    
    // OBU数据事件
    GetB0 = 300,
    GetB1,
    GetB2,
    GetOBUID,
    GetB4,
    GetB5,
    GetOBUVehicle,
    GetCPUInfo,
    GetCPUInfoNew,
    
    // 交易流程事件
    OBUSessionEnd = 400,
    OBUConsumed,
    PurchaseTimeout,
    
    // 认证相关事件
    PSAMInit = 500,
    PSAMResult,
    
    // 系统事件
    SystemStart = 600,
    SystemShutdown,
    ConfigChanged,
    
    // 自定义事件
    UserDefined = 1000
};

// 枚举类：日志级别
enum class LogLevel {
    Debug = 0,
    Info,
    Warning,
    Error,
    Critical
};

// 枚举类：事件优先级
enum class EventPriority {
    Low = 0,
    Normal,
    High,
    Critical
};

// 结果状态枚举
enum class ResultStatus {
    Success = 0,
    Failed,
    Timeout,
    Cancelled,
    InvalidParameter,
    NotSupported,
    InternalError
};

// 通用结果模板类
template<typename T>
class Result {
public:
    Result() : status_(ResultStatus::Success) {}
    Result(const T& data) : status_(ResultStatus::Success), data_(data) {}
    Result(ResultStatus status, const String& message = "") 
        : status_(status), message_(message) {}
    
    bool isSuccess() const { return status_ == ResultStatus::Success; }
    bool isFailed() const { return status_ != ResultStatus::Success; }
    
    ResultStatus status() const { return status_; }
    const String& message() const { return message_; }
    const T& data() const { return data_; }
    T& data() { return data_; }
    
    void setStatus(ResultStatus status) { status_ = status; }
    void setMessage(const String& message) { message_ = message; }
    void setData(const T& data) { data_ = data; }

private:
    ResultStatus status_;
    String message_;
    T data_;
};

// 特化的void结果类型
using VoidResult = Result<void*>;

// 工具宏定义
#define RSU_UNUSED(x) Q_UNUSED(x)

#define RSU_ASSERT(condition) Q_ASSERT(condition)

#define RSU_LIKELY(x) Q_LIKELY(x)
#define RSU_UNLIKELY(x) Q_UNLIKELY(x)

// 禁用拷贝构造和赋值的宏
#define RSU_DISABLE_COPY(Class) \
    Class(const Class&) = delete; \
    Class& operator=(const Class&) = delete;

// 禁用移动构造和赋值的宏
#define RSU_DISABLE_MOVE(Class) \
    Class(Class&&) = delete; \
    Class& operator=(Class&&) = delete;

// 禁用拷贝和移动的宏
#define RSU_DISABLE_COPY_MOVE(Class) \
    RSU_DISABLE_COPY(Class) \
    RSU_DISABLE_MOVE(Class)

// 单例模式宏
#define RSU_SINGLETON(Class) \
    public: \
        static Class& instance() { \
            static Class instance_; \
            return instance_; \
        } \
    private: \
        RSU_DISABLE_COPY_MOVE(Class)

// 调试输出宏
#ifdef QT_DEBUG
    #define RSU_DEBUG(msg) qDebug() << "[DEBUG]" << __FUNCTION__ << ":" << __LINE__ << "-" << msg
    #define RSU_INFO(msg) qInfo() << "[INFO]" << __FUNCTION__ << ":" << __LINE__ << "-" << msg
#else
    #define RSU_DEBUG(msg) 
    #define RSU_INFO(msg) qInfo() << "[INFO]" << msg
#endif

#define RSU_WARNING(msg) qWarning() << "[WARNING]" << __FUNCTION__ << ":" << __LINE__ << "-" << msg
#define RSU_ERROR(msg) qCritical() << "[ERROR]" << __FUNCTION__ << ":" << __LINE__ << "-" << msg

// 时间相关工具函数
namespace TimeUtils {
    inline DateTime now() { return DateTime::currentDateTime(); }
    inline qint64 timestamp() { return DateTime::currentMSecsSinceEpoch(); }
    inline String formatTime(const DateTime& dt, const String& format = "yyyy-MM-dd hh:mm:ss") {
        return dt.toString(format);
    }
}

// 字符串工具函数
namespace StringUtils {
    inline String fromLocal8Bit(const char* str) { return QString::fromLocal8Bit(str); }
    inline String fromUtf8(const char* str) { return QString::fromUtf8(str); }
    inline ByteArray toLocal8Bit(const String& str) { return str.toLocal8Bit(); }
    inline ByteArray toUtf8(const String& str) { return str.toUtf8(); }
    
    inline bool isEmpty(const String& str) { return str.isEmpty(); }
    inline String trimmed(const String& str) { return str.trimmed(); }
    inline StringList split(const String& str, const String& sep) { return str.split(sep); }
}

// 线程工具
namespace ThreadUtils {
    inline void sleep(int ms) { QThread::msleep(ms); }
    inline QThread* currentThread() { return QThread::currentThread(); }
    inline bool isMainThread() { return currentThread() == QCoreApplication::instance()->thread(); }
}

#endif // COMMON_H

