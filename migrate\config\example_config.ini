# RSU设备控制系统配置文件示例
# 文件格式: INI
# 编码: UTF-8

[Common]
# 电子站国标编码
GantryGBID=G006036006003710010
# 电子站Hex码
GantryGBHex=380A26
# 取GantryGBID的前7位
RoadCode=G006036
# 取GantryGBID的前7位
RoadName=G006036
# 取GantryGBID的前8到16位
StationCode=006003710
# 取GantryGBID的前8到16位
StationName=006003710
# 固定配置
StationType=04
# 与GantryGBID相同
LaneNo=G006036006003710010
# 固定配置
LaneType=1
directory=D:\RSU\WorkSpace

[PSAMUrl]
# PSAM卡验证服务地址，每个电子站可能不一样
ip=http://************
port=8950
AuthUrlBak=http://************:8950/psam-auth-platform/v1/service/api/psam/auth
signUrlBak=http://************:8950//psam-auth-platform/v1/service/api/psam/sign
resultUrlBak=http://************:8950//psam-auth-platform/v1/service/api/psam/result
authListUrl=https://************:8890/api/v1/upload

[RSU]
Port=9527
Power=30
IP=127.0.0.1
Channel=1
TransferType=2
Used=1