/**
 * @file TCPCommunication.h
 * @brief ETC系统TCP通信模块
 * 
 * 本文件从Delphi UDevCommTCPNew.pas迁移而来，提供TCP客户端通信功能
 * 支持异步数据接收、连接管理、日志记录等功能
 */

#ifndef TCPCOMMUNICATION_H
#define TCPCOMMUNICATION_H

#include <QObject>
#include <QTcpSocket>
#include <QThread>
#include <QMutex>
#include <QTimer>
#include <QDateTime>
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QByteArray>
#include <QStandardPaths>

namespace ETC {

// 前向声明
class DevTCPClient;

/**
 * TCP客户端接收线程
 * 对应原Delphi代码中的TClientThread
 */
class ClientThread : public QThread {
    Q_OBJECT

public:
    explicit ClientThread(DevTCPClient* client, QObject* parent = nullptr);
    ~ClientThread();

    void setThreadFileName(const QString& fileName) { threadFileName = fileName; }
    void setTerminator(quint8 terminator) { terminator_ = terminator; }
    
    void stopThread();

protected:
    void run() override;

signals:
    void dataReceived(const QByteArray& data);
    void errorOccurred(const QString& error);

public slots:
    void handleSocketData();
    void handleSocketError();

private:
    void writeTCPLog(const QString& log);
    void handleDataNew(const QByteArray& data);

    DevTCPClient* commClient;
    QString threadFileName;
    quint8 terminator_;
    QMutex logMutex;
    bool shouldStop;
    
    static const int BUFFER_SIZE = 5000;
    QByteArray dataBuffer;
};

/**
 * TCP设备通信客户端
 * 对应原Delphi代码中的TDevTCPClient
 */
class DevTCPClient : public QObject {
    Q_OBJECT

public:
    explicit DevTCPClient(QObject* parent = nullptr);
    ~DevTCPClient();

    // 属性访问方法
    bool isActive() const { return active; }
    void setActive(bool value);
    
    QString getServerIP() const;
    void setServerIP(const QString& ip);
    
    int getPort() const;
    void setPort(int port);
    
    quint8 getTerminator() const { return terminator; }
    void setTerminator(quint8 terminator);
    
    QString getThreadFileName() const { return threadFileName; }
    void setThreadFileName(const QString& fileName);
    
    bool isConnected() const;

    // 数据发送方法
    void sendData(const QString& data);
    void sendDevData(const char* data, int dataLen);
    void sendDevData(const QByteArray& data);
    
    // 控制方法
    void stop();
    
    // 获取Socket对象（用于线程访问）
    QTcpSocket* getSocket() const { return tcpSocket; }

signals:
    void connected();
    void disconnected();
    void dataReceived(const QByteArray& data);
    void errorOccurred(const QString& error);

private slots:
    void onSocketConnected();
    void onSocketDisconnected();
    void onSocketError(QAbstractSocket::SocketError error);
    void onDataReceived(const QByteArray& data);
    void onThreadError(const QString& error);
    void checkConnection();

private:
    void handleInput(const char* data, int length);
    void writeLogFile(const QString& log);
    void createLogDirectory();

    QTcpSocket* tcpSocket;
    ClientThread* clientThread;
    
    bool active;
    QString threadFileName;
    quint8 terminator;
    QString serverIP;        // 服务器IP地址
    int serverPort;          // 服务器端口
    
    QTimer* connectionTimer;  // 连接检查定时器
    QMutex logMutex;
    
    static const int CONNECTION_CHECK_INTERVAL = 5000;  // 5秒检查一次连接
};

/**
 * TCP通信管理器
 * 提供多个TCP连接的统一管理
 */
class TCPCommunicationManager : public QObject {
    Q_OBJECT

public:
    static TCPCommunicationManager* getInstance();
    
    // 连接管理
    QString createConnection(const QString& connectionId, const QString& serverIP, int port);
    bool removeConnection(const QString& connectionId);
    DevTCPClient* getConnection(const QString& connectionId);
    QStringList getConnectionList() const;
    
    // 批量操作
    void connectAll();
    void disconnectAll();
    void stopAll();
    
    // 状态查询
    int getActiveConnectionCount() const;
    QStringList getActiveConnections() const;
    
    // 日志管理
    void setLogLevel(int level) { logLevel = level; }
    void setLogDirectory(const QString& dir);
    QString getLogDirectory() const { return logDirectory; }

signals:
    void connectionCreated(const QString& connectionId);
    void connectionRemoved(const QString& connectionId);
    void connectionStatusChanged(const QString& connectionId, bool connected);

private slots:
    void onConnectionStateChanged();

private:
    TCPCommunicationManager(QObject* parent = nullptr);
    ~TCPCommunicationManager();
    
    static TCPCommunicationManager* instance;
    static QMutex instanceMutex;
    
    QMap<QString, DevTCPClient*> connections;
    mutable QMutex connectionsMutex;
    
    QString logDirectory;
    int logLevel;
};

/**
 * TCP连接配置结构
 */
struct TCPConnectionConfig {
    QString connectionId;
    QString serverIP;
    int port;
    quint8 terminator;
    QString threadFileName;
    bool autoReconnect;
    int reconnectInterval;  // 重连间隔(毫秒)
    int maxReconnectAttempts;
    
    TCPConnectionConfig()
        : port(0)
        , terminator(0)
        , autoReconnect(true)
        , reconnectInterval(5000)
        , maxReconnectAttempts(3)
    {}
};

/**
 * TCP连接状态统计
 */
struct TCPConnectionStats {
    QString connectionId;
    QDateTime createTime;
    QDateTime lastConnectTime;
    QDateTime lastDisconnectTime;
    int connectCount;
    int disconnectCount;
    qint64 bytesSent;
    qint64 bytesReceived;
    int errorCount;
    QString lastError;
    
    TCPConnectionStats()
        : connectCount(0)
        , disconnectCount(0)
        , bytesSent(0)
        , bytesReceived(0)
        , errorCount(0)
    {}
};

/**
 * TCP连接工厂类
 * 提供不同类型TCP连接的创建
 */
class TCPConnectionFactory {
public:
    enum ConnectionType {
        StandardTCP,    // 标准TCP连接
        RSUDevice,      // RSU设备连接
        OBUDevice,      // OBU设备连接
        MonitorServer   // 监控服务器连接
    };
    
    static DevTCPClient* createConnection(ConnectionType type, 
                                         const TCPConnectionConfig& config,
                                         QObject* parent = nullptr);
    
    static TCPConnectionConfig getDefaultConfig(ConnectionType type);
    
private:
    static void setupRSUConnection(DevTCPClient* client, const TCPConnectionConfig& config);
    static void setupOBUConnection(DevTCPClient* client, const TCPConnectionConfig& config);
    static void setupMonitorConnection(DevTCPClient* client, const TCPConnectionConfig& config);
};

} // namespace ETC

#endif // TCPCOMMUNICATION_H
