{"system": {"name": "RSU设备控制系统", "version": "1.0.0", "description": "基于Qt 5.12的RSU路侧单元设备控制系统", "encoding": "UTF-8", "timezone": "Asia/Shanghai"}, "logging": {"level": "INFO", "console_output": true, "file_output": true, "max_file_size_mb": 50, "max_files": 10, "log_directory": "logs", "log_format": "[%{time yyyy-MM-dd hh:mm:ss.zzz}] [%{type}] [%{function}:%{line}] %{message}", "async_logging": true, "buffer_size": 1024}, "network": {"tcp_server": {"enabled": true, "host": "0.0.0.0", "port": 8000, "max_connections": 100, "timeout_seconds": 30, "keep_alive": true}, "udp_server": {"enabled": false, "host": "0.0.0.0", "port": 8001, "buffer_size": 4096}, "obu_communication": {"timeout_ms": 3000, "retry_count": 3, "retry_interval_ms": 1000, "heartbeat_interval_s": 30, "max_idle_time_s": 300}}, "security": {"psam_auth": {"enabled": true, "server_url": "http://127.0.0.1:8950/psam-auth-platform/v1/service/api", "endpoints": {"sign": "/psam/sign", "auth": "/psam/auth", "result": "/psam/result"}, "timeout_ms": 5000, "retry_count": 2}, "encryption": {"algorithm": "AES256", "key_version": 1, "enable_mac": true}}, "database": {"type": "file", "file_storage": {"data_directory": "data", "backup_directory": "backup", "auto_backup": true, "backup_interval_hours": 24, "max_backup_files": 30}}, "ui": {"main_window": {"width": 1200, "height": 800, "min_width": 800, "min_height": 600, "center_on_screen": true, "remember_position": true}, "theme": {"style": "default", "dark_mode": false, "font_family": "Microsoft YaHei", "font_size": 10}, "language": "zh_CN", "system_tray": {"enabled": true, "minimize_to_tray": true, "close_to_tray": false}}, "performance": {"max_threads": 8, "event_queue_size": 1000, "cache_size_mb": 100, "gc_interval_minutes": 10, "monitoring": {"enabled": true, "cpu_threshold": 80.0, "memory_threshold_mb": 500}}, "features": {"hot_reload_config": true, "plugin_support": false, "multilingual": true, "statistics": true, "export_data": true}}