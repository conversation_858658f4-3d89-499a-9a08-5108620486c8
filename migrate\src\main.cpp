/**
 * @file main.cpp
 * @brief RSU设备控制系统主程序入口
 * 
 * 控制台应用程序，专注于后台服务和设备通信
 * 支持命令行参数、配置文件管理、服务化运行
 * 
 * <AUTHOR>
 * @date 2024-12-20
 * @version 1.0.0
 * 
 * @note Qt版本: 5.12.12 (固定版本)
 * @note 应用类型: 控制台程序 (QCoreApplication)
 * @note 编码格式: UTF-8
 * @note 时区设置: 北京时间(GMT+8)
 */

#include "logging/LoggingSystem.h"
#include "core/EventFramework.h"
#include "core/OBUDevice.h"
#include "network/TCPCommunication.h"
#include "security/SecurityModule.h"
#include "core/ConfigManager.h"
#include <QCoreApplication>
#include <QDir>
#include <QStandardPaths>
#include <QTextCodec>
#include <QDateTime>
#include <QDebug>
#include <QCommandLineParser>
#include <QCommandLineOption>
#include <QJsonDocument>
#include <QJsonObject>
#include <QSettings>
#include <QTimer>
#include <QThread>
#include <iostream>
#include <exception>
#include <cstdio>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#include <locale>
#include <codecvt>
#endif

/**
 * 应用程序版本信息
 */
const QString APP_NAME = "RSU设备控制系统";
const QString APP_VERSION = "1.0.0";
const QString APP_COMPANY = "RSU设备系统开发组";
const QString APP_COPYRIGHT = "Copyright © 2024 ETC System Development Team";

/**
 * 函数声明
 */
void writeCachedLogsToFile(const QString& logFilePath);
void initializeAppDirectories();
void setupConsoleUTF8();
void setupTextCodec();
void setupCommandLineParser(QCoreApplication& app, QCommandLineParser& parser);
void showStartupInfo();
bool loadSystemConfig(const QCommandLineParser& parser);
void initializeServices();
void globalExceptionHandler();
void messageOutput(QtMsgType type, const QMessageLogContext& context, const QString& msg);

/**
 * 全局异常处理器 (控制台版本)
 */
void globalExceptionHandler() {
    try {
        throw;
    } catch (const std::exception& e) {
        QString errorMsg = QString("未处理的异常: %1").arg(e.what());
        qCritical() << errorMsg;
        fprintf(stderr, "严重错误: %s\n", errorMsg.toUtf8().constData());
        QCoreApplication::exit(-1);
    } catch (...) {
        QString errorMsg = "未知的严重异常";
        qCritical() << errorMsg;
        fprintf(stderr, "严重错误: %s\n", errorMsg.toUtf8().constData());
        QCoreApplication::exit(-1);
    }
}

/**
 * 控制台消息处理器
 */
void messageOutput(QtMsgType type, const QMessageLogContext &context, const QString &msg) {
    Q_UNUSED(context)
    
    // 始终输出到控制台和日志文件
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString levelStr;
    
    switch (type) {
        case QtDebugMsg:    levelStr = "[DEBUG]"; break;
        case QtInfoMsg:     levelStr = "[INFO] "; break;
        case QtWarningMsg:  levelStr = "[WARN] "; break;
        case QtCriticalMsg: levelStr = "[ERROR]"; break;
        case QtFatalMsg:    levelStr = "[FATAL]"; break;
    }
    
    QString formattedMsg = QString("%1 %2 %3").arg(timestamp, levelStr, msg);
    
    // 输出到控制台 - 使用UTF-8编码
    printf("%s\n", formattedMsg.toUtf8().constData());
    
    // 如果是致命错误，终止程序
    if (type == QtFatalMsg) {
        abort();
    }
}

/**
 * 将缓存的日志写入指定文件
 * 在配置加载失败时调用，将所有缓存的日志信息写入错误日志文件
 */
void writeCachedLogsToFile(const QString& logFilePath) {
    try {
        QFile logFile(logFilePath);
        if (!logFile.open(QIODevice::WriteOnly | QIODevice::Append)) {
            std::cerr << "无法创建错误日志文件: " << logFilePath.toStdString() << std::endl;
            return;
        }

        QTextStream stream(&logFile);
        stream.setCodec("UTF-8");

        // 写入文件头信息
        stream << "========================================" << endl;
        stream << "RSU设备控制系统 - 配置加载失败日志" << endl;
        stream << "时间: " << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << endl;
        stream << "应用程序: " << APP_NAME << endl;
        stream << "版本: " << APP_VERSION << endl;
        stream << "工作目录: " << QDir::currentPath() << endl;
        stream << "========================================" << endl;

        // 获取缓存的日志
        ETC::Logger* logger = ETC::Logger::getInstance();
        QStringList recentLogs = logger->getRecentLogs(1000); // 获取最近1000条日志

        if (recentLogs.isEmpty()) {
            stream << "没有缓存的日志信息" << endl;
        } else {
            stream << "缓存的日志信息 (共 " << recentLogs.size() << " 条):" << endl;
            stream << "----------------------------------------" << endl;

            for (const QString& logEntry : recentLogs) {
                stream << logEntry << endl;
            }
        }

        stream << "========================================" << endl;
        stream << "日志文件结束" << endl;

        logFile.close();

        std::cout << "缓存的日志已写入文件: " << logFilePath.toStdString() << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "写入错误日志文件时发生异常: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "写入错误日志文件时发生未知异常" << std::endl;
    }
}

/**
 * 初始化应用程序目录
 * 根据ConfigManager中的directory配置创建工作目录
 */
void initializeAppDirectories() {
    QStringList directories = {
        "logs",
        "config", 
        "data",
        "temp",
        "backup"
    };
    
    // 获取ConfigManager中配置的工作目录
    ConfigManager& configMgr = ConfigManager::getInstance();
    QString baseDirectory;
    
    if (configMgr.isLoaded()) {
        baseDirectory = configMgr.getConfig().directory;
    }
    
    // 如果配置中没有指定目录或目录为空，使用默认路径
    if (baseDirectory.isEmpty()) {
        baseDirectory = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
        qInfo() << "使用默认工作目录:" << baseDirectory;
    } else {
        qInfo() << "使用配置指定的工作目录:" << baseDirectory;
    }
    
    // 确保基础目录存在
    if (!QDir().mkpath(baseDirectory)) {
        qWarning() << "创建基础工作目录失败:" << baseDirectory;
        // 回退到默认目录
        baseDirectory = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
        qWarning() << "回退到默认目录:" << baseDirectory;
    }
    
    // 在基础目录下创建子目录
    for (const QString& dir : directories) {
        QString fullPath = QDir(baseDirectory).absoluteFilePath(dir);
        if (QDir().mkpath(fullPath)) {
            qDebug() << QString("创建目录:") << fullPath;
        } else {
            qWarning() << QString("创建目录失败:") << fullPath;
        }
    }
}

/**
 * 设置Windows控制台UTF-8支持
 */
void setupConsoleUTF8() {
#ifdef _WIN32
    // 设置控制台代码页为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
    
//    qDebug() << QString("Windows控制台UTF-8支持已设置");
#endif
}

/**
 * 设置字符编码
 */
void setupTextCodec() {
    // 设置本地字符编码为UTF-8
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
    //qDebug() << QString("字符编码设置为UTF-8");
}

/**
 * 解析命令行参数
 */
/**
 * @brief 设置命令行参数解析器
 * @param app 应用程序对象
 * @param parser 命令行解析器
 */
void setupCommandLineParser(QCoreApplication& app, QCommandLineParser& parser) {
    parser.setApplicationDescription("RSU设备控制系统\n\n支持多种启动方式:\n"
                                   "1. 工作目录启动: --workdir /path/to/directory\n"
                                   "2. 配置文件启动: --configFile /path/to/config.ini\n"
                                   "3. 键值对参数启动: --GantryGBID=xxx --RSUPort=9527 ...\n"
                                   "4. 无参数启动: 自动查找默认配置文件");
    parser.addHelpOption();
    parser.addVersionOption();

    // 添加命令行选项
    QCommandLineOption workdirOption("workdir", "指定工作目录路径，在该目录中查找config.json或config.ini", "directory");
    QCommandLineOption configFileOption("configFile", "指定配置文件路径(支持INI和JSON格式)", "file");
    QCommandLineOption configOption("config", "指定配置文件路径(支持INI和JSON格式，兼容旧版本)", "file");
    QCommandLineOption daemonOption("daemon", "以后台服务模式运行");
    QCommandLineOption debugOption("debug", "启用调试模式");
    QCommandLineOption portOption("port", "指定TCP服务端口", "port", "8080");
    QCommandLineOption logLevelOption("log-level", "设置日志级别(debug,info,warn,error)", "level", "info");
    
    // 添加配置相关选项（用于键值对参数模式）
    QCommandLineOption gantryGBIDOption("GantryGBID", "电子站国标编码", "value");
    QCommandLineOption gantryGBHexOption("GantryGBHex", "电子站Hex码", "value");
    QCommandLineOption roadCodeOption("RoadCode", "路段编码", "value");
    QCommandLineOption roadNameOption("RoadName", "路段名称", "value");
    QCommandLineOption stationCodeOption("StationCode", "站点编码", "value");
    QCommandLineOption stationNameOption("StationName", "站点名称", "value");
    QCommandLineOption stationTypeOption("StationType", "站点类型", "value");
    QCommandLineOption laneNoOption("LaneNo", "车道号", "value");
    QCommandLineOption laneTypeOption("LaneType", "车道类型", "value");
    QCommandLineOption directoryOption("directory", "工作目录路径", "path");
    
    QCommandLineOption psamUrlIpOption("PSAMUrlip", "PSAM服务IP地址", "value");
    QCommandLineOption psamPortOption("PSAMPort", "PSAM服务端口", "value");
    QCommandLineOption authUrlBakOption("AuthUrlBak", "认证URL备份", "value");
    QCommandLineOption signUrlBakOption("signUrlBak", "签名URL备份", "value");
    QCommandLineOption resultUrlBakOption("resultUrlBak", "结果URL备份", "value");
    QCommandLineOption authListUrlOption("authListUrl", "认证列表URL", "value");
    
    QCommandLineOption rsuPortOption("RSUPort", "RSU端口", "value");
    QCommandLineOption rsuPowerOption("RSUPower", "RSU功率", "value");
    QCommandLineOption rsuIpOption("RSUIp", "RSU IP地址", "value");
    QCommandLineOption rsuChannelOption("RSUChannel", "RSU信道", "value");
    QCommandLineOption rsuTransferTypeOption("RSUTransferType", "RSU传输类型", "value");
    QCommandLineOption rsuUsedOption("RSUUsed", "RSU使用状态", "value");
    
    parser.addOption(workdirOption);
    parser.addOption(configFileOption);
    parser.addOption(configOption);
    parser.addOption(daemonOption);
    parser.addOption(debugOption);
    parser.addOption(portOption);
    parser.addOption(logLevelOption);
    
    // 添加配置参数选项
    parser.addOption(gantryGBIDOption);
    parser.addOption(gantryGBHexOption);
    parser.addOption(roadCodeOption);
    parser.addOption(roadNameOption);
    parser.addOption(stationCodeOption);
    parser.addOption(stationNameOption);
    parser.addOption(stationTypeOption);
    parser.addOption(laneNoOption);
    parser.addOption(laneTypeOption);
    parser.addOption(directoryOption);
    
    parser.addOption(psamUrlIpOption);
    parser.addOption(psamPortOption);
    parser.addOption(authUrlBakOption);
    parser.addOption(signUrlBakOption);
    parser.addOption(resultUrlBakOption);
    parser.addOption(authListUrlOption);
    
    parser.addOption(rsuPortOption);
    parser.addOption(rsuPowerOption);
    parser.addOption(rsuIpOption);
    parser.addOption(rsuChannelOption);
    parser.addOption(rsuTransferTypeOption);
    parser.addOption(rsuUsedOption);
    
    // 解析命令行参数
    parser.process(app);
}

/**
 * 显示系统启动信息
 */
void showStartupInfo() {
    printf("===============================================\n");
    printf("    %s\n", APP_NAME.toUtf8().constData());
    printf("    版本: %s\n", APP_VERSION.toUtf8().constData());
    printf("    %s\n", APP_COPYRIGHT.toUtf8().constData());
    printf("    Qt版本: %s\n", QT_VERSION_STR);
    printf("    编译时间: %s %s\n", __DATE__, __TIME__);
    printf("===============================================\n");
    printf("正在初始化系统...\n");
}

/**
 * @brief 加载系统配置
 * @param parser 命令行解析器
 * @return 配置加载是否成功
 */
bool loadSystemConfig(const QCommandLineParser& parser) {
    ConfigManager& configMgr = ConfigManager::getInstance();

    // 1. 工作目录指定方式
    if (parser.isSet("workdir")) {
        QString workDir = parser.value("workdir");
        qInfo() << "使用指定的工作目录:" << workDir;

        if (!configMgr.loadFromWorkDir(workDir)) {
            qCritical() << "在工作目录中加载配置失败:" << workDir;
            qCritical() << "程序无法继续运行，请检查目录中是否存在config.json或config.ini文件";
            return false;
        }
    }
    // 2. 配置文件指定方式
    else if (parser.isSet("configFile") || parser.isSet("config")) {
        QString configFile = parser.isSet("configFile") ? parser.value("configFile") : parser.value("config");
        qInfo() << "使用指定的配置文件:" << configFile;

        if (!configMgr.loadFromFile(configFile)) {
            qCritical() << "配置文件加载失败:" << configFile;
            qCritical() << "程序无法继续运行，请检查配置文件路径和格式";
            return false;
        }
    }
    // 3. 检查是否有键值对参数
    else if (parser.isSet("GantryGBID") || parser.isSet("RSUPort") || parser.isSet("PSAMUrlip")) {
        qInfo() << "使用命令行键值对参数";

        QStringList args = QCoreApplication::arguments();
        if (!configMgr.loadFromCommandLine(args)) {
            qWarning() << "命令行参数解析失败，尝试加载默认配置";
            if (!configMgr.loadDefaultConfig()) {
                qWarning() << "未找到默认配置文件，使用内置默认配置";
            }
        }
    }
    // 4. 无参数启动，查找默认配置文件
    else {
        qInfo() << "无参数启动，在当前目录查找默认配置文件";

        if (!configMgr.loadDefaultConfig()) {
            qCritical() << "未找到默认配置文件，程序退出";
            qCritical() << "请提供配置文件或使用命令行参数";
            qCritical() << "使用 --help 查看帮助信息";
            return false;
        }
    }
    
    // 验证配置
    const ConfigManager::ConfigData& config = configMgr.getConfig();
    if (!config.validate()) {
        qCritical() << "配置数据验证失败，程序无法继续运行";
        return false;
    }
    
    // 打印配置信息
    config.printConfig();
    
    return true;
}

/**
 * 初始化系统服务
 */
void initializeServices() {
    qInfo() << "正在初始化系统服务...";
    
    try {
        // 1. 初始化事件管理器
        qInfo() << "初始化事件管理器...";
        // EventManager::getInstance()->start();
        
        // 2. 初始化TCP通信管理器
        qInfo() << "初始化TCP通信管理器...";
        // TCPCommunicationManager::getInstance()->initialize();
        
        // 3. 初始化设备管理器
        qInfo() << "初始化设备管理器...";
        // OBUDeviceManager::getInstance()->initialize();
        
        // 4. 初始化安全模块
        qInfo() << "初始化安全模块...";
        // SecurityModule::getInstance()->initialize();
        
        qInfo() << "系统服务初始化完成";
        
    } catch (const std::exception& e) {
        qCritical() << "系统服务初始化失败:" << e.what();
        throw;
    }
}

/**
 * 控制台应用程序主函数
 */
int main(int argc, char *argv[])
{
    // 创建控制台应用程序对象
    QCoreApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName(APP_NAME);
    app.setApplicationVersion(APP_VERSION);
    app.setOrganizationName(APP_COMPANY);
    app.setOrganizationDomain("etc.system.com");
    
    try {
        // 安装全局异常处理器
        std::set_terminate(globalExceptionHandler);
        
        // 设置控制台UTF-8支持
        setupConsoleUTF8();
        
        // 设置Qt消息处理器
        qInstallMessageHandler(messageOutput);
        
        // 显示系统启动信息
        showStartupInfo();
        
        // 设置字符编码
        setupTextCodec();
        
        // 解析命令行参数
        QCommandLineParser parser;
        setupCommandLineParser(app, parser);
        
        qDebug() << "=== RSU设备控制系统启动 ===";
        qDebug() << "应用程序:" << APP_NAME;
        qDebug() << "版本:" << APP_VERSION;
        qDebug() << "编译时间:" << __DATE__ << __TIME__;
        qDebug() << "Qt版本:" << QT_VERSION_STR;
        qDebug() << "启动时间:" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        qDebug() << "工作目录:" << QDir::currentPath();
        
        // 启用日志缓存模式（在目录创建之前）
        ETC::Logger::getInstance()->enableLogCache();
        qInfo() << "日志缓存模式已启用";

        // 加载系统配置
        qInfo() << "加载系统配置...";
        if (!loadSystemConfig(parser)) {
            qCritical() << "配置加载失败，程序退出";

            // 配置加载失败时，将缓存的日志写入当前目录的项目同名log文件
            QString errorLogFile = QDir::currentPath() + "/" + APP_NAME + ".log";
            qCritical() << "将缓存的日志写入文件:" << errorLogFile;

            // 写入缓存的日志到错误日志文件
            writeCachedLogsToFile(errorLogFile);

            return -1;
        }

        // 配置加载成功后，初始化应用程序目录
        qInfo() << "初始化应用程序目录...";
        initializeAppDirectories();

        // 禁用日志缓存模式并刷新缓存的日志到文件
        ETC::Logger::getInstance()->disableLogCache();
        qInfo() << "日志缓存已刷新到文件";
        
        // 初始化系统服务
        initializeServices();
        
        // 显示命令行参数信息
        if (parser.isSet("debug")) {
            qDebug() << "调试模式已启用";
        }
        
        if (parser.isSet("daemon")) {
            qInfo() << "以后台服务模式运行";
        }
        
        // 显示配置加载信息
        ConfigManager& configMgr = ConfigManager::getInstance();
        if (configMgr.isLoaded()) {
            qInfo() << "配置加载成功";
            if (!configMgr.getConfigFilePath().isEmpty()) {
                qInfo() << "配置文件:" << configMgr.getConfigFilePath();
            } else {
                qInfo() << "配置来源: 命令行参数";
            }
        }
        
        qInfo() << "=== 系统启动成功，进入运行状态 ===";
        qInfo() << "系统准备就绪，等待设备连接和交易处理...";
        
        // 设置优雅退出处理
        QTimer::singleShot(0, []() {
            std::cout << "系统运行中... (按 Ctrl+C 退出)" << std::endl;
        });
        
        // 进入事件循环
        int result = app.exec();
        
        qInfo() << "=== 系统正在关闭 ===";
        
        // 清理资源
        qInfo() << "清理系统资源...";
        // EventManager::getInstance()->stop();
        // LoggingSystem::getInstance()->cleanup();
        
        qInfo() << "应用程序退出，返回码:" << result;
        qInfo() << "=== RSU设备控制系统已安全退出 ===";
        
        return result;
        
    } catch (const std::exception& e) {
        QString errorMsg = QString("启动时发生严重错误: %1").arg(e.what());
        qCritical() << errorMsg;
        std::cerr << "启动错误: " << errorMsg.toStdString() << std::endl;
        return -1;
        
    } catch (...) {
        QString errorMsg = "启动时发生未知严重错误";
        qCritical() << errorMsg;
        std::cerr << "启动错误: " << errorMsg.toStdString() << std::endl;
        return -1;
    }
}
