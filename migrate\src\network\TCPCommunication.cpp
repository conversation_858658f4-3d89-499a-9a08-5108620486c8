/**
 * @file TCPCommunication.cpp
 * @brief ETC系统TCP通信模块实现
 */

#include "network/TCPCommunication.h"
#include <QDebug>
#include <QHostAddress>
#include <QCoreApplication>
#include <QStandardPaths>
#include <QMutexLocker>
#include <algorithm>

namespace ETC {

// ClientThread 实现
ClientThread::ClientThread(DevTCPClient* client, QObject* parent)
    : QThread(parent)
    , commClient(client)
    , terminator_(0)
    , shouldStop(false)
{
    dataBuffer.reserve(BUFFER_SIZE);
}

ClientThread::~ClientThread() {
    stopThread();
}

void ClientThread::stopThread() {
    shouldStop = true;
    if (isRunning()) {
        quit();
        if (!wait(3000)) {  // 等待3秒
            terminate();
            wait(1000);
        }
    }
}

void ClientThread::run() {
    writeTCPLog("TCP接收线程启动");
    
    exec();  // 进入事件循环
    
    writeTCPLog("TCP接收线程结束");
}

void ClientThread::handleSocketData() {
    if (!commClient || shouldStop) {
        return;
    }
    
    QTcpSocket* socket = commClient->getSocket();
    if (!socket || socket->state() != QAbstractSocket::ConnectedState) {
        return;
    }
    
    QByteArray data = socket->readAll();
    if (!data.isEmpty()) {
        writeTCPLog(QString("通信连接收到数据，长度：%1").arg(data.size()));
        handleDataNew(data);
    }
}

void ClientThread::handleSocketError() {
    if (!commClient || shouldStop) {
        return;
    }
    
    QTcpSocket* socket = commClient->getSocket();
    if (socket) {
        QString error = socket->errorString();
        writeTCPLog(QString("通信连接错误：%1").arg(error));
        emit errorOccurred(error);
    }
}

void ClientThread::handleDataNew(const QByteArray& data) {
    if (data.isEmpty() || shouldStop) {
        return;
    }
    
    try {
        emit dataReceived(data);
    } catch (const std::exception& e) {
        writeTCPLog(QString("处理接收数据时发生异常：%1").arg(e.what()));
    }
}

void ClientThread::writeTCPLog(const QString& log) {
    QMutexLocker locker(&logMutex);
    
    QString fileName = QDir::currentPath() + 
                      QString("/Log/TCPThread%1%2.log")
                      .arg(threadFileName)
                      .arg(QDate::currentDate().toString("yyyy-MM-dd"));
    
    QString logStr = QString("%1: %2")
                    .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                    .arg(log);
    
    QDir logDir = QFileInfo(fileName).absoluteDir();
    if (!logDir.exists()) {
        logDir.mkpath(".");
    }
    
    QFile file(fileName);
    if (file.open(QIODevice::WriteOnly | QIODevice::Append)) {
        QTextStream stream(&file);
        stream.setCodec("UTF-8");
        stream << logStr << endl;
        file.close();
    }
}

// DevTCPClient 实现
DevTCPClient::DevTCPClient(QObject* parent)
    : QObject(parent)
    , tcpSocket(nullptr)
    , clientThread(nullptr)
    , active(false)
    , terminator(0)
    , serverIP("127.0.0.1")
    , serverPort(8000)
{
    tcpSocket = new QTcpSocket(this);
    
    // 连接信号槽
    connect(tcpSocket, &QTcpSocket::connected, this, &DevTCPClient::onSocketConnected);
    connect(tcpSocket, &QTcpSocket::disconnected, this, &DevTCPClient::onSocketDisconnected);
    connect(tcpSocket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::error),
            this, &DevTCPClient::onSocketError);
    
    // 创建连接检查定时器
    connectionTimer = new QTimer(this);
    connectionTimer->setInterval(CONNECTION_CHECK_INTERVAL);
    connect(connectionTimer, &QTimer::timeout, this, &DevTCPClient::checkConnection);
    
    createLogDirectory();
}

DevTCPClient::~DevTCPClient() {
    writeLogFile("销毁TCP客户端");
    stop();
}

void DevTCPClient::setActive(bool value) {
    if (value == active) {
        return;
    }
    
    if (serverIP.isEmpty()) {
        active = false;
        return;
    }
    
    if (value) {
        try {
            writeLogFile("尝试销毁线程");
            if (clientThread) {
                clientThread->stopThread();
                clientThread->deleteLater();
                clientThread = nullptr;
            }
            
            writeLogFile("尝试关闭连接");
            if (tcpSocket->state() != QAbstractSocket::UnconnectedState) {
                tcpSocket->disconnectFromHost();
                if (tcpSocket->state() != QAbstractSocket::UnconnectedState) {
                    tcpSocket->waitForDisconnected(3000);
                }
            }
            
            active = false;
            
            writeLogFile(QString("尝试连接到 %1:%2").arg(serverIP).arg(serverPort));
            tcpSocket->connectToHost(serverIP, serverPort);
            
            if (tcpSocket->waitForConnected(5000)) {
                writeLogFile("连接成功");
                
                writeLogFile("创建通信线程");
                clientThread = new ClientThread(this, this);
                clientThread->setThreadFileName(threadFileName);
                clientThread->setTerminator(terminator);
                
                // 连接线程信号
                connect(clientThread, &ClientThread::dataReceived, this, &DevTCPClient::onDataReceived);
                connect(clientThread, &ClientThread::errorOccurred, this, &DevTCPClient::onThreadError);
                
                // 连接Socket的readyRead信号到线程处理方法
                connect(tcpSocket, &QTcpSocket::readyRead, clientThread, &ClientThread::handleSocketData);
                connect(tcpSocket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::error),
                        clientThread, &ClientThread::handleSocketError);
                
                clientThread->start();
                active = true;
                connectionTimer->start();
            } else {
                writeLogFile(QString("连接失败: %1").arg(tcpSocket->errorString()));
                active = false;
            }
            
        } catch (const std::exception& e) {
            active = false;
            writeLogFile(QString("通信连接错误：%1").arg(e.what()));
        }
    } else {
        writeLogFile("尝试销毁线程");
        if (clientThread) {
            clientThread->stopThread();
            clientThread->deleteLater();
            clientThread = nullptr;
        }
        
        writeLogFile("尝试关闭连接");
        connectionTimer->stop();
        if (tcpSocket->state() != QAbstractSocket::UnconnectedState) {
            tcpSocket->disconnectFromHost();
            if (tcpSocket->state() != QAbstractSocket::UnconnectedState) {
                tcpSocket->waitForDisconnected(3000);
            }
        }
        active = false;
    }
}

QString DevTCPClient::getServerIP() const {
    return serverIP;
}

void DevTCPClient::setServerIP(const QString& ip) {
    serverIP = ip;
    writeLogFile(QString("设置服务器IP: %1").arg(ip));
}

int DevTCPClient::getPort() const {
    return serverPort;
}

void DevTCPClient::setPort(int port) {
    serverPort = port;
    writeLogFile(QString("设置服务器端口: %1").arg(port));
}

void DevTCPClient::setThreadFileName(const QString& fileName) {
    threadFileName = fileName;
    writeLogFile(QString("设置线程文件名: %1").arg(fileName));
}

void DevTCPClient::setTerminator(quint8 terminator) {
    this->terminator = terminator;
    writeLogFile(QString("设置终止符: 0x%1").arg(terminator, 2, 16, QChar('0')));
}

bool DevTCPClient::isConnected() const {
    return tcpSocket && tcpSocket->state() == QAbstractSocket::ConnectedState;
}

void DevTCPClient::sendData(const QString& data) {
    if (active && isConnected()) {
        QByteArray dataBytes = data.toUtf8();
        dataBytes.append('\n');  // 添加换行符
        qint64 written = tcpSocket->write(dataBytes);
        if (written > 0) {
            writeLogFile(QString("发送字符串数据，长度: %1").arg(written));
        }
    }
}

void DevTCPClient::sendDevData(const char* data, int dataLen) {
    if (active && isConnected()) {
        qint64 written = tcpSocket->write(data, dataLen);
        if (written > 0) {
            writeLogFile(QString("通信连接发送数据，长度: %1").arg(written));
        } else {
            writeLogFile(QString("发送数据失败: %1").arg(tcpSocket->errorString()));
        }
    }
}

void DevTCPClient::sendDevData(const QByteArray& data) {
    sendDevData(data.constData(), data.size());
}

void DevTCPClient::stop() {
    if (clientThread) {
        clientThread->stopThread();
        clientThread->deleteLater();
        clientThread = nullptr;
    }
    
    if (active) {
        setActive(false);
    }
    
    disconnect(this, &DevTCPClient::dataReceived, nullptr, nullptr);
}

void DevTCPClient::onSocketConnected() {
    active = true;
    writeLogFile("Socket连接成功");
    emit connected();
}

void DevTCPClient::onSocketDisconnected() {
    active = false;
    writeLogFile("Socket连接断开");
    emit disconnected();
}

void DevTCPClient::onSocketError(QAbstractSocket::SocketError error) {
    Q_UNUSED(error)
    QString errorStr = tcpSocket->errorString();
    writeLogFile(QString("Socket错误: %1").arg(errorStr));
    emit errorOccurred(errorStr);
}

void DevTCPClient::onDataReceived(const QByteArray& data) {
    handleInput(data.constData(), data.size());
    emit dataReceived(data);
}

void DevTCPClient::onThreadError(const QString& error) {
    writeLogFile(QString("线程错误: %1").arg(error));
    emit errorOccurred(error);
}

void DevTCPClient::checkConnection() {
    if (active && !isConnected()) {
        writeLogFile("检测到连接断开，尝试重连");
        setActive(false);
        QTimer::singleShot(1000, this, [this]() {
            setActive(true);
        });
    }
}

void DevTCPClient::handleInput(const char* data, int length) {
    Q_UNUSED(data)
    Q_UNUSED(length)
    // 这里可以添加具体的数据处理逻辑
    // 原Delphi代码中调用OnReceiveData事件，在Qt中通过信号槽实现
}

void DevTCPClient::writeLogFile(const QString& log) {
    QMutexLocker locker(&logMutex);
    
    QString fileName = QDir::currentPath() + 
                      QString("/Log/TCP%1%2.log")
                      .arg(threadFileName)
                      .arg(QDate::currentDate().toString("yyyy-MM-dd"));
    
    QString logStr = QString("%1: %2")
                    .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                    .arg(log);
    
    QFile file(fileName);
    if (file.open(QIODevice::WriteOnly | QIODevice::Append)) {
        QTextStream stream(&file);
        stream.setCodec("UTF-8");
        stream << logStr << endl;
        file.close();
    }
}

void DevTCPClient::createLogDirectory() {
    QString logPath = QDir::currentPath() + "/Log";
    QDir logDir(logPath);
    if (!logDir.exists()) {
        logDir.mkpath(".");
    }
}

// TCPCommunicationManager 实现
TCPCommunicationManager* TCPCommunicationManager::instance = nullptr;
QMutex TCPCommunicationManager::instanceMutex;

TCPCommunicationManager::TCPCommunicationManager(QObject* parent)
    : QObject(parent)
    , logLevel(1)
{
    logDirectory = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs";
    QDir().mkpath(logDirectory);
}

TCPCommunicationManager::~TCPCommunicationManager() {
    stopAll();
}

TCPCommunicationManager* TCPCommunicationManager::getInstance() {
    QMutexLocker locker(&instanceMutex);
    if (!instance) {
        instance = new TCPCommunicationManager();
    }
    return instance;
}

QString TCPCommunicationManager::createConnection(const QString& connectionId, 
                                                 const QString& serverIP, 
                                                 int port) {
    QMutexLocker locker(&connectionsMutex);
    
    if (connections.contains(connectionId)) {
        return QString("连接ID '%1' 已存在").arg(connectionId);
    }
    
    DevTCPClient* client = new DevTCPClient(this);
    client->setThreadFileName(connectionId);
    
    // 连接信号
    connect(client, &DevTCPClient::connected, this, &TCPCommunicationManager::onConnectionStateChanged);
    connect(client, &DevTCPClient::disconnected, this, &TCPCommunicationManager::onConnectionStateChanged);
    
    connections.insert(connectionId, client);
    
    emit connectionCreated(connectionId);
    return QString();  // 成功返回空字符串
}

bool TCPCommunicationManager::removeConnection(const QString& connectionId) {
    QMutexLocker locker(&connectionsMutex);
    
    if (!connections.contains(connectionId)) {
        return false;
    }
    
    DevTCPClient* client = connections.take(connectionId);
    client->stop();
    client->deleteLater();
    
    emit connectionRemoved(connectionId);
    return true;
}

DevTCPClient* TCPCommunicationManager::getConnection(const QString& connectionId) {
    QMutexLocker locker(&connectionsMutex);
    return connections.value(connectionId, nullptr);
}

QStringList TCPCommunicationManager::getConnectionList() const {
    QMutexLocker locker(&connectionsMutex);
    return connections.keys();
}

void TCPCommunicationManager::connectAll() {
    QMutexLocker locker(&connectionsMutex);
    for (DevTCPClient* client : connections.values()) {
        if (!client->isActive()) {
            client->setActive(true);
        }
    }
}

void TCPCommunicationManager::disconnectAll() {
    QMutexLocker locker(&connectionsMutex);
    for (DevTCPClient* client : connections.values()) {
        if (client->isActive()) {
            client->setActive(false);
        }
    }
}

void TCPCommunicationManager::stopAll() {
    QMutexLocker locker(&connectionsMutex);
    for (DevTCPClient* client : connections.values()) {
        client->stop();
    }
    qDeleteAll(connections.values());
    connections.clear();
}

int TCPCommunicationManager::getActiveConnectionCount() const {
    QMutexLocker locker(&connectionsMutex);
    int count = 0;
    for (DevTCPClient* client : connections.values()) {
        if (client->isConnected()) {
            count++;
        }
    }
    return count;
}

QStringList TCPCommunicationManager::getActiveConnections() const {
    QMutexLocker locker(&connectionsMutex);
    QStringList activeList;
    for (auto it = connections.begin(); it != connections.end(); ++it) {
        if (it.value()->isConnected()) {
            activeList << it.key();
        }
    }
    return activeList;
}

void TCPCommunicationManager::setLogDirectory(const QString& dir) {
    logDirectory = dir;
    QDir().mkpath(logDirectory);
}

void TCPCommunicationManager::onConnectionStateChanged() {
    DevTCPClient* client = qobject_cast<DevTCPClient*>(sender());
    if (!client) {
        return;
    }
    
    // 查找连接ID
    QMutexLocker locker(&connectionsMutex);
    for (auto it = connections.begin(); it != connections.end(); ++it) {
        if (it.value() == client) {
            emit connectionStatusChanged(it.key(), client->isConnected());
            break;
        }
    }
}

// TCPConnectionFactory 实现
DevTCPClient* TCPConnectionFactory::createConnection(ConnectionType type, 
                                                    const TCPConnectionConfig& config,
                                                    QObject* parent) {
    DevTCPClient* client = new DevTCPClient(parent);
    client->setThreadFileName(config.threadFileName);
    client->setTerminator(config.terminator);
    
    switch (type) {
        case RSUDevice:
            setupRSUConnection(client, config);
            break;
        case OBUDevice:
            setupOBUConnection(client, config);
            break;
        case MonitorServer:
            setupMonitorConnection(client, config);
            break;
        case StandardTCP:
        default:
            // 标准TCP连接，使用默认设置
            break;
    }
    
    return client;
}

TCPConnectionConfig TCPConnectionFactory::getDefaultConfig(ConnectionType type) {
    TCPConnectionConfig config;
    
    switch (type) {
        case RSUDevice:
            config.port = 8001;
            config.terminator = 0x00;
            config.threadFileName = "RSU";
            config.autoReconnect = true;
            config.reconnectInterval = 3000;
            config.maxReconnectAttempts = 5;
            break;
            
        case OBUDevice:
            config.port = 8002;
            config.terminator = 0x0D;
            config.threadFileName = "OBU";
            config.autoReconnect = true;
            config.reconnectInterval = 5000;
            config.maxReconnectAttempts = 3;
            break;
            
        case MonitorServer:
            config.port = 9001;
            config.terminator = 0x0A;
            config.threadFileName = "Monitor";
            config.autoReconnect = false;
            config.reconnectInterval = 10000;
            config.maxReconnectAttempts = 1;
            break;
            
        case StandardTCP:
        default:
            config.port = 8000;
            config.terminator = 0x00;
            config.threadFileName = "Standard";
            config.autoReconnect = true;
            config.reconnectInterval = 5000;
            config.maxReconnectAttempts = 3;
            break;
    }
    
    return config;
}

void TCPConnectionFactory::setupRSUConnection(DevTCPClient* client, const TCPConnectionConfig& config) {
    Q_UNUSED(client)
    Q_UNUSED(config)
    // RSU设备特定的连接设置
    // 可以在这里添加RSU设备特有的配置
}

void TCPConnectionFactory::setupOBUConnection(DevTCPClient* client, const TCPConnectionConfig& config) {
    Q_UNUSED(client)
    Q_UNUSED(config)
    // OBU设备特定的连接设置
    // 可以在这里添加OBU设备特有的配置
}

void TCPConnectionFactory::setupMonitorConnection(DevTCPClient* client, const TCPConnectionConfig& config) {
    Q_UNUSED(client)
    Q_UNUSED(config)
    // 监控服务器特定的连接设置
    // 可以在这里添加监控服务器特有的配置
}

} // namespace ETC
