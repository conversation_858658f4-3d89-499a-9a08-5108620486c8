/**
 * @file ConfigManager.h
 * @brief RSU设备控制系统配置管理器
 * 
 * 支持多种配置源：
 * - INI格式配置文件
 * - JSON格式配置文件
 * - 命令行键值对参数
 * - 默认配置文件查找
 * 
 * <AUTHOR>
 * @date 2024-12-20
 * @version 1.0.0
 */

#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QString>
#include <QVariant>
#include <QMap>
#include <QStringList>

/**
 * @brief 配置管理器类
 * 
 * 负责从多种源加载和管理系统配置参数
 */
class ConfigManager
{
public:
    /**
     * @brief 配置数据结构
     */
    struct ConfigData {
        // Common配置
        QString GantryGBID;          // 电子站国标编码
        QString GantryGBHex;         // 电子站Hex码
        QString RoadCode;            // 路段编码(取GantryGBID前7位)
        QString RoadName;            // 路段名称
        QString StationCode;         // 站点编码(取GantryGBID第8-16位)
        QString StationName;         // 站点名称
        QString StationType;         // 站点类型(固定04)
        QString LaneNo;              // 车道号(与GantryGBID相同)
        QString LaneType;            // 车道类型(固定1)
        QString directory;           // 工作目录路径
        
        // PSAM URL配置
        QString PSAMUrlIp;           // PSAM服务IP
        QString PSAMUrlPort;         // PSAM服务端口
        QString AuthUrlBak;          // 认证URL备份
        QString SignUrlBak;          // 签名URL备份
        QString ResultUrlBak;        // 结果URL备份
        QString AuthListUrl;         // 认证列表URL
        
        // RSU配置
        QString RSUPort;             // RSU端口
        QString RSUPower;            // RSU功率
        QString RSUIp;               // RSU IP地址
        QString RSUChannel;          // RSU信道
        QString RSUTransferType;     // RSU传输类型
        QString RSUUsed;             // RSU使用状态
        
        /**
         * @brief 构造函数，设置默认值
         */
        ConfigData();
        
        /**
         * @brief 验证配置数据完整性
         * @return 验证是否通过
         */
        bool validate() const;
        
        /**
         * @brief 打印配置信息
         */
        void printConfig() const;
    };
    
    /**
     * @brief 获取单例实例
     * @return ConfigManager实例引用
     */
    static ConfigManager& getInstance();
    
    /**
     * @brief 从配置文件加载配置
     * @param configFilePath 配置文件路径
     * @return 加载是否成功
     */
    bool loadFromFile(const QString& configFilePath);
    
    /**
     * @brief 从命令行参数加载配置
     * @param args 命令行参数列表
     * @return 加载是否成功
     */
    bool loadFromCommandLine(const QStringList& args);
    
    /**
     * @brief 查找并加载默认配置文件
     * @return 加载是否成功
     */
    bool loadDefaultConfig();
    
    /**
     * @brief 获取配置数据
     * @return 配置数据结构
     */
    const ConfigData& getConfig() const { return m_config; }
    
    /**
     * @brief 获取配置值
     * @param key 配置键名
     * @param defaultValue 默认值
     * @return 配置值
     */
    QVariant getValue(const QString& key, const QVariant& defaultValue = QVariant()) const;
    
    /**
     * @brief 设置配置值
     * @param key 配置键名
     * @param value 配置值
     */
    void setValue(const QString& key, const QVariant& value);
    
    /**
     * @brief 检查配置是否已加载
     * @return 是否已加载
     */
    bool isLoaded() const { return m_loaded; }
    
    /**
     * @brief 获取配置文件路径
     * @return 配置文件路径
     */
    QString getConfigFilePath() const { return m_configFilePath; }
    
private:
    ConfigManager() = default;
    ~ConfigManager() = default;
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;
    
    /**
     * @brief 从INI文件加载配置
     * @param filePath INI文件路径
     * @return 加载是否成功
     */
    bool loadFromIniFile(const QString& filePath);
    
    /**
     * @brief 从JSON文件加载配置
     * @param filePath JSON文件路径
     * @return 加载是否成功
     */
    bool loadFromJsonFile(const QString& filePath);
    
    /**
     * @brief 解析命令行键值对参数
     * @param args 参数列表
     * @return 解析的键值对映射
     */
    QMap<QString, QString> parseKeyValueArgs(const QStringList& args);
    
    /**
     * @brief 应用键值对到配置数据
     * @param keyValues 键值对映射
     */
    void applyKeyValues(const QMap<QString, QString>& keyValues);
    
    /**
     * @brief 从配置数据生成路段和站点信息
     */
    void generateDerivedFields();
    
private:
    ConfigData m_config;              // 配置数据
    QMap<QString, QVariant> m_values; // 额外配置值
    bool m_loaded = false;            // 是否已加载
    QString m_configFilePath;         // 配置文件路径
};

#endif // CONFIGMANAGER_H