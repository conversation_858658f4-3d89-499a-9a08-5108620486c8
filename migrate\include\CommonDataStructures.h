/**
 * @file CommonDataStructures.h
 * @brief ETC RSU系统公共数据结构定义
 * @details 包含从Delphi 12迁移的所有公共数据结构，确保与原始定义的二进制兼容性
 * <AUTHOR> ETC开发团队
 * @date 2024-12-20
 * @version 1.0
 * @copyright 2024 ETC RSU System. All rights reserved.
 */

#ifndef COMMONDATASTRUCTURES_H
#define COMMONDATASTRUCTURES_H

#include <QString>
#include <QRegExp>
#include <cstring>
#include <cstdint>
#include <QStringList>

namespace ETC {

// Delphi类型到C++类型的映射
using AnsiChar = char;              // Delphi AnsiChar -> C++ char
using Word = uint16_t;              // Delphi Word -> C++ uint16_t
using Integer = int32_t;            // Delphi Integer -> C++ int32_t

/**
 * @struct TPSAMStatus
 * @brief PSAM状态信息结构体
 * @details 对应Delphi中的TPSAMStatus，包含PSAM的状态信息
 * 
 * 原始Delphi定义：
 * TPSAMStatus = packed record
 *   Channel: Byte;
 *   Status: Byte;
 *   Authorized: Byte;
 * end;
 */
struct __attribute__((packed)) TPSAMStatus {
    uint8_t channel;        // Channel: PSAM通道
    uint8_t status;         // Status: PSAM状态码
    uint8_t authorized;     // Authorized: 授权状态
    
    TPSAMStatus() {
        memset(this, 0, sizeof(TPSAMStatus));
    }
};

/**
 * @struct TAntennaStatus
 * @brief 天线状态信息结构体
 * @details 对应Delphi中的TAntennaStatus，包含天线的状态信息
 * 
 * 原始Delphi定义：
 * TAntennaStatus = packed record
 *   ID: Byte;
 *   Status: Byte;
 *   Channel: Byte;
 *   Power: Byte;
 * end;
 */
struct __attribute__((packed)) TAntennaStatus {
    uint8_t id;             // ID: 天线ID
    uint8_t status;         // Status: 天线状态
    uint8_t channel;        // Channel: 天线通道
    uint8_t power;          // Power: 天线功率
    
    TAntennaStatus() {
        memset(this, 0, sizeof(TAntennaStatus));
    }
};

/**
 * @struct TPSAMInfo
 * @brief PSAM卡信息结构体
 * @details 对应Delphi中的TPSAMInfo，包含PSAM卡的基本信息
 */
struct __attribute__((packed)) TPSAMInfo {
    uint8_t psamChannel;        // PSAM通道编号
    uint8_t psamStatus;         // PSAM状态
    uint32_t psamId;           // PSAM卡号
    uint16_t psamVersion;      // PSAM版本
    uint8_t psamManufacturer;  // PSAM制造商代码
    uint8_t psamType;          // PSAM类型
    uint8_t algorithmId;       // 算法标识
    uint8_t keyVersion;        // 密钥版本
    uint8_t reserved[6];       // 保留字段
    
    TPSAMInfo() {
        memset(this, 0, sizeof(TPSAMInfo));
    }
};

/**
 * @struct TB0Info
 * @brief RSU B0信息结构体
 * @details 对应Delphi中的TB0Info，完全按照原始定义迁移
 * 
 * 原始Delphi定义：
 * TB0Info = packed record
 *   RSCTL: Byte;
 *   ErrorCode: Byte;
 *   RSUStatus: Byte;
 *   PSAMNum1: Byte;
 *   PSAMInfo1: array[0..7] of TPSAMInfo;
 *   RSUAlgId: Byte;
 *   RSUManuID: Byte;
 *   RSUID: array[0..2] of Byte;
 *   RSUVersion: Word;
 *   workstatus: Byte;
 *   FlagID: array[0..2] of Byte;
 *   Reserved: array[0..3] of Byte;
 * end;
 */
struct __attribute__((packed)) TB0Info {
    uint8_t rsctl;              // RSCTL: RSU控制字节
    uint8_t errorCode;          // ErrorCode: 错误代码
    uint8_t rsuStatus;          // RSUStatus: RSU状态
    uint8_t psamNum1;           // PSAMNum1: PSAM数量
    TPSAMInfo psamInfo1[8];     // PSAMInfo1: PSAM信息数组[0..7]
    uint8_t rsuAlgId;           // RSUAlgId: RSU算法标识
    uint8_t rsuManuID;          // RSUManuID: RSU制造商ID
    uint8_t rsuid[3];           // RSUID: RSU标识[0..2]
    uint16_t rsuVersion;        // RSUVersion: RSU版本(Word)
    uint8_t workstatus;         // workstatus: 工作状态
    uint8_t flagID[3];          // FlagID: 标识ID[0..2]
    uint8_t reserved[4];        // Reserved: 保留字段[0..3]
    
    TB0Info() {
        memset(this, 0, sizeof(TB0Info));
    }
    
    /**
     * @brief 获取指定索引的PSAM信息
     * @param index PSAM索引(0-7)
     * @return PSAM信息，如果索引无效返回默认值
     */
    const TPSAMInfo& getPSAMInfo(int index) const {
        static const TPSAMInfo defaultPsam;
        if (index >= 0 && index < 8) {
            return psamInfo1[index];
        }
        return defaultPsam;
    }
    
    /**
     * @brief 设置指定索引的PSAM信息
     * @param index PSAM索引(0-7)
     * @param info PSAM信息
     */
    void setPSAMInfo(int index, const TPSAMInfo& info) {
        if (index >= 0 && index < 8) {
            psamInfo1[index] = info;
        }
    }
    
    /**
     * @brief 获取RSU ID的字符串表示
     * @return RSU ID字符串
     */
    QString getRSUIdString() const {
        return QString("%1%2%3")
            .arg(rsuid[0], 2, 16, QChar('0'))
            .arg(rsuid[1], 2, 16, QChar('0'))
            .arg(rsuid[2], 2, 16, QChar('0'));
    }
    
    /**
     * @brief 获取标识ID的字符串表示
     * @return 标识ID字符串
     */
    QString getFlagIdString() const {
        return QString("%1%2%3")
            .arg(flagID[0], 2, 16, QChar('0'))
            .arg(flagID[1], 2, 16, QChar('0'))
            .arg(flagID[2], 2, 16, QChar('0'));
    }
};

/**
 * @struct TBAPSAMInfo
 * @brief BA事件中的PSAM卡信息结构体
 * @details 对应Delphi中的TBAPSAMInfo，包含PSAM卡的详细信息
 * 
 * 原始Delphi定义：
 * TBAPSAMInfo = packed record
 *   ChannelID: Byte;
 *   TerminalNo: array[0..5] of Byte;
 *   SerialNo: array[0..9] of Byte;
 *   Version: Byte;
 *   KeyCardType: Byte;
 *   AppAreaID: array[0..7] of Byte;
 *   RandomNumber: array[0..7] of Byte;
 * end;
 */
struct __attribute__((packed)) TBAPSAMInfo {
    uint8_t channelID;          // ChannelID: 通道ID
    uint8_t terminalNo[6];      // TerminalNo: 终端号[0..5]
    uint8_t serialNo[10];       // SerialNo: 序列号[0..9]
    uint8_t version;            // Version: 版本
    uint8_t keyCardType;        // KeyCardType: 密钥卡类型
    uint8_t appAreaID[8];       // AppAreaID: 应用区域ID[0..7]
    uint8_t randomNumber[8];    // RandomNumber: 随机数[0..7]
    
    TBAPSAMInfo() {
        memset(this, 0, sizeof(TBAPSAMInfo));
    }
    
    /**
     * @brief 获取终端号的字符串表示
     * @return 终端号十六进制字符串
     */
    QString getTerminalNoString() const {
        QString result;
        for (int i = 0; i < 6; ++i) {
            result += QString("%1").arg(terminalNo[i], 2, 16, QChar('0'));
        }
        return result.toUpper();
    }
    
    /**
     * @brief 获取序列号的字符串表示
     * @return 序列号十六进制字符串
     */
    QString getSerialNoString() const {
        QString result;
        for (int i = 0; i < 10; ++i) {
            result += QString("%1").arg(serialNo[i], 2, 16, QChar('0'));
        }
        return result.toUpper();
    }
    
    /**
     * @brief 获取应用区域ID的字符串表示
     * @return 应用区域ID十六进制字符串
     */
    QString getAppAreaIdString() const {
        QString result;
        for (int i = 0; i < 8; ++i) {
            result += QString("%1").arg(appAreaID[i], 2, 16, QChar('0'));
        }
        return result.toUpper();
    }
    
    /**
     * @brief 获取随机数的字符串表示
     * @return 随机数十六进制字符串
     */
    QString getRandomNumberString() const {
        QString result;
        for (int i = 0; i < 8; ++i) {
            result += QString("%1").arg(randomNumber[i], 2, 16, QChar('0'));
        }
        return result.toUpper();
    }
    
    /**
     * @brief 设置终端号
     * @param terminalStr 终端号字符串(十六进制)
     */
    void setTerminalNo(const QString& terminalStr) {
        QString cleanStr = QString(terminalStr).remove(QRegExp("[^0-9A-Fa-f]"));
        if (cleanStr.length() >= 12) {
            for (int i = 0; i < 6; ++i) {
                bool ok;
                terminalNo[i] = cleanStr.mid(i * 2, 2).toUInt(&ok, 16);
                if (!ok) terminalNo[i] = 0;
            }
        }
    }
    
    /**
     * @brief 设置序列号
     * @param serialStr 序列号字符串(十六进制)
     */
    void setSerialNo(const QString& serialStr) {
        QString cleanStr = QString(serialStr).remove(QRegExp("[^0-9A-Fa-f]"));
        if (cleanStr.length() >= 20) {
            for (int i = 0; i < 10; ++i) {
                bool ok;
                serialNo[i] = cleanStr.mid(i * 2, 2).toUInt(&ok, 16);
                if (!ok) serialNo[i] = 0;
            }
        }
    }
};

/**
 * @struct TB2Info
 * @brief B2事件信息结构体
 * @details 包含设备和合同相关信息
 */
struct __attribute__((packed)) TB2Info {
    uint8_t errorCode;               // 错误代码
    uint8_t antennaID;              // 天线ID
    uint8_t deviceType;             // 设备类型
    uint8_t issuerIdentifier[8];    // 发行方标识
    uint8_t contractType;           // 合同类型
    uint8_t contractVersion;        // 合同版本
    uint8_t serialNumber[8];        // 序列号
    QString dateofIssue;            // 发行日期
    QString dateofExpire;           // 过期日期
    uint8_t equitmentCV;            // 设备CV
    uint16_t status;                // 状态
    
    TB2Info() {
        memset(this, 0, sizeof(TB2Info));
        status = 0;
    }
};

/**
 * @struct TBAInfo
 * @brief BA事件信息结构体
 * @details 对应Delphi中的TBAInfo，包含PSAM签到认证信息
 */
struct __attribute__((packed)) TBAInfo {
    uint8_t frameType;          // 帧类型
    uint8_t errorCode;          // 错误代码
    //TOBUDateTime datetime;    // 日期时间(暂时注释，需要定义TOBUDateTime)
    uint8_t psamCount;          // PSAM数量
    TBAPSAMInfo* psamInfos;     // PSAM信息数组指针
    
    TBAInfo() {
        frameType = 0;
        errorCode = 0;
        psamCount = 0;
        psamInfos = nullptr;
    }
    
    /**
     * @brief 析构函数，释放PSAM信息数组
     */
    ~TBAInfo() {
        if (psamInfos) {
            delete[] psamInfos;
            psamInfos = nullptr;
        }
    }
    
    /**
     * @brief 拷贝构造函数
     */
    TBAInfo(const TBAInfo& other) {
        frameType = other.frameType;
        errorCode = other.errorCode;
        psamCount = other.psamCount;
        
        if (other.psamInfos && other.psamCount > 0) {
            psamInfos = new TBAPSAMInfo[psamCount];
            for (int i = 0; i < psamCount; ++i) {
                psamInfos[i] = other.psamInfos[i];
            }
        } else {
            psamInfos = nullptr;
        }
    }
    
    /**
     * @brief 赋值运算符
     */
    TBAInfo& operator=(const TBAInfo& other) {
        if (this != &other) {
            // 释放原有内存
            if (psamInfos) {
                delete[] psamInfos;
                psamInfos = nullptr;
            }
            
            frameType = other.frameType;
            errorCode = other.errorCode;
            psamCount = other.psamCount;
            
            if (other.psamInfos && other.psamCount > 0) {
                psamInfos = new TBAPSAMInfo[psamCount];
                for (int i = 0; i < psamCount; ++i) {
                    psamInfos[i] = other.psamInfos[i];
                }
            }
        }
        return *this;
    }
    
    /**
     * @brief 设置PSAM信息数组
     * @param count PSAM数量
     */
    void setPSAMCount(uint8_t count) {
        if (psamInfos) {
            delete[] psamInfos;
            psamInfos = nullptr;
        }
        
        psamCount = count;
        if (count > 0) {
            psamInfos = new TBAPSAMInfo[count];
        }
    }
    
    /**
     * @brief 获取指定索引的PSAM信息
     * @param index 索引
     * @return PSAM信息引用，如果索引无效返回默认值
     */
    const TBAPSAMInfo& getPSAMInfo(int index) const {
        static const TBAPSAMInfo defaultInfo;
        if (psamInfos && index >= 0 && index < psamCount) {
            return psamInfos[index];
        }
        return defaultInfo;
    }
    
    /**
     * @brief 设置指定索引的PSAM信息
     * @param index 索引
     * @param info PSAM信息
     */
    void setPSAMInfo(int index, const TBAPSAMInfo& info) {
        if (psamInfos && index >= 0 && index < psamCount) {
            psamInfos[index] = info;
        }
    }
};

/**
 * @struct TAuthResult
 * @brief 认证结果结构体
 * @details 包含认证过程的结果信息
 */
struct __attribute__((packed)) TAuthResult {
    uint8_t channelNumber;      // 通道号
    uint8_t statusCode;         // 状态码
    uint16_t authTimeCos;       // 认证时间COS
    
    TAuthResult() {
        channelNumber = 0;
        statusCode = 0;
        authTimeCos = 0;
    }
};

/**
 * @struct TB1Info
 * @brief RSU B1信息结构体
 * @details 对应Delphi中的TB1Info，包含RSU控制状态、PSAM状态、天线状态等信息
 * 
 * 原始Delphi定义：
 * TB1Info = packed record
 *   RSUControlStatus1: Byte;
 *   RSUControlStatus2: Byte;
 *   RSUControlStatus3: Byte;
 *   PSAMNum1: Byte;
 *   PSAMStatus1: array[0..7] of TPSAMStatus;
 *   PSAMNum2: Byte;
 *   PSAMStatus2: array[0..7] of TPSAMStatus;
 *   RSUAntennaNum: Byte;
 *   RSUAntennaNum2: Byte;
 *   AntennaStatus: array[0..5] of TAntennaStatus;
 * end;
 */
struct __attribute__((packed)) TB1Info {
    uint8_t rsuControlStatus1;          // RSUControlStatus1: RSU控制状态1
    uint8_t rsuControlStatus2;          // RSUControlStatus2: RSU控制状态2
    uint8_t rsuControlStatus3;          // RSUControlStatus3: RSU控制状态3
    uint8_t psamNum1;                   // PSAMNum1: PSAM数量1
    TPSAMStatus psamStatus1[8];         // PSAMStatus1: PSAM状态数组1[0..7]
    uint8_t psamNum2;                   // PSAMNum2: PSAM数量2
    TPSAMStatus psamStatus2[8];         // PSAMStatus2: PSAM状态数组2[0..7]
    uint8_t rsuAntennaNum;              // RSUAntennaNum: RSU天线数量
    uint8_t rsuAntennaNum2;             // RSUAntennaNum2: RSU天线数量2
    TAntennaStatus antennaStatus[6];    // AntennaStatus: 天线状态数组[0..5]
    
    TB1Info() {
        memset(this, 0, sizeof(TB1Info));
    }
    
    /**
     * @brief 获取指定索引的PSAM状态信息（状态组1）
     * @param index PSAM索引(0-7)
     * @return PSAM状态信息，如果索引无效返回默认值
     */
    const TPSAMStatus& getPSAMStatus1(int index) const {
        static const TPSAMStatus defaultStatus;
        if (index >= 0 && index < 8) {
            return psamStatus1[index];
        }
        return defaultStatus;
    }
    
    /**
     * @brief 设置指定索引的PSAM状态信息（状态组1）
     * @param index PSAM索引(0-7)
     * @param status PSAM状态信息
     */
    void setPSAMStatus1(int index, const TPSAMStatus& status) {
        if (index >= 0 && index < 8) {
            psamStatus1[index] = status;
        }
    }
    
    /**
     * @brief 获取指定索引的PSAM状态信息（状态组2）
     * @param index PSAM索引(0-7)
     * @return PSAM状态信息，如果索引无效返回默认值
     */
    const TPSAMStatus& getPSAMStatus2(int index) const {
        static const TPSAMStatus defaultStatus;
        if (index >= 0 && index < 8) {
            return psamStatus2[index];
        }
        return defaultStatus;
    }
    
    /**
     * @brief 设置指定索引的PSAM状态信息（状态组2）
     * @param index PSAM索引(0-7)
     * @param status PSAM状态信息
     */
    void setPSAMStatus2(int index, const TPSAMStatus& status) {
        if (index >= 0 && index < 8) {
            psamStatus2[index] = status;
        }
    }
    
    /**
     * @brief 获取指定索引的天线状态信息
     * @param index 天线索引(0-5)
     * @return 天线状态信息，如果索引无效返回默认值
     */
    const TAntennaStatus& getAntennaStatus(int index) const {
        static const TAntennaStatus defaultStatus;
        if (index >= 0 && index < 6) {
            return antennaStatus[index];
        }
        return defaultStatus;
    }
    
    /**
     * @brief 设置指定索引的天线状态信息
     * @param index 天线索引(0-5)
     * @param status 天线状态信息
     */
    void setAntennaStatus(int index, const TAntennaStatus& status) {
        if (index >= 0 && index < 6) {
            antennaStatus[index] = status;
        }
    }
};

/**
 * @struct TB4Content0and1
 * @brief B4事件内容结构体（内容0和1）
 * @details 对应Delphi中的TB4Content0and1，包含车辆的详细信息
 * 
 * 原始Delphi定义：
 * TB4Content0and1 = packed record
 *   VehPlateNo: array[0..11] of AnsiChar;
 *   VehPlateColor: Word;
 *   VehClass: Byte;
 *   VehUserType: Byte;
 *   VehDimensions: array[0..3] of Byte;
 *   VehWheels: Byte;
 *   VehAxles: Byte;
 *   VehWheelBases: Word;
 *   VehWeightLimits: array[0..2] of Byte;
 *   VehSpecificInfo: array[0..15] of AnsiChar;
 *   VehEngineNo: array[0..15] of AnsiChar;
 *   CardRestMoney: Integer;
 * end;
 */
struct __attribute__((packed)) TB4Content0and1 {
    AnsiChar vehPlateNo[12];        // VehPlateNo: 车牌号[0..11]
    Word vehPlateColor;             // VehPlateColor: 车牌颜色
    uint8_t vehClass;               // VehClass: 车辆类别
    uint8_t vehUserType;            // VehUserType: 车辆用户类型
    uint8_t vehDimensions[4];       // VehDimensions: 车辆尺寸[0..3]
    uint8_t vehWheels;              // VehWheels: 车轮数
    uint8_t vehAxles;               // VehAxles: 轴数
    Word vehWheelBases;             // VehWheelBases: 轴距
    uint8_t vehWeightLimits[3];     // VehWeightLimits: 重量限制[0..2]
    AnsiChar vehSpecificInfo[16];   // VehSpecificInfo: 车辆特殊信息[0..15]
    AnsiChar vehEngineNo[16];       // VehEngineNo: 发动机号[0..15]
    Integer cardRestMoney;          // CardRestMoney: 卡余额
    
    TB4Content0and1() {
        memset(this, 0, sizeof(TB4Content0and1));
    }
    
    /**
     * @brief 获取车牌号字符串
     * @return 车牌号字符串
     */
    QString getVehPlateNoString() const {
        return QString::fromLatin1(vehPlateNo, 12).trimmed();
    }
    
    /**
     * @brief 设置车牌号
     * @param plateNo 车牌号字符串
     */
    void setVehPlateNo(const QString& plateNo) {
        memset(vehPlateNo, 0, sizeof(vehPlateNo));
        QByteArray plateBytes = plateNo.toLatin1();
        int copyLen = qMin(plateBytes.size(), static_cast<int>(sizeof(vehPlateNo)));
        memcpy(vehPlateNo, plateBytes.constData(), copyLen);
    }
    
    /**
     * @brief 获取车辆特殊信息字符串
     * @return 车辆特殊信息字符串
     */
    QString getVehSpecificInfoString() const {
        return QString::fromLatin1(vehSpecificInfo, 16).trimmed();
    }
    
    /**
     * @brief 设置车辆特殊信息
     * @param info 车辆特殊信息字符串
     */
    void setVehSpecificInfo(const QString& info) {
        memset(vehSpecificInfo, 0, sizeof(vehSpecificInfo));
        QByteArray infoBytes = info.toLatin1();
        int copyLen = qMin(infoBytes.size(), static_cast<int>(sizeof(vehSpecificInfo)));
        memcpy(vehSpecificInfo, infoBytes.constData(), copyLen);
    }
    
    /**
     * @brief 获取发动机号字符串
     * @return 发动机号字符串
     */
    QString getVehEngineNoString() const {
        return QString::fromLatin1(vehEngineNo, 16).trimmed();
    }
    
    /**
     * @brief 设置发动机号
     * @param engineNo 发动机号字符串
     */
    void setVehEngineNo(const QString& engineNo) {
        memset(vehEngineNo, 0, sizeof(vehEngineNo));
        QByteArray engineBytes = engineNo.toLatin1();
        int copyLen = qMin(engineBytes.size(), static_cast<int>(sizeof(vehEngineNo)));
        memcpy(vehEngineNo, engineBytes.constData(), copyLen);
    }
    
    /**
     * @brief 获取车辆尺寸信息字符串
     * @return 格式化的车辆尺寸字符串
     */
    QString getVehDimensionsString() const {
        return QString("长:%1 宽:%2 高:%3 重:%4")
            .arg(vehDimensions[0])
            .arg(vehDimensions[1])
            .arg(vehDimensions[2])
            .arg(vehDimensions[3]);
    }
    
    /**
     * @brief 获取重量限制信息字符串
     * @return 格式化的重量限制字符串
     */
    QString getVehWeightLimitsString() const {
        return QString("限重1:%1 限重2:%2 限重3:%3")
            .arg(vehWeightLimits[0])
            .arg(vehWeightLimits[1])
            .arg(vehWeightLimits[2]);
    }
};

/**
 * @struct TVehicleInfo
 * @brief 车辆详细信息结构体
 * @details 对应Delphi中的TVehicleInfo，包含车辆的完整信息
 * 
 * 原始Delphi定义：
 * TVehicleInfo = packed record
 *   VLP: array[0..11] of AnsiChar;
 *   VLPColor: word;
 *   VehicleType: byte;
 *   UserType: byte;
 *   VehLength: Word;
 *   VehWidth: Word;
 *   VehHight: Word;
 *   Weight: array[0..2] of Byte;
 *   CurbWeight: array[0..2] of Byte;
 *   TotalWeight: array[0..2] of Byte;
 *   PersonNum: byte;
 *   IdenNum: array[0..16] of AnsiChar;
 *   SpecialDescription: array[0..15] of AnsiChar;
 *   Spare: array[0..13] of AnsiChar;
 * end;
 */
struct __attribute__((packed)) TVehicleInfo {
    AnsiChar vlp[12];               // VLP: 车牌号[0..11]
    Word vlpColor;                  // VLPColor: 车牌颜色
    uint8_t vehicleType;            // VehicleType: 车辆类型
    uint8_t userType;               // UserType: 用户类型
    Word vehLength;                 // VehLength: 车辆长度
    Word vehWidth;                  // VehWidth: 车辆宽度
    Word vehHight;                  // VehHight: 车辆高度
    uint8_t weight[3];              // Weight: 重量[0..2]
    uint8_t curbWeight[3];          // CurbWeight: 整备重量[0..2]
    uint8_t totalWeight[3];         // TotalWeight: 总重量[0..2]
    uint8_t personNum;              // PersonNum: 人数
    AnsiChar idenNum[17];           // IdenNum: 身份证号[0..16]
    AnsiChar specialDescription[16]; // SpecialDescription: 特殊描述[0..15]
    AnsiChar spare[14];             // Spare: 备用字段[0..13]
    
    TVehicleInfo() {
        memset(this, 0, sizeof(TVehicleInfo));
    }
    
    /**
     * @brief 获取车牌号字符串
     * @return 车牌号字符串
     */
    QString getVLPString() const {
        return QString::fromLatin1(vlp, 12).trimmed();
    }
    
    /**
     * @brief 设置车牌号
     * @param plateNo 车牌号字符串
     */
    void setVLP(const QString& plateNo) {
        memset(vlp, 0, sizeof(vlp));
        QByteArray plateBytes = plateNo.toLatin1();
        int copyLen = qMin(plateBytes.size(), static_cast<int>(sizeof(vlp)));
        memcpy(vlp, plateBytes.constData(), copyLen);
    }
    
    /**
     * @brief 获取车牌颜色描述
     * @return 车牌颜色字符串
     */
    QString getVLPColorString() const {
        switch (vlpColor) {
            case 0x00: return "蓝色";
            case 0x01: return "黄色";
            case 0x02: return "黑色";
            case 0x03: return "白色";
            case 0x04: return "渐变绿色";
            case 0x05: return "黄绿双拼色";
            case 0x06: return "蓝白渐变";
            default: return QString("保留颜色(0x%1)").arg(vlpColor, 2, 16, QChar('0'));
        }
    }
    
    /**
     * @brief 获取身份证号字符串
     * @return 身份证号字符串
     */
    QString getIdenNumString() const {
        return QString::fromLatin1(idenNum, 17).trimmed();
    }
    
    /**
     * @brief 设置身份证号
     * @param idenNo 身份证号字符串
     */
    void setIdenNum(const QString& idenNo) {
        memset(idenNum, 0, sizeof(idenNum));
        QByteArray idenBytes = idenNo.toLatin1();
        int copyLen = qMin(idenBytes.size(), static_cast<int>(sizeof(idenNum)));
        memcpy(idenNum, idenBytes.constData(), copyLen);
    }
    
    /**
     * @brief 获取特殊描述字符串
     * @return 特殊描述字符串
     */
    QString getSpecialDescriptionString() const {
        return QString::fromLatin1(specialDescription, 16).trimmed();
    }
    
    /**
     * @brief 设置特殊描述
     * @param description 特殊描述字符串
     */
    void setSpecialDescription(const QString& description) {
        memset(specialDescription, 0, sizeof(specialDescription));
        QByteArray descBytes = description.toLatin1();
        int copyLen = qMin(descBytes.size(), static_cast<int>(sizeof(specialDescription)));
        memcpy(specialDescription, descBytes.constData(), copyLen);
    }
    
    /**
     * @brief 获取车辆尺寸信息字符串
     * @return 格式化的车辆尺寸字符串
     */
    QString getVehicleDimensionsString() const {
        return QString("长:%1mm 宽:%2mm 高:%3mm")
            .arg(vehLength)
            .arg(vehWidth)
            .arg(vehHight);
    }
    
    /**
     * @brief 获取重量信息字符串
     * @return 格式化的重量信息字符串
     */
    QString getWeightInfoString() const {
        // 将3字节重量转换为整数（假设为小端序）
        uint32_t weight_kg = (weight[2] << 16) | (weight[1] << 8) | weight[0];
        uint32_t curbWeight_kg = (curbWeight[2] << 16) | (curbWeight[1] << 8) | curbWeight[0];
        uint32_t totalWeight_kg = (totalWeight[2] << 16) | (totalWeight[1] << 8) | totalWeight[0];
        
        return QString("重量:%1kg 整备重量:%2kg 总重量:%3kg")
            .arg(weight_kg)
            .arg(curbWeight_kg)
            .arg(totalWeight_kg);
    }
};

/**
 * @struct TAuthInfo
 * @brief PSAM认证信息结构体
 * @details 对应Delphi中的TAuthInfo，包含PSAM认证相关信息
 */
struct __attribute__((packed)) TAuthInfo {
    uint8_t psamChannel;            // PSAMChannel: PSAM通道
    uint8_t authStatus;             // AuthStatus: 认证状态
    uint8_t authData[13];           // AuthData: 认证数据
    
    TAuthInfo() {
        memset(this, 0, sizeof(TAuthInfo));
    }
    
    QString getPSAMChannelString() const {
        return QString("通道%1").arg(psamChannel);
    }
    
    QString getAuthStatusString() const {
        switch(authStatus) {
            case 0x00: return "认证失败";
            case 0x01: return "认证成功";
            default: return QString("未知状态(%1)").arg(authStatus, 2, 16, QChar('0'));
        }
    }
    
    QString getAuthDataString() const {
        QString data;
        for(int i = 0; i < 13; i++) {
            data += QString("%1").arg(authData[i], 2, 16, QChar('0'));
        }
        return data;
    }
};

/**
 * @struct TIC0012
 * @brief IC0012入口信息结构体
 * @details 对应Delphi中的TIC0012，包含车辆入口相关信息
 * 
 * 原始Delphi定义：
 * TIC0012 = packed record
 *   EntryNetwork: Word;
 *   EntryStation: Word;
 *   EntryLane: Byte;
 *   EntryTime: Integer;
 *   OperatorID: DWord;
 *   VClass: Byte;
 *   CardStatus: Byte;
 *   EntryLaneType: Byte;
 *   EntryWasteSN: Byte;
 *   EntryAxisType: DWord;
 *   VehiclePlate: array[0..15] of AnsiChar;
 *   EntryPSAMID: DWord;
 *   LableStations: array[0..8] of Byte;
 *   CheckBit: Word;
 * end;
 */
struct __attribute__((packed)) TIC0012 {
    Word entryNetwork;              // EntryNetwork: 入口网络编号(3600)
    Word entryStation;              // EntryStation: 收费站编号去掉36后的整数
    uint8_t entryLane;              // EntryLane: 入口车道编号
    Integer entryTime;              // EntryTime: 入口时间(UNIX时间，从2000年01月01日零时开始)
    uint32_t operatorID;            // OperatorID: 操作员ID
    uint8_t vClass;                 // VClass: 车辆类别
    uint8_t cardStatus;             // CardStatus: 卡状态(1MTC入口 2MTC出口 3ETC入口 4ETC出口)
    uint8_t entryLaneType;          // EntryLaneType: 入口车道类型(1MTC入口 2MTC出口 3ETC入口 4ETC出口)
    uint8_t entryWasteSN;           // EntryWasteSN: 入口流水序号
    uint32_t entryAxisType;         // EntryAxisType: 入口轴型
    AnsiChar vehiclePlate[16];      // VehiclePlate: 车牌号
    uint32_t entryPSAMID;           // EntryPSAMID: 入口PSAM ID
    uint8_t lableStations[9];       // LableStations: 标识收费站
    Word checkBit;                  // CheckBit: 校验位
    
    TIC0012() {
        memset(this, 0, sizeof(TIC0012));
    }
    
    QString getEntryNetworkString() const {
        return QString::number(entryNetwork);
    }
    
    QString getEntryStationString() const {
        return QString::number(entryStation);
    }
    
    QString getEntryTimeString() const {
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0));
        QDateTime entryDateTime = baseTime.addSecs(entryTime);
        return entryDateTime.toString("yyyy-MM-dd hh:mm:ss");
    }
    
    QString getVehiclePlateString() const {
        return QString::fromLatin1(vehiclePlate, 16).trimmed();
    }
    
    void setVehiclePlate(const QString& plateNo) {
        memset(vehiclePlate, 0, sizeof(vehiclePlate));
        QByteArray plateBytes = plateNo.toLatin1();
        int copyLen = qMin(plateBytes.size(), (int)sizeof(vehiclePlate) - 1);
        memcpy(vehiclePlate, plateBytes.constData(), copyLen);
    }
    
    QString getCardStatusString() const {
        switch(cardStatus) {
            case 0: return "已回收卡";
            case 1: return "已发放卡/MTC入口";
            case 2: return "未使用的预编码卡/MTC出口";
            case 3: return "ETC入口";
            case 4: return "ETC出口";
            default: return QString("未知状态(%1)").arg(cardStatus);
        }
    }
    
    QString getEntryLaneTypeString() const {
        switch(entryLaneType) {
            case 1: return "MTC入口";
            case 2: return "MTC出口";
            case 3: return "ETC入口";
            case 4: return "ETC出口";
            default: return QString("未知类型(%1)").arg(entryLaneType);
        }
    }
    
    QString getLableStationsString() const {
        QString stations;
        for(int i = 0; i < 9; i++) {
            stations += QString("%1").arg(lableStations[i], 2, 16, QChar('0'));
        }
        return stations;
    }
    
    void setEntryTimeFromCurrent() {
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0));
        QDateTime current = QDateTime::currentDateTime();
        entryTime = static_cast<Integer>(baseTime.secsTo(current));
    }
};

/**
 * @struct TIC0015
 * @brief IC0015发行信息结构体（标准版本）
 * @details 对应Delphi中的TIC0015，包含CPU卡发行相关信息
 * 
 * 原始Delphi定义：
 * TIC0015 = packed record
 *   CardLabel: array[0..7] of AnsiChar;
 *   CardType: Byte; //22储值卡 23记账卡
 *   CardVer: Byte; //升级用
 *   CardNetwork: array[0..1] of Byte; //BCD码，联网之前，只处理编号为3600的卡片
 *   CardNo: array[0..7] of Byte; //BCD码
 *   IssueDate: array[0..3] of Byte; //BCD码
 *   ExpireDate: array[0..3] of Byte; //BCD码
 *   FCI: array[0..1] of Byte; //BCD码，0没有折扣 1000 全折 20表示98折
 *   VehiclePlate: array[0..15] of AnsiChar; //如果车牌不为空，则该车牌与OBU车牌必须一致
 *   Spare: Word;
 *   Spare2: array[0..6] of Byte; //新增7字节长度
 * end;
 */
struct __attribute__((packed)) TIC0015 {
    AnsiChar cardLabel[8];          // CardLabel: 卡标识
    uint8_t cardType;               // CardType: 卡类型(22储值卡 23记账卡)
    uint8_t cardVer;                // CardVer: 卡版本(升级用)
    uint8_t cardNetwork[2];         // CardNetwork: 网络编号(BCD码，联网之前，只处理编号为3600的卡片)
    uint8_t cardNo[8];              // CardNo: 卡号(BCD码)
    uint8_t issueDate[4];           // IssueDate: 发行日期(BCD码)
    uint8_t expireDate[4];          // ExpireDate: 到期日期(BCD码)
    uint8_t fci[2];                 // FCI: 折扣信息(BCD码，0没有折扣 1000 全折 20表示98折)
    AnsiChar vehiclePlate[16];      // VehiclePlate: 车牌号(如果车牌不为空，则该车牌与OBU车牌必须一致)
    Word spare;                     // Spare: 备用字段
    uint8_t spare2[7];              // Spare2: 新增7字节长度
    
    TIC0015() {
        memset(this, 0, sizeof(TIC0015));
    }
    
    QString getCardLabelString() const {
        return QString::fromLatin1(cardLabel, 8).trimmed();
    }
    
    QString getCardTypeString() const {
        switch(cardType) {
            case 22: return "储值卡";
            case 23: return "记账卡";
            default: return QString("未知类型(%1)").arg(cardType);
        }
    }
    
    QString getCardNetworkString() const {
        return QString("%1%2").arg(cardNetwork[0], 2, 16, QChar('0'))
                              .arg(cardNetwork[1], 2, 16, QChar('0'));
    }
    
    QString getCardNoString() const {
        QString cardNoStr;
        for(int i = 0; i < 8; i++) {
            cardNoStr += QString("%1").arg(cardNo[i], 2, 16, QChar('0'));
        }
        return cardNoStr;
    }
    
    QString getIssueDateString() const {
        // BCD格式转换为日期字符串
        return QString("20%1%2-%3%4-%5%6")
            .arg(issueDate[0], 2, 16, QChar('0'))
            .arg(issueDate[1], 2, 16, QChar('0'))
            .arg(issueDate[2], 2, 16, QChar('0'))
            .arg(issueDate[3], 2, 16, QChar('0'));
    }
    
    QString getExpireDateString() const {
        // BCD格式转换为日期字符串
        return QString("20%1%2-%3%4-%5%6")
            .arg(expireDate[0], 2, 16, QChar('0'))
            .arg(expireDate[1], 2, 16, QChar('0'))
            .arg(expireDate[2], 2, 16, QChar('0'))
            .arg(expireDate[3], 2, 16, QChar('0'));
    }
    
    QString getFCIString() const {
        uint16_t fciValue = (fci[0] << 8) | fci[1];
        if (fciValue == 0) {
            return "无折扣";
        } else if (fciValue == 1000) {
            return "全折";
        } else {
            // 计算折扣率，如20表示98折
            double discount = (1000.0 - fciValue) / 10.0;
            return QString("%1折").arg(discount, 0, 'f', 1);
        }
    }
    
    QString getVehiclePlateString() const {
        return QString::fromLatin1(vehiclePlate, 16).trimmed();
    }
    
    void setVehiclePlate(const QString& plateNo) {
        memset(vehiclePlate, 0, sizeof(vehiclePlate));
        QByteArray plateBytes = plateNo.toLatin1();
        int copyLen = qMin(plateBytes.size(), (int)sizeof(vehiclePlate) - 1);
        memcpy(vehiclePlate, plateBytes.constData(), copyLen);
    }
    
    void setCardLabel(const QString& label) {
        memset(cardLabel, 0, sizeof(cardLabel));
        QByteArray labelBytes = label.toLatin1();
        int copyLen = qMin(labelBytes.size(), (int)sizeof(cardLabel) - 1);
        memcpy(cardLabel, labelBytes.constData(), copyLen);
    }
};

/**
 * @struct TIC0019
 * @brief IC0019车辆通行记录结构体
 * @details 对应Delphi中的TIC0019，包含车辆通行记录信息
 * 
 * 原始Delphi定义：
 * TIC0019 = packed record//43
 *   ComplexLabel: Byte; //0XAA
 *   FileLength: Byte; //0X29
 *   LockedLabel: Byte; //0X00
 *   EntryNetwork: Word; //3600
 *   EntryStation: Word; //收费站编号去掉36后的整数
 *   EntryLane: Byte;
 *   EntryTime: Integer; //采用UNIX时间，从2000年01月01日零时开始
 *   VClass: Byte;
 *   CardStatus: Byte; //1MTC入口 2MTC出口 3ETC入口 4ETC出口  //0已回收卡 1已发放卡 2未使用的预编码卡
 *   VehiclePlate: array[0..13] of AnsiChar;
 *   OperatorID: array[0..2] of Byte;
 *   EntryWasteSN: Byte; //入口流水序号，同入口流水表中流水号的低字节
 *   EntryAxisType: DWord;
 *   EntryPSAMID: DWord;
 *   Spare: Byte;
 *   CheckBit: Word;
 *   Spare2: array[0..19] of Byte; //新增20字节长度
 * end;
 */
struct __attribute__((packed)) TIC0019 {
    uint8_t complexLabel;           // ComplexLabel: 复合标识(0XAA)
    uint8_t fileLength;             // FileLength: 文件长度(0X29)
    uint8_t lockedLabel;            // LockedLabel: 锁定标识(0X00)
    Word entryNetwork;              // EntryNetwork: 入口网络编号(3600)
    Word entryStation;              // EntryStation: 收费站编号去掉36后的整数
    uint8_t entryLane;              // EntryLane: 入口车道编号
    Integer entryTime;              // EntryTime: 入口时间(UNIX时间，从2000年01月01日零时开始)
    uint8_t vClass;                 // VClass: 车辆类别
    uint8_t cardStatus;             // CardStatus: 卡状态(1MTC入口 2MTC出口 3ETC入口 4ETC出口)
    AnsiChar vehiclePlate[14];      // VehiclePlate: 车牌号
    uint8_t operatorID[3];          // OperatorID: 操作员ID
    uint8_t entryWasteSN;           // EntryWasteSN: 入口流水序号
    uint32_t entryAxisType;         // EntryAxisType: 入口轴型
    uint32_t entryPSAMID;           // EntryPSAMID: 入口PSAM ID
    uint8_t spare;                  // Spare: 备用字段
    Word checkBit;                  // CheckBit: 校验位
    uint8_t spare2[20];             // Spare2: 新增20字节长度
    
    TIC0019() {
        memset(this, 0, sizeof(TIC0019));
        complexLabel = 0xAA;        // 默认设置为0xAA
        fileLength = 0x29;          // 默认文件长度
        lockedLabel = 0x00;         // 默认未锁定
    }
    
    QString getEntryNetworkString() const {
        return QString::number(entryNetwork);
    }
    
    QString getEntryStationString() const {
        return QString::number(entryStation);
    }
    
    QString getEntryTimeString() const {
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0));
        QDateTime entryDateTime = baseTime.addSecs(entryTime);
        return entryDateTime.toString("yyyy-MM-dd hh:mm:ss");
    }
    
    QString getVehiclePlateString() const {
        return QString::fromLatin1(vehiclePlate, 14).trimmed();
    }
    
    void setVehiclePlate(const QString& plateNo) {
        memset(vehiclePlate, 0, sizeof(vehiclePlate));
        QByteArray plateBytes = plateNo.toLatin1();
        int copyLen = qMin(plateBytes.size(), (int)sizeof(vehiclePlate) - 1);
        memcpy(vehiclePlate, plateBytes.constData(), copyLen);
    }
    
    QString getCardStatusString() const {
        switch(cardStatus) {
            case 0: return "已回收卡";
            case 1: return "已发放卡/MTC入口";
            case 2: return "未使用的预编码卡/MTC出口";
            case 3: return "ETC入口";
            case 4: return "ETC出口";
            default: return QString("未知状态(%1)").arg(cardStatus);
        }
    }
    
    QString getOperatorIDString() const {
        return QString("%1%2%3")
            .arg(operatorID[0], 2, 16, QChar('0'))
            .arg(operatorID[1], 2, 16, QChar('0'))
            .arg(operatorID[2], 2, 16, QChar('0'));
    }
    
    QString getVClassString() const {
        switch(vClass) {
            case 1: return "一类车";
            case 2: return "二类车";
            case 3: return "三类车";
            case 4: return "四类车";
            case 5: return "五类车";
            case 6: return "六类车";
            default: return QString("未知车型(%1)").arg(vClass);
        }
    }
    
    void setEntryTimeFromCurrent() {
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0));
        QDateTime current = QDateTime::currentDateTime();
        entryTime = static_cast<Integer>(baseTime.secsTo(current));
    }
    
    void setOperatorID(uint32_t opId) {
        operatorID[0] = (opId >> 16) & 0xFF;
        operatorID[1] = (opId >> 8) & 0xFF;
        operatorID[2] = opId & 0xFF;
    }
    
    uint32_t getOperatorIDValue() const {
        return (static_cast<uint32_t>(operatorID[0]) << 16) |
               (static_cast<uint32_t>(operatorID[1]) << 8) |
               static_cast<uint32_t>(operatorID[2]);
    }
};

/**
 * @struct TIC0015New
 * @brief IC0015新版发行信息结构体
 * @details 对应Delphi中的TIC0015New，包含CPU卡发行相关信息
 * 
 * 原始Delphi定义：
 * TIC0015New = packed record
 *   CardLabel: array[0..7] of AnsiChar;
 *   CardType: Byte;
 *   CardVer: Byte;
 *   CardNetwork: array[0..1] of Byte;
 *   CardNo: array[0..7] of Byte;
 *   IssueDate: array[0..3] of Byte;
 *   ExpireDate: array[0..3] of Byte;
 *   VehiclePlate: array[0..11] of AnsiChar;
 *   UserType: Byte;
 *   PlateColor: array[0..1] of AnsiChar;
 *   Spare2: array[0..6] of Byte;
 * end;
 */
struct __attribute__((packed)) TIC0015New {
    AnsiChar cardLabel[8];          // CardLabel: CPU卡一级分散因子[0..7]
    uint8_t cardType;               // CardType: 卡类型(22储值卡 23记账卡)
    uint8_t cardVer;                // CardVer: 卡版本(0x10支持复合消费)
    uint8_t cardNetwork[2];         // CardNetwork: 网络编号BCD码[0..1]
    uint8_t cardNo[8];              // CardNo: 卡号BCD码[0..7]
    uint8_t issueDate[4];           // IssueDate: 发行日期BCD码[0..3]
    uint8_t expireDate[4];          // ExpireDate: 过期日期BCD码[0..3]
    AnsiChar vehiclePlate[12];      // VehiclePlate: 车牌号[0..11]
    uint8_t userType;               // UserType: 用户类型(0普通用户 6公务车用户)
    AnsiChar plateColor[2];         // PlateColor: 车牌颜色[0..1]
    uint8_t spare2[7];              // Spare2: 备用字段[0..6]
    
    TIC0015New() {
        memset(this, 0, sizeof(TIC0015New));
    }
    
    /**
     * @brief 获取卡标签字符串
     * @return 卡标签字符串
     */
    QString getCardLabelString() const {
        return QString::fromLatin1(cardLabel, 8).trimmed();
    }
    
    /**
     * @brief 设置卡标签
     * @param label 卡标签字符串
     */
    void setCardLabel(const QString& label) {
        memset(cardLabel, 0, sizeof(cardLabel));
        QByteArray labelBytes = label.toLatin1();
        int copyLen = qMin(labelBytes.size(), static_cast<int>(sizeof(cardLabel)));
        memcpy(cardLabel, labelBytes.constData(), copyLen);
    }
    
    /**
     * @brief 获取卡类型描述
     * @return 卡类型字符串
     */
    QString getCardTypeString() const {
        switch (cardType) {
            case 22: return "储值卡";
            case 23: return "记账卡";
            default: return QString("未知卡类型(%1)").arg(cardType);
        }
    }
    
    /**
     * @brief 获取网络编号字符串（BCD码）
     * @return 网络编号字符串
     */
    QString getCardNetworkString() const {
        return QString("%1%2")
            .arg(cardNetwork[0], 2, 16, QChar('0'))
            .arg(cardNetwork[1], 2, 16, QChar('0'))
            .toUpper();
    }
    
    /**
     * @brief 获取卡号字符串（BCD码）
     * @return 卡号字符串
     */
    QString getCardNoString() const {
        QString result;
        for (int i = 0; i < 8; ++i) {
            result += QString("%1").arg(cardNo[i], 2, 16, QChar('0'));
        }
        return result.toUpper();
    }
    
    /**
     * @brief 获取发行日期字符串（BCD码）
     * @return 发行日期字符串 YYYY-MM-DD
     */
    QString getIssueDateString() const {
        if (issueDate[0] == 0 && issueDate[1] == 0) {
            return "未设置";
        }
        return QString("20%1-%2-%3")
            .arg(issueDate[0], 2, 16, QChar('0'))
            .arg(issueDate[1], 2, 16, QChar('0'))
            .arg(issueDate[2], 2, 16, QChar('0'));
    }
    
    /**
     * @brief 获取过期日期字符串（BCD码）
     * @return 过期日期字符串 YYYY-MM-DD
     */
    QString getExpireDateString() const {
        if (expireDate[0] == 0 && expireDate[1] == 0) {
            return "未设置";
        }
        return QString("20%1-%2-%3")
            .arg(expireDate[0], 2, 16, QChar('0'))
            .arg(expireDate[1], 2, 16, QChar('0'))
            .arg(expireDate[2], 2, 16, QChar('0'));
    }
    
    /**
     * @brief 获取车牌号字符串
     * @return 车牌号字符串
     */
    QString getVehiclePlateString() const {
        return QString::fromLatin1(vehiclePlate, 12).trimmed();
    }
    
    /**
     * @brief 设置车牌号
     * @param plateNo 车牌号字符串
     */
    void setVehiclePlate(const QString& plateNo) {
        memset(vehiclePlate, 0, sizeof(vehiclePlate));
        QByteArray plateBytes = plateNo.toLatin1();
        int copyLen = qMin(plateBytes.size(), static_cast<int>(sizeof(vehiclePlate)));
        memcpy(vehiclePlate, plateBytes.constData(), copyLen);
    }
    
    /**
     * @brief 获取用户类型描述
     * @return 用户类型字符串
     */
    QString getUserTypeString() const {
        switch (userType) {
            case 0: return "普通用户";
            case 6: return "公务车用户";
            default: return QString("未知用户类型(%1)").arg(userType);
        }
    }
    
    /**
     * @brief 获取车牌颜色字符串
     * @return 车牌颜色字符串
     */
    QString getPlateColorString() const {
        return QString::fromLatin1(plateColor, 2).trimmed();
    }
};

/**
 * @struct TIC0019GB
 * @brief IC0019国标信息结构体
 * @details 对应Delphi中的TIC0019GB，包含通行记录国标信息（43字节）
 * 
 * 原始Delphi定义：
 * TIC0019GB = packed record//43
 *   ComplexLabel: Byte;
 *   FileLength: Byte;
 *   LockedLabel: Byte;
 *   EntryNetwork: Word;
 *   EntryStation: Word;
 *   EntryLane: Byte;
 *   EntryTime: Integer;
 *   VClass: Byte;
 *   EntryType: Byte;
 *   ETCNo: array[0..2] of Byte;
 *   PassTime: Integer;
 *   VehiclePlate: array[0..11] of AnsiChar;
 *   PlateColor: Byte;
 *   AixNum: Byte;
 *   TotalWeight: array[0..2] of byte;
 *   VehStatus: Byte;
 *   Spare: array[0..3] of Byte;
 * end;
 */
struct __attribute__((packed)) TIC0019GB {
    uint8_t complexLabel;           // ComplexLabel: 复合标识(0xAA)
    uint8_t fileLength;             // FileLength: 文件长度(0x29)
    uint8_t lockedLabel;            // LockedLabel: 锁定标识(0x00)
    Word entryNetwork;              // EntryNetwork: 入口网络编号(3600)
    Word entryStation;              // EntryStation: 入口收费站编号
    uint8_t entryLane;              // EntryLane: 入口车道号
    Integer entryTime;              // EntryTime: 入口时间(UNIX时间，从2000年01月01日开始)
    uint8_t vClass;                 // VClass: 车型
    uint8_t entryType;              // EntryType: 入口类型
    uint8_t etcNo[3];               // ETCNo: ETC门架编号[0..2]
    Integer passTime;               // PassTime: 通行门架时间
    AnsiChar vehiclePlate[12];      // VehiclePlate: 车牌号[0..11]
    uint8_t plateColor;             // PlateColor: 车牌颜色
    uint8_t aixNum;                 // AixNum: 车轴数
    uint8_t totalWeight[3];         // TotalWeight: 总重量[0..2] (固定值0xBBBBBB)
    uint8_t vehStatus;              // VehStatus: 车辆状态标识(默认0xFF)
    uint8_t spare[4];               // Spare: 备用字段[0..3]
    
    TIC0019GB() {
        memset(this, 0, sizeof(TIC0019GB));
        // 设置默认值
        complexLabel = 0xAA;
        fileLength = 0x29;
        lockedLabel = 0x00;
        totalWeight[0] = 0xBB;
        totalWeight[1] = 0xBB;
        totalWeight[2] = 0xBB;
        vehStatus = 0xFF;
    }
    
    /**
     * @brief 获取入口网络编号字符串
     * @return 入口网络编号字符串
     */
    QString getEntryNetworkString() const {
        return QString::number(entryNetwork);
    }
    
    /**
     * @brief 获取入口收费站编号字符串
     * @return 收费站编号字符串
     */
    QString getEntryStationString() const {
        return QString::number(entryStation);
    }
    
    /**
     * @brief 获取入口时间字符串
     * @return 格式化的入口时间字符串
     */
    QString getEntryTimeString() const {
        if (entryTime == 0) {
            return "未设置";
        }
        // UNIX时间从2000年01月01日开始
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0), Qt::UTC);
        QDateTime entryDateTime = baseTime.addSecs(entryTime);
        return entryDateTime.toString("yyyy-MM-dd hh:mm:ss");
    }
    
    /**
     * @brief 获取入口类型描述
     * @return 入口类型字符串
     */
    QString getEntryTypeString() const {
        switch (entryType) {
            case 0x01: return "封闭MTC入口";
            case 0x02: return "封闭MTC出口";
            case 0x03: return "封闭ETC入口";
            case 0x04: return "封闭ETC出口";
            case 0x05: return "E/M混合开放式";
            case 0x06: return "ETC开放式";
            default: return QString("未知入口类型(0x%1)").arg(entryType, 2, 16, QChar('0'));
        }
    }
    
    /**
     * @brief 获取ETC门架编号字符串
     * @return ETC门架编号字符串
     */
    QString getETCNoString() const {
        return QString("%1%2%3")
            .arg(etcNo[0], 2, 16, QChar('0'))
            .arg(etcNo[1], 2, 16, QChar('0'))
            .arg(etcNo[2], 2, 16, QChar('0'))
            .toUpper();
    }
    
    /**
     * @brief 获取通行时间字符串
     * @return 格式化的通行时间字符串
     */
    QString getPassTimeString() const {
        if (passTime == 0) {
            return "未设置";
        }
        // UNIX时间从2000年01月01日开始
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0), Qt::UTC);
        QDateTime passDateTime = baseTime.addSecs(passTime);
        return passDateTime.toString("yyyy-MM-dd hh:mm:ss");
    }
    
    /**
     * @brief 获取车牌号字符串
     * @return 车牌号字符串
     */
    QString getVehiclePlateString() const {
        return QString::fromLatin1(vehiclePlate, 12).trimmed();
    }
    
    /**
     * @brief 设置车牌号
     * @param plateNo 车牌号字符串
     */
    void setVehiclePlate(const QString& plateNo) {
        memset(vehiclePlate, 0, sizeof(vehiclePlate));
        QByteArray plateBytes = plateNo.toLatin1();
        int copyLen = qMin(plateBytes.size(), static_cast<int>(sizeof(vehiclePlate)));
        memcpy(vehiclePlate, plateBytes.constData(), copyLen);
    }
    
    /**
     * @brief 获取车牌颜色描述
     * @return 车牌颜色字符串
     */
    QString getPlateColorString() const {
        switch (plateColor) {
            case 0x00: return "蓝色";
            case 0x01: return "黄色";
            case 0x02: return "黑色";
            case 0x03: return "白色";
            case 0x04: return "渐变绿";
            case 0x05: return "黄绿双拼色";
            case 0x06: return "蓝白渐变";
            default: return QString("未知颜色(0x%1)").arg(plateColor, 2, 16, QChar('0'));
        }
    }
    
    /**
     * @brief 获取总重量信息字符串
     * @return 总重量字符串
     */
    QString getTotalWeightString() const {
        if (totalWeight[0] == 0xBB && totalWeight[1] == 0xBB && totalWeight[2] == 0xBB) {
            return "固定值(0xBBBBBB)";
        }
        // 如果不是固定值，按实际重量解析（3字节小端序）
        uint32_t weight_kg = (totalWeight[2] << 16) | (totalWeight[1] << 8) | totalWeight[0];
        return QString("%1kg").arg(weight_kg);
    }
    
    /**
     * @brief 设置入口时间（从当前时间）
     */
    void setEntryTimeFromCurrent() {
        QDateTime currentTime = QDateTime::currentDateTimeUtc();
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0), Qt::UTC);
        entryTime = baseTime.secsTo(currentTime);
    }
    
    /**
     * @brief 设置通行时间（从当前时间）
     */
    void setPassTimeFromCurrent() {
        QDateTime currentTime = QDateTime::currentDateTimeUtc();
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0), Qt::UTC);
        passTime = baseTime.secsTo(currentTime);
    }
};

/**
 * @struct TB4Info
 * @brief B4事件信息结构体
 * @details 对应Delphi中的TB4Info，包含交易相关的详细信息
 * 
 * 原始Delphi定义：
 * TB4Info = packed record
 *   ErrorCode: Byte;
 *   TransType: Byte;
 *   VehicleInfo: TVehicleInfo;
 *   CardRestMoney: integer;
 *   ETCInfo: array[0..90] of Byte;
 *   IssuerInfo: TIC0015New;
 *   LastStation: TIC0019GB;
 * end;
 */
struct __attribute__((packed)) TB4Info {
    uint8_t errorCode;              // ErrorCode: 错误代码
    uint8_t transType;              // TransType: 交易类型
    TVehicleInfo vehicleInfo;       // VehicleInfo: 车辆信息
    Integer cardRestMoney;          // CardRestMoney: 卡余额
    uint8_t etcInfo[91];            // ETCInfo: ETC信息[0..90]
    TIC0015New issuerInfo;          // IssuerInfo: 发行方信息
    TIC0019GB lastStation;          // LastStation: 最后一站信息
    
    TB4Info() {
        memset(this, 0, sizeof(TB4Info));
    }
    
    /**
     * @brief 获取车牌号字符串
     * @return 车辆车牌号
     */
    QString getVehiclePlateNo() const {
        return vehicleInfo.getVLPString();
    }
    
    /**
     * @brief 获取车牌颜色描述
     * @return 车牌颜色字符串
     */
    QString getVehiclePlateColor() const {
        return vehicleInfo.getVLPColorString();
    }
    
    /**
     * @brief 获取余额字符串（以元为单位）
     * @return 格式化的余额字符串
     */
    QString getCardRestMoneyString() const {
        return QString("%1元").arg(cardRestMoney / 100.0, 0, 'f', 2);
    }
    
    /**
     * @brief 获取交易类型描述
     * @return 交易类型字符串
     */
    QString getTransTypeString() const {
        switch (transType) {
            case 0x01: return "入口交易";
            case 0x02: return "出口交易";
            case 0x03: return "路径交易";
            case 0x04: return "etc充值";
            default: return QString("未知交易类型(0x%1)").arg(transType, 2, 16, QChar('0'));
        }
    }
};

/**
 * @struct TB5Info
 * @brief B5事件信息结构体
 * @details 对应Delphi中的TB5Info，包含PSAM交易相关信息
 * 
 * 原始Delphi定义：
 * TB5Info = packed record
 *   ErrorCode: Byte;
 *   PSAMNo: array[0..5] of Byte;
 *   TransTime: array[0..6] of Byte;
 *   AlgFlag: Byte;
 *   KeyVersion: Byte;
 *   TAC: Integer;
 *   ICCPayserial: Word;
 *   PSAMTransSerial: integer;
 *   CardBalance: integer;
 * end;
 */
struct __attribute__((packed)) TB5Info {
    uint8_t errorCode;              // ErrorCode: 错误代码
    uint8_t psamNo[6];              // PSAMNo: PSAM卡号[0..5]
    uint8_t transTime[7];           // TransTime: 交易时间[0..6]
    uint8_t algFlag;                // AlgFlag: 算法标识
    uint8_t keyVersion;             // KeyVersion: 密钥版本
    Integer tac;                    // TAC: 交易认证码
    Word iccPayserial;              // ICCPayserial: ICC支付序号
    Integer psamTransSerial;        // PSAMTransSerial: PSAM交易序号
    Integer cardBalance;            // CardBalance: 卡余额
    
    TB5Info() {
        memset(this, 0, sizeof(TB5Info));
    }
    
    /**
     * @brief 获取PSAM卡号字符串
     * @return PSAM卡号十六进制字符串
     */
    QString getPSAMNoString() const {
        QString result;
        for (int i = 0; i < 6; ++i) {
            result += QString("%1").arg(psamNo[i], 2, 16, QChar('0'));
        }
        return result.toUpper();
    }
    
    /**
     * @brief 获取交易时间字符串
     * @return 格式化的交易时间字符串
     */
    QString getTransTimeString() const {
        // 假设格式为：年年月月日日时时分分秒秒
        if (transTime[0] == 0 && transTime[1] == 0) {
            return "未设置";
        }
        
        return QString("20%1-%2-%3 %4:%5:%6")
            .arg(transTime[0], 2, 10, QChar('0'))  // 年
            .arg(transTime[1], 2, 10, QChar('0'))  // 月
            .arg(transTime[2], 2, 10, QChar('0'))  // 日
            .arg(transTime[3], 2, 10, QChar('0'))  // 时
            .arg(transTime[4], 2, 10, QChar('0'))  // 分
            .arg(transTime[5], 2, 10, QChar('0')); // 秒
    }
    
    /**
     * @brief 获取TAC字符串
     * @return TAC十六进制字符串
     */
    QString getTACString() const {
        return QString("0x%1").arg(static_cast<uint32_t>(tac), 8, 16, QChar('0')).toUpper();
    }
    
    /**
     * @brief 获取卡余额字符串（以元为单位）
     * @return 格式化的余额字符串
     */
    QString getCardBalanceString() const {
        return QString("%1元").arg(cardBalance / 100.0, 0, 'f', 2);
    }
    
    /**
     * @brief 设置PSAM卡号
     * @param psamNoStr PSAM卡号十六进制字符串
     */
    void setPSAMNo(const QString& psamNoStr) {
        QString cleanStr = QString(psamNoStr).remove(QRegExp("[^0-9A-Fa-f]"));
        if (cleanStr.length() >= 12) {
            for (int i = 0; i < 6; ++i) {
                bool ok;
                psamNo[i] = cleanStr.mid(i * 2, 2).toUInt(&ok, 16);
                if (!ok) psamNo[i] = 0;
            }
        }
    }
};

/**
 * @struct TMoneyInfo
 * @brief 金额信息结构体
 * @details 对应Delphi中的TMoneyInfo，包含门架通行和收费信息
 * 
 * 原始Delphi定义：
 * TMoneyInfo = packed record
 *   GantryID: array[0..2] of Byte;
 *   PassTime: integer;
 *   TotalMoney: array[0..2] of Byte;
 * end;
 */
struct __attribute__((packed)) TMoneyInfo {
    uint8_t gantryID[3];            // GantryID: 门架编号[0..2]
    Integer passTime;               // PassTime: 通行时间
    uint8_t totalMoney[3];          // TotalMoney: 总金额[0..2]
    
    TMoneyInfo() {
        memset(this, 0, sizeof(TMoneyInfo));
    }
    
    /**
     * @brief 获取门架编号字符串
     * @return 门架编号十六进制字符串
     */
    QString getGantryIDString() const {
        return QString("%1%2%3")
            .arg(gantryID[0], 2, 16, QChar('0'))
            .arg(gantryID[1], 2, 16, QChar('0'))
            .arg(gantryID[2], 2, 16, QChar('0'))
            .toUpper();
    }
    
    /**
     * @brief 获取通行时间字符串
     * @return 格式化的通行时间字符串
     */
    QString getPassTimeString() const {
        if (passTime == 0) {
            return "未设置";
        }
        // UNIX时间从2000年01月01日开始
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0), Qt::UTC);
        QDateTime passDateTime = baseTime.addSecs(passTime);
        return passDateTime.toString("yyyy-MM-dd hh:mm:ss");
    }
    
    /**
     * @brief 获取总金额（以分为单位）
     * @return 总金额整数值
     */
    uint32_t getTotalMoneyValue() const {
        // 将3字节金额转换为整数（小端序）
        return (totalMoney[2] << 16) | (totalMoney[1] << 8) | totalMoney[0];
    }
    
    /**
     * @brief 获取总金额字符串（以元为单位）
     * @return 格式化的金额字符串
     */
    QString getTotalMoneyString() const {
        uint32_t moneyInCents = getTotalMoneyValue();
        return QString("%1元").arg(moneyInCents / 100.0, 0, 'f', 2);
    }
    
    /**
     * @brief 设置门架编号
     * @param gantryStr 门架编号十六进制字符串
     */
    void setGantryID(const QString& gantryStr) {
        QString cleanStr = QString(gantryStr).remove(QRegExp("[^0-9A-Fa-f]"));
        if (cleanStr.length() >= 6) {
            for (int i = 0; i < 3; ++i) {
                bool ok;
                gantryID[i] = cleanStr.mid(i * 2, 2).toUInt(&ok, 16);
                if (!ok) gantryID[i] = 0;
            }
        }
    }
    
    /**
     * @brief 设置总金额（以分为单位）
     * @param moneyInCents 总金额（分）
     */
    void setTotalMoney(uint32_t moneyInCents) {
        totalMoney[0] = moneyInCents & 0xFF;
        totalMoney[1] = (moneyInCents >> 8) & 0xFF;
        totalMoney[2] = (moneyInCents >> 16) & 0xFF;
    }
    
    /**
     * @brief 设置通行时间为当前时间
     */
    void setPassTimeFromCurrent() {
        QDateTime currentTime = QDateTime::currentDateTimeUtc();
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0), Qt::UTC);
        passTime = baseTime.secsTo(currentTime);
    }
};

/**
 * @struct TPassInfo
 * @brief 通行信息结构体
 * @details 对应Delphi中的TPassInfo，包含收费次数、门架编号和通行路径信息
 * 
 * 原始Delphi定义：
 * TPassInfo = packed record
 *   ChargeCount: Byte;
 *   GantryID: array[0..2] of Byte;
 *   GantryPath: array[0..599] of Byte;
 * end;
 */
struct __attribute__((packed)) TPassInfo {
    uint8_t chargeCount;            // ChargeCount: 收费次数
    uint8_t gantryID[3];            // GantryID: 门架编号[0..2]
    uint8_t gantryPath[600];        // GantryPath: 门架路径[0..599]
    
    TPassInfo() {
        memset(this, 0, sizeof(TPassInfo));
    }
    
    /**
     * @brief 获取门架编号字符串
     * @return 门架编号十六进制字符串
     */
    QString getGantryIDString() const {
        return QString("%1%2%3")
            .arg(gantryID[0], 2, 16, QChar('0'))
            .arg(gantryID[1], 2, 16, QChar('0'))
            .arg(gantryID[2], 2, 16, QChar('0'))
            .toUpper();
    }
    
    /**
     * @brief 获取收费次数字符串
     * @return 收费次数字符串
     */
    QString getChargeCountString() const {
        return QString::number(chargeCount);
    }
    
    /**
     * @brief 获取门架路径数据的有效长度
     * @return 有效路径数据长度
     */
    int getValidPathLength() const {
        // 从后往前查找第一个非零字节
        for (int i = 599; i >= 0; --i) {
            if (gantryPath[i] != 0) {
                return i + 1;
            }
        }
        return 0;
    }
    
    /**
     * @brief 获取门架路径十六进制字符串（仅有效部分）
     * @return 门架路径十六进制字符串
     */
    QString getGantryPathString() const {
        int validLen = getValidPathLength();
        if (validLen == 0) {
            return "空路径";
        }
        
        QString result;
        for (int i = 0; i < validLen; ++i) {
            result += QString("%1").arg(gantryPath[i], 2, 16, QChar('0'));
            if (i < validLen - 1 && (i + 1) % 16 == 0) {
                result += "\n";  // 每16字节换行便于阅读
            } else if (i < validLen - 1) {
                result += " ";
            }
        }
        return result.toUpper();
    }
    
    /**
     * @brief 获取门架路径概要信息
     * @return 路径概要字符串
     */
    QString getGantryPathSummary() const {
        int validLen = getValidPathLength();
        if (validLen == 0) {
            return "空路径";
        }
        
        return QString("路径长度:%1字节, 起始:%2%3%4, 结尾:%5%6%7")
            .arg(validLen)
            .arg(gantryPath[0], 2, 16, QChar('0'))
            .arg(gantryPath[1], 2, 16, QChar('0'))
            .arg(gantryPath[2], 2, 16, QChar('0'))
            .arg(gantryPath[validLen-3 < 0 ? 0 : validLen-3], 2, 16, QChar('0'))
            .arg(gantryPath[validLen-2 < 0 ? 0 : validLen-2], 2, 16, QChar('0'))
            .arg(gantryPath[validLen-1], 2, 16, QChar('0'))
            .toUpper();
    }
    
    /**
     * @brief 设置门架编号
     * @param gantryStr 门架编号十六进制字符串
     */
    void setGantryID(const QString& gantryStr) {
        QString cleanStr = QString(gantryStr).remove(QRegExp("[^0-9A-Fa-f]"));
        if (cleanStr.length() >= 6) {
            for (int i = 0; i < 3; ++i) {
                bool ok;
                gantryID[i] = cleanStr.mid(i * 2, 2).toUInt(&ok, 16);
                if (!ok) gantryID[i] = 0;
            }
        }
    }
    
    /**
     * @brief 设置门架路径数据
     * @param pathData 路径数据字节数组
     * @param length 数据长度（最大600字节）
     */
    void setGantryPath(const uint8_t* pathData, int length) {
        memset(gantryPath, 0, sizeof(gantryPath));
        if (pathData && length > 0) {
            int copyLen = qMin(length, static_cast<int>(sizeof(gantryPath)));
            memcpy(gantryPath, pathData, copyLen);
        }
    }
    
    /**
     * @brief 从十六进制字符串设置门架路径
     * @param pathStr 十六进制字符串（每两个字符代表一个字节）
     */
    void setGantryPathFromHexString(const QString& pathStr) {
        QString cleanStr = QString(pathStr).remove(QRegExp("[^0-9A-Fa-f]"));
        memset(gantryPath, 0, sizeof(gantryPath));
        
        int maxBytes = qMin(cleanStr.length() / 2, static_cast<int>(sizeof(gantryPath)));
        for (int i = 0; i < maxBytes; ++i) {
            bool ok;
            gantryPath[i] = cleanStr.mid(i * 2, 2).toUInt(&ok, 16);
            if (!ok) gantryPath[i] = 0;
        }
    }
    
    /**
     * @brief 清空门架路径数据
     */
    void clearGantryPath() {
        memset(gantryPath, 0, sizeof(gantryPath));
    }
};

/**
 * @struct TB4Content2
 * @brief B4事件内容2结构体
 * @details 对应Delphi中的TB4Content2，包含金额信息和通行信息
 * 
 * 原始Delphi定义：
 * TB4Content2 = packed record
 *   MoneyhInfo: TMoneyInfo;
 *   PassInfo: TPassInfo;
 * end;
 * 
 * 实际大小: TMoneyInfo(10字节) + TPassInfo(604字节) = 614字节
 */
struct __attribute__((packed)) TB4Content2 {
    TMoneyInfo moneyhInfo;          // MoneyhInfo: 金额信息(10字节)
    TPassInfo passInfo;             // PassInfo: 通行信息(604字节)
    
    TB4Content2() {
        // 构造函数会自动调用成员的构造函数
    }
    
    /**
     * @brief 获取金额信息概要
     * @return 金额信息字符串
     */
    QString getMoneyInfoSummary() const {
        return QString("门架:%1, 时间:%2, 金额:%3")
            .arg(moneyhInfo.getGantryIDString())
            .arg(moneyhInfo.getPassTimeString())
            .arg(moneyhInfo.getTotalMoneyString());
    }
    
    /**
     * @brief 获取通行信息概要
     * @return 通行信息字符串
     */
    QString getPassInfoSummary() const {
        return QString("收费次数:%1, 门架:%2, %3")
            .arg(passInfo.getChargeCountString())
            .arg(passInfo.getGantryIDString())
            .arg(passInfo.getGantryPathSummary());
    }
    
    /**
     * @brief 获取完整的内容概要
     * @return 完整概要字符串
     */
    QString getCompleteSummary() const {
        return QString("【金额信息】%1\n【通行信息】%2")
            .arg(getMoneyInfoSummary())
            .arg(getPassInfoSummary());
    }
    
    /**
     * @brief 验证数据完整性
     * @return 验证结果和错误信息
     */
    QString validateData() const {
        QStringList errors;
        
        // 检查金额信息
        if (moneyhInfo.getTotalMoneyValue() == 0) {
            errors << "金额为零";
        }
        
        if (moneyhInfo.passTime == 0) {
            errors << "通行时间未设置";
        }
        
        // 检查通行信息
        if (passInfo.chargeCount == 0) {
            errors << "收费次数为零";
        }
        
        if (passInfo.getValidPathLength() == 0) {
            errors << "通行路径为空";
        }
        
        if (errors.isEmpty()) {
            return "数据验证通过";
        } else {
            return QString("数据验证失败: %1").arg(errors.join(", "));
        }
    }
};

/**
 * @struct TB4Content0Station
 * @brief B4事件内容0站点结构体
 * @details 对应Delphi中的TB4Content0Station，包含收费站的详细信息
 * 
 * 原始Delphi定义：
 * TB4Content0Station = packed record
 *   ComplexUseId: Byte;
 *   RecordLength: Byte;
 *   LockId: Byte;
 *   NetworkNo: Word;
 *   StationId: Word;
 *   LaneId: Byte;
 *   LaneTime: Integer;
 *   VClass: Byte;
 *   LaneStatus: Byte;
 *   Spare1: array[0..8] of AnsiChar;
 *   EmployeeNo: array[0..3] of Byte;
 *   Shift: Byte;
 *   VLP: array[0..11] of AnsiChar;
 *   Spare2: array[0..3] of AnsiChar;
 * end;
 */
struct __attribute__((packed)) TB4Content0Station {
    uint8_t complexUseId;           // ComplexUseId: 复合使用标识
    uint8_t recordLength;           // RecordLength: 记录长度
    uint8_t lockId;                 // LockId: 锁定标识
    Word networkNo;                 // NetworkNo: 网络编号
    Word stationId;                 // StationId: 收费站编号
    uint8_t laneId;                 // LaneId: 车道编号
    Integer laneTime;               // LaneTime: 车道时间
    uint8_t vClass;                 // VClass: 车型
    uint8_t laneStatus;             // LaneStatus: 车道状态
    AnsiChar spare1[9];             // Spare1: 备用字段1[0..8]
    uint8_t employeeNo[4];          // EmployeeNo: 员工号[0..3]
    uint8_t shift;                  // Shift: 班次
    AnsiChar vlp[12];               // VLP: 车牌号[0..11]
    AnsiChar spare2[4];             // Spare2: 备用字段2[0..3]
    
    TB4Content0Station() {
        memset(this, 0, sizeof(TB4Content0Station));
    }
    
    /**
     * @brief 获取网络编号字符串
     * @return 网络编号字符串
     */
    QString getNetworkNoString() const {
        return QString::number(networkNo);
    }
    
    /**
     * @brief 获取收费站编号字符串
     * @return 收费站编号字符串
     */
    QString getStationIdString() const {
        return QString::number(stationId);
    }
    
    /**
     * @brief 获取车道时间字符串
     * @return 格式化的车道时间字符串
     */
    QString getLaneTimeString() const {
        if (laneTime == 0) {
            return "未设置";
        }
        // UNIX时间从2000年01月01日开始
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0), Qt::UTC);
        QDateTime laneDateTime = baseTime.addSecs(laneTime);
        return laneDateTime.toString("yyyy-MM-dd hh:mm:ss");
    }
    
    /**
     * @brief 获取员工号字符串
     * @return 员工号十六进制字符串
     */
    QString getEmployeeNoString() const {
        QString result;
        for (int i = 0; i < 4; ++i) {
            result += QString("%1").arg(employeeNo[i], 2, 16, QChar('0'));
        }
        return result.toUpper();
    }
    
    /**
     * @brief 获取车牌号字符串
     * @return 车牌号字符串
     */
    QString getVLPString() const {
        return QString::fromLatin1(vlp, 12).trimmed();
    }
    
    /**
     * @brief 设置车牌号
     * @param plateNo 车牌号字符串
     */
    void setVLP(const QString& plateNo) {
        memset(vlp, 0, sizeof(vlp));
        QByteArray plateBytes = plateNo.toLatin1();
        int copyLen = qMin(plateBytes.size(), static_cast<int>(sizeof(vlp)));
        memcpy(vlp, plateBytes.constData(), copyLen);
    }
    
    /**
     * @brief 获取备用字段1字符串
     * @return 备用字段1字符串
     */
    QString getSpare1String() const {
        return QString::fromLatin1(spare1, 9).trimmed();
    }
    
    /**
     * @brief 获取备用字段2字符串
     * @return 备用字段2字符串
     */
    QString getSpare2String() const {
        return QString::fromLatin1(spare2, 4).trimmed();
    }
    
    /**
     * @brief 设置车道时间为当前时间
     */
    void setLaneTimeFromCurrent() {
        QDateTime currentTime = QDateTime::currentDateTimeUtc();
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0), Qt::UTC);
        laneTime = baseTime.secsTo(currentTime);
    }
};

/**
 * @struct TB4Content1Station
 * @brief B4事件内容1站点结构体
 * @details 对应Delphi中的TB4Content1Station，包含扩展的收费站信息
 * 
 * 原始Delphi定义：
 * TB4Content1Station = packed record
 *   NetworkNo: Word;
 *   StationId: Word;
 *   LaneId: Byte;
 *   LaneTime: Integer;
 *   VClass: Byte;
 *   LaneStatus: Byte;
 *   EmployeeNo: array[0..3] of Byte;
 *   Shift: Byte;
 *   VLP: array[0..11] of AnsiChar;
 *   VLPColor: Byte;
 *   Axle: Byte;
 *   VehDimensions: array[0..3] of Byte;
 *   Weight: integer;
 *   Spare: array[0..30] of AnsiChar;
 * end;
 */
struct __attribute__((packed)) TB4Content1Station {
    Word networkNo;                 // NetworkNo: 网络编号
    Word stationId;                 // StationId: 收费站编号
    uint8_t laneId;                 // LaneId: 车道编号
    Integer laneTime;               // LaneTime: 车道时间
    uint8_t vClass;                 // VClass: 车型
    uint8_t laneStatus;             // LaneStatus: 车道状态
    uint8_t employeeNo[4];          // EmployeeNo: 员工号[0..3]
    uint8_t shift;                  // Shift: 班次
    AnsiChar vlp[12];               // VLP: 车牌号[0..11]
    uint8_t vlpColor;               // VLPColor: 车牌颜色
    uint8_t axle;                   // Axle: 车轴数
    uint8_t vehDimensions[4];       // VehDimensions: 车辆尺寸[0..3]
    Integer weight;                 // Weight: 重量
    AnsiChar spare[31];             // Spare: 备用字段[0..30]
    
    TB4Content1Station() {
        memset(this, 0, sizeof(TB4Content1Station));
    }
    
    /**
     * @brief 获取网络编号字符串
     * @return 网络编号字符串
     */
    QString getNetworkNoString() const {
        return QString::number(networkNo);
    }
    
    /**
     * @brief 获取收费站编号字符串
     * @return 收费站编号字符串
     */
    QString getStationIdString() const {
        return QString::number(stationId);
    }
    
    /**
     * @brief 获取车道时间字符串
     * @return 格式化的车道时间字符串
     */
    QString getLaneTimeString() const {
        if (laneTime == 0) {
            return "未设置";
        }
        // UNIX时间从2000年01月01日开始
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0), Qt::UTC);
        QDateTime laneDateTime = baseTime.addSecs(laneTime);
        return laneDateTime.toString("yyyy-MM-dd hh:mm:ss");
    }
    
    /**
     * @brief 获取员工号字符串
     * @return 员工号十六进制字符串
     */
    QString getEmployeeNoString() const {
        QString result;
        for (int i = 0; i < 4; ++i) {
            result += QString("%1").arg(employeeNo[i], 2, 16, QChar('0'));
        }
        return result.toUpper();
    }
    
    /**
     * @brief 获取车牌号字符串
     * @return 车牌号字符串
     */
    QString getVLPString() const {
        return QString::fromLatin1(vlp, 12).trimmed();
    }
    
    /**
     * @brief 设置车牌号
     * @param plateNo 车牌号字符串
     */
    void setVLP(const QString& plateNo) {
        memset(vlp, 0, sizeof(vlp));
        QByteArray plateBytes = plateNo.toLatin1();
        int copyLen = qMin(plateBytes.size(), static_cast<int>(sizeof(vlp)));
        memcpy(vlp, plateBytes.constData(), copyLen);
    }
    
    /**
     * @brief 获取车牌颜色描述
     * @return 车牌颜色字符串
     */
    QString getVLPColorString() const {
        switch (vlpColor) {
            case 0x00: return "蓝色";
            case 0x01: return "黄色";
            case 0x02: return "黑色";
            case 0x03: return "白色";
            case 0x04: return "渐变绿";
            case 0x05: return "黄绿双拼色";
            case 0x06: return "蓝白渐变";
            default: return QString("未知颜色(0x%1)").arg(vlpColor, 2, 16, QChar('0'));
        }
    }
    
    /**
     * @brief 获取车辆尺寸信息字符串
     * @return 格式化的车辆尺寸字符串
     */
    QString getVehDimensionsString() const {
        return QString("长:%1 宽:%2 高:%3 其他:%4")
            .arg(vehDimensions[0])
            .arg(vehDimensions[1])
            .arg(vehDimensions[2])
            .arg(vehDimensions[3]);
    }
    
    /**
     * @brief 获取重量字符串（以kg为单位）
     * @return 格式化的重量字符串
     */
    QString getWeightString() const {
        return QString("%1kg").arg(weight);
    }
    
    /**
     * @brief 获取备用字段字符串
     * @return 备用字段字符串
     */
    QString getSpareString() const {
        return QString::fromLatin1(spare, 31).trimmed();
    }
    
    /**
     * @brief 设置车道时间为当前时间
     */
    void setLaneTimeFromCurrent() {
        QDateTime currentTime = QDateTime::currentDateTimeUtc();
        QDateTime baseTime(QDate(2000, 1, 1), QTime(0, 0, 0), Qt::UTC);
        laneTime = baseTime.secsTo(currentTime);
    }
    
    /**
     * @brief 设置车辆尺寸
     * @param length 长度
     * @param width 宽度
     * @param height 高度
     * @param other 其他尺寸
     */
    void setVehDimensions(uint8_t length, uint8_t width, uint8_t height, uint8_t other) {
        vehDimensions[0] = length;
        vehDimensions[1] = width;
        vehDimensions[2] = height;
        vehDimensions[3] = other;
    }
};

/**
 * @struct TVClassConversion
 * @brief 车辆分类转换结构体
 * @details 对应Delphi中的TVClassConversion，包含车辆分类和尺寸信息
 * 
 * 原始Delphi定义：
 * TVClassConversion = packed record
 *   VType: Byte;
 *   VLength: Word;
 *   VHeight: Byte;
 *   VWidth: Byte;
 *   WheelNum: Byte;
 *   AxleNum: Byte;
 *   VWeight: DWord;
 * end;
 */
struct __attribute__((packed)) TVClassConversion {
    uint8_t vType;                  // VType: 车种/车类
    Word vLength;                   // VLength: 车长
    uint8_t vHeight;                // VHeight: 车头高
    uint8_t vWidth;                 // VWidth: 车宽
    uint8_t wheelNum;               // WheelNum: 轮数
    uint8_t axleNum;                // AxleNum: 轴数
    uint32_t vWeight;               // VWeight: 载重/座数
    
    TVClassConversion() {
        memset(this, 0, sizeof(TVClassConversion));
    }
    
    /**
     * @brief 获取车型描述
     * @return 车型字符串
     */
    QString getVTypeString() const {
        switch (vType) {
            case 1: return "一类车(小客车)";
            case 2: return "二类车(中客车)";
            case 3: return "三类车(大客车)";
            case 4: return "四类车(小货车)";
            case 5: return "五类车(中货车)";
            case 6: return "六类车(大货车)";
            default: return QString("未知车型(%1)").arg(vType);
        }
    }
    
    /**
     * @brief 获取车辆尺寸字符串
     * @return 格式化的尺寸信息
     */
    QString getDimensionsString() const {
        return QString("长:%1cm 高:%2cm 宽:%3cm")
            .arg(vLength)
            .arg(vHeight)
            .arg(vWidth);
    }
    
    /**
     * @brief 获取车轮和车轴信息
     * @return 格式化的车轮车轴信息
     */
    QString getWheelAxleString() const {
        return QString("轮数:%1 轴数:%2")
            .arg(wheelNum)
            .arg(axleNum);
    }
    
    /**
     * @brief 获取载重信息字符串
     * @return 格式化的载重信息
     */
    QString getVWeightString() const {
        if (vType >= 1 && vType <= 3) {
            // 客车显示座位数
            return QString("%1座").arg(vWeight);
        } else {
            // 货车显示载重(可能是kg)
            return QString("%1kg").arg(vWeight);
        }
    }
    
    /**
     * @brief 获取完整的车辆信息
     * @return 完整的车辆信息字符串
     */
    QString getCompleteVehicleInfo() const {
        return QString("%1, %2, %3, %4")
            .arg(getVTypeString())
            .arg(getDimensionsString())
            .arg(getWheelAxleString())
            .arg(getVWeightString());
    }
    
    /**
     * @brief 验证车辆参数的合理性
     * @return 验证结果和问题描述
     */
    QString validateVehicleData() const {
        QStringList issues;
        
        // 检查车型范围
        if (vType < 1 || vType > 6) {
            issues << QString("车型无效(%1)").arg(vType);
        }
        
        // 检查尺寸合理性
        if (vLength == 0) {
            issues << "车长不能为0";
        } else if (vLength > 3000) {  // 假设最大30米
            issues << QString("车长过大(%1cm)").arg(vLength);
        }
        
        if (vHeight == 0) {
            issues << "车高不能为0";
        } else if (vHeight > 500) {  // 假设最大5米
            issues << QString("车高过大(%1cm)").arg(vHeight);
        }
        
        if (vWidth == 0) {
            issues << "车宽不能为0";
        } else if (vWidth > 300) {  // 假设最大3米
            issues << QString("车宽过大(%1cm)").arg(vWidth);
        }
        
        // 检查轮数和轴数的合理性
        if (wheelNum == 0) {
            issues << "轮数不能为0";
        } else if (wheelNum > 20) {  // 假设最大20轮
            issues << QString("轮数过多(%1)").arg(wheelNum);
        }
        
        if (axleNum == 0) {
            issues << "轴数不能为0";
        } else if (axleNum > 10) {  // 假设最大10轴
            issues << QString("轴数过多(%1)").arg(axleNum);
        }
        
        // 检查轮数和轴数的关系（通常轮数是轴数的2倍）
        if (wheelNum > 0 && axleNum > 0 && wheelNum != axleNum * 2) {
            issues << QString("轮数(%1)和轴数(%2)关系异常").arg(wheelNum).arg(axleNum);
        }
        
        // 检查载重/座数
        if (vWeight == 0) {
            issues << "载重/座数不能为0";
        }
        
        if (issues.isEmpty()) {
            return "车辆参数验证通过";
        } else {
            return QString("车辆参数验证失败: %1").arg(issues.join(", "));
        }
    }
    
    /**
     * @brief 设置车辆尺寸
     * @param length 车长(cm)
     * @param height 车高(cm)  
     * @param width 车宽(cm)
     */
    void setDimensions(uint16_t length, uint8_t height, uint8_t width) {
        vLength = length;
        vHeight = height;
        vWidth = width;
    }
    
    /**
     * @brief 设置车轮和车轴信息
     * @param wheels 轮数
     * @param axles 轴数
     */
    void setWheelAxle(uint8_t wheels, uint8_t axles) {
        wheelNum = wheels;
        axleNum = axles;
    }
    
    /**
     * @brief 根据车型自动设置典型参数
     * @param vehicleType 车型(1-6)
     */
    void setTypicalParametersByType(uint8_t vehicleType) {
        vType = vehicleType;
        
        switch (vehicleType) {
            case 1: // 一类车(小客车)
                setDimensions(450, 150, 180);  // 4.5m×1.5m×1.8m
                setWheelAxle(4, 2);
                vWeight = 5;  // 5座
                break;
            case 2: // 二类车(中客车)
                setDimensions(700, 180, 220);  // 7m×1.8m×2.2m
                setWheelAxle(6, 3);
                vWeight = 19; // 19座
                break;
            case 3: // 三类车(大客车)
                setDimensions(1200, 200, 250); // 12m×2m×2.5m
                setWheelAxle(6, 3);
                vWeight = 50; // 50座
                break;
            case 4: // 四类车(小货车)
                setDimensions(600, 200, 200);  // 6m×2m×2m
                setWheelAxle(6, 3);
                vWeight = 5000; // 5吨
                break;
            case 5: // 五类车(中货车)
                setDimensions(1000, 250, 250); // 10m×2.5m×2.5m
                setWheelAxle(10, 5);
                vWeight = 15000; // 15吨
                break;
            case 6: // 六类车(大货车)
                setDimensions(1800, 300, 250); // 18m×3m×2.5m
                setWheelAxle(18, 9);
                vWeight = 40000; // 40吨
                break;
            default:
                // 保持默认的零值
                break;
        }
    }
};

/**
 * @class CommonDataStructuresValidator
 * @brief 公共数据结构验证器
 * @details 提供各种数据结构的验证功能
 */
class CommonDataStructuresValidator {
public:
    /**
     * @struct ValidationResult
     * @brief 验证结果结构
     */
    struct ValidationResult {
        bool isValid;           // 是否有效
        QStringList errors;     // 错误列表
        
        ValidationResult() : isValid(true) {}
    };
    
    /**
     * @brief 验证TPSAMInfo结构
     * @param info PSAM信息
     * @return 验证结果
     */
    static ValidationResult validateTPSAMInfo(const TPSAMInfo& info) {
        ValidationResult result;
        
        // 检查PSAM ID是否为0
        if (info.psamId == 0) {
            result.isValid = false;
            result.errors << "PSAM ID不能为0";
        }
        
        // 检查通道号是否有效
        if (info.psamChannel == 0 || info.psamChannel > 8) {
            result.isValid = false;
            result.errors << "PSAM通道号无效(1-8)";
        }
        
        return result;
    }
    
    /**
     * @brief 验证TB0Info结构
     * @param info B0信息
     * @return 验证结果
     */
    static ValidationResult validateTB0Info(const TB0Info& info) {
        ValidationResult result;
        
        // 检查PSAM数量
        if (info.psamNum1 > 8) {
            result.isValid = false;
            result.errors << "PSAM数量不能超过8个";
        }
        
        // 检查RSU ID是否全为0
        bool rsuIdAllZero = true;
        for (int i = 0; i < 3; ++i) {
            if (info.rsuid[i] != 0) {
                rsuIdAllZero = false;
                break;
            }
        }
        if (rsuIdAllZero) {
            result.isValid = false;
            result.errors << "RSU ID不能全为0";
        }
        
        return result;
    }
    
    /**
     * @brief 验证TBAPSAMInfo结构
     * @param info BA PSAM信息
     * @return 验证结果
     */
    static ValidationResult validateTBAPSAMInfo(const TBAPSAMInfo& info) {
        ValidationResult result;
        
        // 检查通道ID
        if (info.channelID == 0) {
            result.isValid = false;
            result.errors << "通道ID不能为0";
        }
        
        // 检查终端号是否全零
        bool terminalAllZero = true;
        for (int i = 0; i < 6; ++i) {
            if (info.terminalNo[i] != 0) {
                terminalAllZero = false;
                break;
            }
        }
        if (terminalAllZero) {
            result.isValid = false;
            result.errors << "终端号不能全为零";
        }
        
        // 检查序列号是否全零
        bool serialAllZero = true;
        for (int i = 0; i < 10; ++i) {
            if (info.serialNo[i] != 0) {
                serialAllZero = false;
                break;
            }
        }
        if (serialAllZero) {
            result.isValid = false;
            result.errors << "序列号不能全为零";
        }
        
        return result;
    }
};

/**
 * @typedef TCardStru
 * @brief TIC0012的别名
 * @details 对应Delphi中的TCardStru = TIC0012，提供兼容性别名
 */
using TCardStru = TIC0012;

} // namespace ETC

#endif // COMMONDATASTRUCTURES_H
