@echo off
echo ========================================
echo 测试RSU设备控制系统的新初始化流程
echo ========================================

echo.
echo 测试场景说明：
echo 1. 正常配置加载 - 应该先加载配置，再初始化目录
echo 2. 配置加载失败 - 应该在当前目录创建错误日志文件
echo.

echo ========================================
echo 测试1: 正常配置加载流程
echo ========================================
echo 命令: RSU.exe --configFile config.json
echo 预期行为:
echo   1. 启用日志缓存模式
echo   2. 加载系统配置成功
echo   3. 初始化应用程序目录
echo   4. 禁用日志缓存并刷新到文件
echo   5. 系统正常启动
echo.

echo ========================================
echo 测试2: 配置加载失败流程
echo ========================================
echo 命令: RSU.exe --configFile test_invalid_config.ini
echo 预期行为:
echo   1. 启用日志缓存模式
echo   2. 尝试加载配置文件失败
echo   3. 在当前目录创建 "RSU设备控制系统.log" 文件
echo   4. 将缓存的日志信息写入错误日志文件
echo   5. 程序退出，返回码 -1
echo.

echo ========================================
echo 测试3: 配置文件不存在的情况
echo ========================================
echo 命令: RSU.exe --configFile nonexistent.ini
echo 预期行为:
echo   1. 启用日志缓存模式
echo   2. 配置文件不存在，加载失败
echo   3. 在当前目录创建 "RSU设备控制系统.log" 文件
echo   4. 将缓存的日志信息写入错误日志文件
echo   5. 程序退出，返回码 -1
echo.

echo ========================================
echo 测试4: 无参数启动但无配置文件
echo ========================================
echo 说明: 临时重命名config.json和config.ini文件
echo 命令: RSU.exe
echo 预期行为:
echo   1. 启用日志缓存模式
echo   2. 在当前目录查找配置文件失败
echo   3. 在当前目录创建 "RSU设备控制系统.log" 文件
echo   4. 将缓存的日志信息写入错误日志文件
echo   5. 程序退出，返回码 -1
echo.

echo 注意事项：
echo - 实际测试需要先编译项目: make clean && make
echo - 测试前请备份重要的配置文件
echo - 错误日志文件会在当前目录生成
echo - 每次测试后检查生成的日志文件内容
echo.

echo 验证要点：
echo 1. 检查初始化顺序是否正确（配置加载在目录初始化之前）
echo 2. 检查错误日志文件是否包含完整的缓存日志信息
echo 3. 检查错误日志文件的格式和内容是否正确
echo 4. 检查程序退出码是否为 -1
echo.

pause
