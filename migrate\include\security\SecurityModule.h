/**
 * @file SecurityModule.h
 * @brief ETC系统安全验证模块
 * 
 * 本文件从Delphi SecureVerify2.pas迁移而来，提供PSAM卡安全认证功能
 * 支持PSAM签到、认证、结果上报等安全验证流程
 */

#ifndef SECURITYMODULE_H
#define SECURITYMODULE_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QSslError>
#include <QJsonObject>
#include <QJsonDocument>
#include <QString>
#include <QTimer>
#include <QMutex>
#include <QDateTime>
#include <QMap>
#include <memory>

namespace ETC {

/**
 * URL参数结构
 */
struct URLParams {
    QString ip;                     // 服务器IP
    QString port;                   // 端口
    QString psamSign;               // PSAM签到接口路径
    QString psamAuth;               // PSAM认证接口路径
    QString psamResult;             // PSAM结果接口路径
    QString signUrlBak;             // 备用签到URL
    QString authUrlBak;             // 备用认证URL
    QString resultUrlBak;           // 备用结果URL
    
    URLParams()
        : port("8080")
        , psamSign("/api/psam/sign")
        , psamAuth("/api/psam/auth")
        , psamResult("/api/psam/result")
    {}
};

/**
 * PSAM签到数据结构
 * 对应原Delphi代码中的TPsameSign
 */
struct PSAMSignData {
    QString psamNo;                 // PSAM卡号
    QString terminalNo;             // 终端号
    QString provinceCode;           // 省份代码
    QString roadCode;               // 路段代码
    QString roadName;               // 路段名称
    QString stationCode;            // 收费站代码
    QString stationName;            // 收费站名称
    QString stationType;            // 收费站类型
    QString laneNo;                 // 车道号
    QString laneType;               // 车道类型
    QString terminalTime;           // 终端时间
    
    PSAMSignData() {}
    
    // 转换为JSON对象
    QJsonObject toJson() const;
    
    // 从JSON对象创建
    static PSAMSignData fromJson(const QJsonObject& json);
    
    // 验证数据有效性
    bool isValid() const;
    
    // 格式化为字符串
    QString toString() const;
};

/**
 * PSAM认证数据结构
 * 对应原Delphi代码中的TPsameAuth
 */
struct PSAMAuthData {
    QString psamNo;                 // PSAM卡号
    QString random;                 // 随机数
    QString provinceCode;           // 省份代码
    QString roadCode;               // 路段代码
    QString roadName;               // 路段名称
    QString stationCode;            // 收费站代码
    QString stationName;            // 收费站名称
    QString stationType;            // 收费站类型
    QString laneNo;                 // 车道号
    QString laneType;               // 车道类型
    QString terminalTime;           // 终端时间
    
    PSAMAuthData() {}
    
    // 转换为JSON对象
    QJsonObject toJson() const;
    
    // 从JSON对象创建
    static PSAMAuthData fromJson(const QJsonObject& json);
    
    // 验证数据有效性
    bool isValid() const;
    
    // 格式化为字符串
    QString toString() const;
};

/**
 * PSAM结果数据结构
 */
struct PSAMResultData {
    QString psamNo;                 // PSAM卡号
    QString terminalNo;             // 终端号
    QString resultCode;             // 结果代码
    QString resultMessage;          // 结果消息
    QString signTime;               // 签到时间
    QString authTime;               // 认证时间
    QString resultTime;             // 结果时间
    
    PSAMResultData() {}
    
    // 转换为JSON对象
    QJsonObject toJson() const;
    
    // 从JSON对象创建
    static PSAMResultData fromJson(const QJsonObject& json);
    
    // 验证数据有效性
    bool isValid() const;
    
    // 格式化为字符串
    QString toString() const;
};

/**
 * PSAM签到结果响应
 * 对应原Delphi代码中的TPsameSignResultResp
 */
struct PSAMSignResultResponse {
    QString code;                   // 响应代码
    QString message;                // 响应消息
    QDateTime responseTime;         // 响应时间
    
    PSAMSignResultResponse()
        : responseTime(QDateTime::currentDateTime())
    {}
    
    void clear();
    bool isSuccess() const;
    QString toString() const;
};

/**
 * PSAM认证响应
 * 对应原Delphi代码中的TPsameAuthResp
 */
struct PSAMAuthResponse {
    QString code;                   // 响应代码
    QString message;                // 响应消息
    QString listNo;                 // 列表编号
    QString authInstruction;        // 认证指令
    QDateTime responseTime;         // 响应时间
    
    PSAMAuthResponse()
        : responseTime(QDateTime::currentDateTime())
    {}
    
    void clear();
    bool isSuccess() const;
    QString toString() const;
};

/**
 * 安全验证错误类型
 */
enum class SecurityError {
    NoError,                        // 无错误
    NetworkError,                   // 网络错误
    TimeoutError,                   // 超时错误
    InvalidData,                    // 数据无效
    ServerError,                    // 服务器错误
    AuthenticationFailed,           // 认证失败
    PSAMError,                      // PSAM卡错误
    ConfigurationError              // 配置错误
};

/**
 * 安全验证配置
 */
struct SecurityConfig {
    URLParams urls;                 // URL参数
    int timeoutMs;                  // 超时时间(毫秒)
    int retryCount;                 // 重试次数
    int retryIntervalMs;            // 重试间隔(毫秒)
    bool enableBackupUrls;          // 启用备用URL
    bool enableLogging;             // 启用日志记录
    QString userAgent;              // User Agent
    QMap<QString, QString> headers; // 自定义HTTP头
    
    SecurityConfig()
        : timeoutMs(30000)
        , retryCount(3)
        , retryIntervalMs(1000)
        , enableBackupUrls(true)
        , enableLogging(true)
        , userAgent("ETCSecurityClient/1.0")
    {}
};

/**
 * 安全验证类
 * 对应原Delphi代码中的TSecureVerify
 */
class SecurityVerifier : public QObject {
    Q_OBJECT

public:
    explicit SecurityVerifier(QObject* parent = nullptr);
    explicit SecurityVerifier(const SecurityConfig& config, QObject* parent = nullptr);
    ~SecurityVerifier();

    // 配置管理
    void setConfig(const SecurityConfig& config);
    SecurityConfig getConfig() const;
    void setUrls(const URLParams& params);
    URLParams getUrls() const;

    // PSAM操作
    int sendPSAMSign(const PSAMSignData& signData, PSAMSignResultResponse& response);
    int sendPSAMAuth(const PSAMAuthData& authData, PSAMAuthResponse& response);
    int sendPSAMResult(const PSAMResultData& resultData, PSAMSignResultResponse& response);

    // 异步操作
    void sendPSAMSignAsync(const PSAMSignData& signData);
    void sendPSAMAuthAsync(const PSAMAuthData& authData);
    void sendPSAMResultAsync(const PSAMResultData& resultData);

    // 状态查询
    bool isBusy() const;
    SecurityError getLastError() const;
    QString getLastErrorString() const;

    // 统计信息
    int getSuccessCount() const { return successCount; }
    int getFailureCount() const { return failureCount; }
    void resetStatistics();

signals:
    void psamSignCompleted(const PSAMSignResultResponse& response);
    void psamAuthCompleted(const PSAMAuthResponse& response);
    void psamResultCompleted(const PSAMSignResultResponse& response);
    void errorOccurred(SecurityError error, const QString& message);
    void requestStarted(const QString& operation);
    void requestFinished(const QString& operation, bool success);

private slots:
    void onNetworkReplyFinished();
    void onNetworkError(QNetworkReply::NetworkError error);
    void onSslErrors(QNetworkReply* reply, const QList<QSslError>& errors);
    void onRetryTimer();

private:
    QString sendRequest(const QString& url, const QJsonObject& data);
    QString sendRequestWithRetry(const QString& url, const QJsonObject& data);
    QNetworkRequest createRequest(const QString& url) const;
    void processSignResponse(const QString& response, PSAMSignResultResponse& result);
    void processAuthResponse(const QString& response, PSAMAuthResponse& result);
    void processResultResponse(const QString& response, PSAMSignResultResponse& result);
    void handleError(SecurityError error, const QString& message);
    void writeLog(const QString& message);
    QString formatUrl(const QString& baseUrl, const QString& path) const;

    SecurityConfig config;
    QNetworkAccessManager* networkManager;
    QNetworkReply* currentReply;
    QTimer* retryTimer;
    
    // 状态信息
    bool busy;
    SecurityError lastError;
    QString lastErrorString;
    
    // 统计信息
    int successCount;
    int failureCount;
    QDateTime lastRequestTime;
    
    // 重试状态
    int currentRetryCount;
    QString retryUrl;
    QJsonObject retryData;
    QString retryOperation;
    
    mutable QMutex configMutex;
};

/**
 * PSAM安全管理器
 * 提供多PSAM卡的统一管理
 */
class PSAMSecurityManager : public QObject {
    Q_OBJECT

public:
    explicit PSAMSecurityManager(QObject* parent = nullptr);
    ~PSAMSecurityManager();

    // 安全验证器管理
    void addVerifier(const QString& psamNo, std::shared_ptr<SecurityVerifier> verifier);
    void removeVerifier(const QString& psamNo);
    std::shared_ptr<SecurityVerifier> getVerifier(const QString& psamNo);
    QStringList getVerifierList() const;

    // 批量操作
    void signAllPSAMs(const PSAMSignData& baseData);
    void setGlobalConfig(const SecurityConfig& config);

    // 状态查询
    int getActiveVerifierCount() const;
    QMap<QString, SecurityError> getVerifierStatus() const;

    // 统计信息
    QMap<QString, QPair<int, int>> getStatistics() const;  // success, failure
    QString generateStatusReport() const;

signals:
    void verifierAdded(const QString& psamNo);
    void verifierRemoved(const QString& psamNo);
    void allSignsCompleted();
    void verifierError(const QString& psamNo, SecurityError error, const QString& message);

private slots:
    void onVerifierError(SecurityError error, const QString& message);
    void onSignCompleted(const PSAMSignResultResponse& response);

private:
    void connectVerifierSignals(std::shared_ptr<SecurityVerifier> verifier);
    void writeLog(const QString& message);

    QMap<QString, std::shared_ptr<SecurityVerifier>> verifiers;
    mutable QMutex verifiersMutex;
    SecurityConfig globalConfig;
    
    // 批量操作状态
    int pendingSignCount;
    QStringList completedSigns;
};

/**
 * 安全验证工厂类
 */
class SecurityVerifierFactory {
public:
    static std::shared_ptr<SecurityVerifier> createVerifier(const SecurityConfig& config);
    static std::shared_ptr<SecurityVerifier> createVerifierForPSAM(const QString& psamNo, const URLParams& urls);
    static SecurityConfig createDefaultConfig();
    static SecurityConfig createConfigFromFile(const QString& configFile);
    static bool saveConfigToFile(const SecurityConfig& config, const QString& configFile);

private:
    static void validateConfig(const SecurityConfig& config);
};

/**
 * 安全验证助手类
 * 提供便利的静态方法
 */
class SecurityHelper {
public:
    // 数据验证
    static bool validatePSAMNo(const QString& psamNo);
    static bool validateTerminalNo(const QString& terminalNo);
    static bool validateProvinceCode(const QString& provinceCode);
    static bool validateStationCode(const QString& stationCode);
    
    // 数据格式化
    static QString formatPSAMNo(const QString& psamNo);
    static QString formatTerminalTime(const QDateTime& dateTime);
    static QString generateRandomString(int length = 16);
    
    // 错误处理
    static QString getErrorDescription(SecurityError error);
    static SecurityError parseErrorCode(const QString& code);
    
    // URL处理
    static QString buildUrl(const QString& baseUrl, const QString& path);
    static bool isValidUrl(const QString& url);
    
    // 加密相关（如果需要）
    static QByteArray calculateMD5(const QString& data);
    static QByteArray calculateSHA256(const QString& data);
    static QString encodeBase64(const QByteArray& data);
    static QByteArray decodeBase64(const QString& data);
};

} // namespace ETC

Q_DECLARE_METATYPE(ETC::SecurityError)
Q_DECLARE_METATYPE(ETC::PSAMSignData)
Q_DECLARE_METATYPE(ETC::PSAMAuthData)
Q_DECLARE_METATYPE(ETC::PSAMResultData)
Q_DECLARE_METATYPE(ETC::PSAMSignResultResponse)
Q_DECLARE_METATYPE(ETC::PSAMAuthResponse)

#endif // SECURITYMODULE_H
