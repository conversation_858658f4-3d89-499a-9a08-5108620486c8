@echo off
echo ========================================
echo 测试RSU设备控制系统的新启动方式
echo ========================================

echo.
echo 1. 测试无参数启动（查找当前目录的config.json或config.ini）
echo ----------------------------------------
echo 命令: RSU.exe
echo 预期: 应该找到并加载config.json文件
echo.

echo 2. 测试工作目录指定方式
echo ----------------------------------------
echo 命令: RSU.exe --workdir test_workdir
echo 预期: 应该在test_workdir目录中找到并加载config.ini文件
echo.

echo 3. 测试配置文件指定方式
echo ----------------------------------------
echo 命令: RSU.exe --configFile config.ini
echo 预期: 应该直接加载指定的config.ini文件
echo.

echo 4. 测试新的configFile参数
echo ----------------------------------------
echo 命令: RSU.exe --configFile config.json
echo 预期: 应该直接加载指定的config.json文件
echo.

echo 5. 测试帮助信息
echo ----------------------------------------
echo 命令: RSU.exe --help
echo 预期: 应该显示包含新启动方式的帮助信息
echo.

echo 注意：实际测试需要先编译项目
echo 编译命令: make clean && make
echo.
pause
