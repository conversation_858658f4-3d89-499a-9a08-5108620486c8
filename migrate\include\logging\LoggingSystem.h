/**
 * @file LoggingSystem.h
 * @brief ETC系统日志记录模块
 * 
 * 本文件从Delphi UThreadLog.pas迁移而来，提供线程安全的日志记录功能
 * 支持多级别日志、文件轮转、内存显示、队列缓冲等功能
 */

#ifndef LOGGINGSYSTEM_H
#define LOGGINGSYSTEM_H

#include <QObject>
#include <QThread>
#include <QQueue>
#include <QMutex>
#include <QWaitCondition>
#include <QDateTime>
#include <QString>
#include <QTimer>
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QSettings>
#include <QMap>
#include <QStringList>
#include <memory>

namespace ETC {

/**
 * 日志级别枚举
 */
enum class LogLevel {
    Debug = 0,      // 调试信息
    Info = 1,       // 一般信息
    Warning = 2,    // 警告信息
    Error = 3,      // 错误信息
    Critical = 4    // 严重错误
};

/**
 * 日志记录结构
 * 对应原Delphi代码中的RLog
 */
struct LogRecord {
    QDateTime logTime;      // 日志时间
    QString logInfo;        // 日志信息
    QString logFile;        // 日志文件名
    LogLevel level;         // 日志级别
    QString category;       // 日志分类
    int logInt;            // 附加整数信息
    QString threadId;      // 线程ID
    QString sourceFile;    // 源文件名
    int sourceLine;        // 源文件行号
    
    LogRecord()
        : logTime(QDateTime::currentDateTime())
        , level(LogLevel::Info)
        , logInt(0)
        , sourceLine(0)
    {}
    
    LogRecord(const QString& info, LogLevel lvl = LogLevel::Info, const QString& file = QString())
        : logTime(QDateTime::currentDateTime())
        , logInfo(info)
        , logFile(file)
        , level(lvl)
        , logInt(0)
        , sourceLine(0)
    {}
};

/**
 * 日志配置结构
 */
struct LoggingConfig {
    bool enableFileLogging;         // 启用文件日志
    bool enableConsoleLogging;      // 启用控制台日志
    bool enableMemoryLogging;        // 启用内存日志缓存
    LogLevel minLevel;              // 最小日志级别
    QString logDirectory;           // 日志目录
    QString logFilePattern;         // 日志文件名模式
    int maxFileSize;               // 最大文件大小(MB)
    int maxFileCount;              // 最大文件数量
    int flushInterval;             // 刷新间隔(毫秒)
    int maxQueueSize;              // 最大队列大小
    bool enableTimestamp;          // 启用时间戳
    bool enableThreadInfo;         // 启用线程信息
    bool enableSourceInfo;         // 启用源文件信息
    QString timeFormat;            // 时间格式
    
    LoggingConfig()
        : enableFileLogging(true)
        , enableConsoleLogging(true)
        , enableMemoryLogging(false)
        , minLevel(LogLevel::Info)
        , logDirectory("logs")
        , logFilePattern("app_%1.log")
        , maxFileSize(10)
        , maxFileCount(10)
        , flushInterval(1000)
        , maxQueueSize(10000)
        , enableTimestamp(true)
        , enableThreadInfo(true)
        , enableSourceInfo(false)
        , timeFormat("yyyy-MM-dd hh:mm:ss.zzz")
    {}
};

/**
 * 日志线程类
 * 对应原Delphi代码中的TLogThread
 */
class LogThread : public QThread {
    Q_OBJECT

public:
    explicit LogThread(const LoggingConfig& config, QObject* parent = nullptr);
    ~LogThread();

    // 队列操作
    void pushLog(const LogRecord& record);
    bool popLog(LogRecord& record);
    int getQueueSize() const;
    
    // 线程控制
    void setInterval(int intervalMs);
    void setCycleInterval(int intervalMs);
    void stopLogging();
    
    // 配置
    void updateConfig(const LoggingConfig& config);
    LoggingConfig getConfig() const;
    
    // 统计信息
    int getCycleCount() const { return cycleCount; }
    qint64 getTotalLogsProcessed() const { return totalLogsProcessed; }
    qint64 getTotalBytesWritten() const { return totalBytesWritten; }
    
    // 日志缓存控制方法（公有接口）
    void enableCacheMode();                    // 启用缓存模式
    void disableCacheMode();                   // 禁用缓存模式并写入缓存的日志

signals:
    void logProcessed(const LogRecord& record);
    void errorOccurred(const QString& error);
    void queueSizeChanged(int size);

protected:
    void run() override;

private slots:
    void onFlushTimer();

private:
    void doWork();
    void processLogRecord(const LogRecord& record);
    void writeToFile(const LogRecord& record);
    void writeToConsole(const LogRecord& record);
    void rotateLogFile();
    void cleanupOldFiles();
    QString formatLogMessage(const LogRecord& record) const;
    QString getLevelString(LogLevel level) const;
    void ensureLogDirectory();
    void mySleep(int ms);
    
    // 日志缓存相关方法（私有实现）
    void flushCachedLogs();                    // 刷新缓存的日志到文件
    void addToCacheIfNeeded(const LogRecord& record);  // 根据需要添加到缓存

    LoggingConfig config;
    QQueue<LogRecord> logQueue;
    mutable QMutex queueMutex;
    QWaitCondition queueCondition;
    mutable QMutex configMutex;
    
    // 日志缓存相关
    QQueue<LogRecord> cachedLogs;      // 缓存的日志记录
    mutable QMutex cacheMutex;         // 缓存互斥锁
    bool directoryReady;               // 目录是否已创建
    bool cacheMode;                    // 是否处于缓存模式
    
    // 文件相关
    QString currentLogFile;
    QFile* logFile;
    QTextStream* logStream;
    qint64 currentFileSize;
    
    // 线程控制
    int interval;
    int cycleInterval;
    int cycleCount;
    bool stopRequested;
    
    // 刷新定时器
    QTimer* flushTimer;
    
    // 统计信息
    qint64 totalLogsProcessed;
    qint64 totalBytesWritten;
    QDateTime lastFlushTime;
    
    static const int DEFAULT_INTERVAL = 100;
    static const int DEFAULT_CYCLE_INTERVAL = 1000;
};

/**
 * 日志记录器类
 * 提供静态接口和多实例支持
 */
class Logger : public QObject {
    Q_OBJECT

public:
    explicit Logger(const QString& name = "default", QObject* parent = nullptr);
    ~Logger();

    // 静态接口
    static Logger* getInstance(const QString& name = "default");
    static void setGlobalConfig(const LoggingConfig& config);
    static LoggingConfig getGlobalConfig();
    
    // 实例方法
    void setConfig(const LoggingConfig& config);
    LoggingConfig getConfig() const;
    
    // 日志记录方法
    void log(LogLevel level, const QString& message, const QString& category = QString());
    void debug(const QString& message, const QString& category = QString());
    void info(const QString& message, const QString& category = QString());
    void warning(const QString& message, const QString& category = QString());
    void error(const QString& message, const QString& category = QString());
    void critical(const QString& message, const QString& category = QString());
    
    // 带源文件信息的日志方法
    void logWithSource(LogLevel level, const QString& message, const char* file, int line, const QString& category = QString());
    
    // 格式化日志方法
    template<typename... Args>
    void logf(LogLevel level, const QString& format, Args&&... args) {
        log(level, QString::asprintf(format.toUtf8().constData(), std::forward<Args>(args)...));
    }
    
    // 内存日志缓存 (控制台版本)
    QStringList getRecentLogs(int maxCount = 100) const;
    void clearMemoryLogs();
    
    // 控制方法
    void start();
    void stop();
    bool isRunning() const;
    void flush();
    
    // 统计信息
    int getQueueSize() const;
    qint64 getTotalLogsProcessed() const;
    qint64 getTotalBytesWritten() const;
    
    // 日志缓存控制
    void enableLogCache();     // 启用日志缓存模式
    void disableLogCache();    // 禁用日志缓存并写入缓存的日志

signals:
    void logRecorded(const LogRecord& record);
    void errorOccurred(const QString& error);

private slots:
    void onLogProcessed(const LogRecord& record);
    void onLogThreadError(const QString& error);

private:
    void initializeLogThread();
    void updateMemoryLogs(const LogRecord& record);

    QString loggerName;
    LogThread* logThread;
    QStringList memoryLogs;     // 内存日志缓存
    mutable QMutex memoryMutex; // 内存日志互斥锁
    int maxMemoryLogs;          // 最大内存日志数量
    LoggingConfig currentConfig;
    
    // 静态成员
    static QMap<QString, Logger*> loggerInstances;
    static QMutex instanceMutex;
    static LoggingConfig globalConfig;
};

/**
 * 日志宏定义
 */
#define LOG_DEBUG(message) \
    Logger::getInstance()->logWithSource(ETC::LogLevel::Debug, message, __FILE__, __LINE__)

#define LOG_INFO(message) \
    Logger::getInstance()->logWithSource(ETC::LogLevel::Info, message, __FILE__, __LINE__)

#define LOG_WARNING(message) \
    Logger::getInstance()->logWithSource(ETC::LogLevel::Warning, message, __FILE__, __LINE__)

#define LOG_ERROR(message) \
    Logger::getInstance()->logWithSource(ETC::LogLevel::Error, message, __FILE__, __LINE__)

#define LOG_CRITICAL(message) \
    Logger::getInstance()->logWithSource(ETC::LogLevel::Critical, message, __FILE__, __LINE__)

#define LOG_DEBUGF(format, ...) \
    Logger::getInstance()->logf(ETC::LogLevel::Debug, format, ##__VA_ARGS__)

#define LOG_INFOF(format, ...) \
    Logger::getInstance()->logf(ETC::LogLevel::Info, format, ##__VA_ARGS__)

#define LOG_WARNINGF(format, ...) \
    Logger::getInstance()->logf(ETC::LogLevel::Warning, format, ##__VA_ARGS__)

#define LOG_ERRORF(format, ...) \
    Logger::getInstance()->logf(ETC::LogLevel::Error, format, ##__VA_ARGS__)

#define LOG_CRITICALF(format, ...) \
    Logger::getInstance()->logf(ETC::LogLevel::Critical, format, ##__VA_ARGS__)

/**
 * 时间转换工具类
 * 对应原Delphi代码中的时间转换函数
 */
class TimeUtils {
public:
    // Unix时间戳转换（从2000年开始，对应原Delphi代码）
    static qint32 dateTimeToUnixDate(const QDateTime& dateTime, int timeZone = 8);
    static QDateTime unixDateToDateTime(qint32 unixDate, int timeZone = 8);
    
    // 标准Unix时间戳转换（从1970年开始）
    static qint64 dateTimeToUnixTimestamp(const QDateTime& dateTime);
    static QDateTime unixTimestampToDateTime(qint64 timestamp);
    
    // 格式化时间
    static QString formatDateTime(const QDateTime& dateTime, const QString& format = "yyyy-MM-dd hh:mm:ss.zzz");
    static QString formatDuration(qint64 milliseconds);
    
    // 时区处理
    static QDateTime toBeijingTime(const QDateTime& utcTime);
    static QDateTime toBeiJingTime(const QDateTime& localTime);
    static QDateTime toShanghaiTime(const QDateTime& utcTime);
};

/**
 * 日志过滤器
 * 提供日志过滤和查询功能
 */
class LogFilter {
public:
    LogFilter();
    
    // 过滤条件设置
    void setLevelFilter(LogLevel minLevel, LogLevel maxLevel = LogLevel::Critical);
    void setCategoryFilter(const QStringList& categories);
    void setTimeRangeFilter(const QDateTime& startTime, const QDateTime& endTime);
    void setTextFilter(const QString& text, bool caseSensitive = false);
    void setThreadFilter(const QStringList& threadIds);
    
    // 过滤器应用
    bool matchRecord(const LogRecord& record) const;
    void clearFilters();
    
    // 预定义过滤器
    static LogFilter createErrorFilter();
    static LogFilter createCategoryFilter(const QString& category);
    static LogFilter createTimeRangeFilter(const QDateTime& start, const QDateTime& end);

private:
    bool levelFilterEnabled;
    LogLevel minLevel;
    LogLevel maxLevel;
    
    bool categoryFilterEnabled;
    QStringList allowedCategories;
    
    bool timeRangeFilterEnabled;
    QDateTime startTime;
    QDateTime endTime;
    
    bool textFilterEnabled;
    QString filterText;
    bool caseSensitive;
    
    bool threadFilterEnabled;
    QStringList allowedThreads;
};

/**
 * 日志管理器
 * 提供全局日志管理功能
 */
class LogManager : public QObject {
    Q_OBJECT

public:
    static LogManager* getInstance();
    
    // 全局配置
    void setGlobalConfig(const LoggingConfig& config);
    LoggingConfig getGlobalConfig() const;
    
    // 日志记录器管理
    Logger* getLogger(const QString& name = "default");
    QStringList getLoggerNames() const;
    void removeLogger(const QString& name);
    
    // 全局控制
    void startAllLoggers();
    void stopAllLoggers();
    void flushAllLoggers();
    
    // 统计信息
    QMap<QString, qint64> getLoggingStatistics() const;
    QString generateStatusReport() const;
    
    // 配置管理
    void loadConfig(const QString& configFile);
    void saveConfig(const QString& configFile) const;

signals:
    void configChanged(const LoggingConfig& config);
    void loggerAdded(const QString& name);
    void loggerRemoved(const QString& name);

private:
    LogManager(QObject* parent = nullptr);
    ~LogManager();
    
    static LogManager* instance;
    static QMutex instanceMutex;
    
    QMap<QString, Logger*> loggers;
    mutable QMutex loggersMutex;
    LoggingConfig globalConfig;
};

} // namespace ETC

Q_DECLARE_METATYPE(ETC::LogLevel)
Q_DECLARE_METATYPE(ETC::LogRecord)

#endif // LOGGINGSYSTEM_H
