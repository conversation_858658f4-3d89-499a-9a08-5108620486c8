/**
 * @file OBUEventConnector.h
 * @brief OBU设备事件连接器
 * 
 * 本文件从Delphi uOBUEventConnector.pas迁移而来
 * 负责将OBU设备的事件与事件处理框架连接起来
 * 提供统一的设备管理接口和多设备管理功能
 */

#ifndef OBUEVENTCONNECTOR_H
#define OBUEVENTCONNECTOR_H

#include <QObject>
#include <QMap>
#include <QList>
#include <QTimer>
#include <QMutex>
#include <QSettings>
#include <QJsonObject>
#include <QJsonDocument>
#include <memory>
#include "core/OBUDevice.h"
#include "core/EventFramework.h"
#include "core/EventHandlers.h"
#include "CommonDataStructures.h"

namespace ETC {

/**
 * OBU设备事件连接器类
 * 对应原Delphi代码中的TOBUEventConnector
 */
class OBUEventConnector : public QObject {
    Q_OBJECT

public:
    explicit OBUEventConnector(std::shared_ptr<OBUDevice> obuDevice, 
                              const QString& deviceId, 
                              QObject* parent = nullptr);
    ~OBUEventConnector();

    // 连接器管理
    void connect();
    void disconnect();
    bool isConnected() const { return connected; }

    // 设备操作接口
    bool startDevice();
    bool stopDevice();
    QString getDeviceStatus() const;
    
    // 属性访问
    QString getDeviceId() const { return deviceId; }
    bool isEnabled() const { return enabled; }
    void setEnabled(bool value) { enabled = value; }
    std::shared_ptr<OBUDevice> getOBUDevice() const { return obuDevice; }
    
    // 事件统计
    int getEventCount() const { return eventCount; }
    QDateTime getLastEventTime() const { return lastEventTime; }
    void resetEventCount() { eventCount = 0; }

signals:
    void deviceStatusChanged(const QString& deviceId, const QString& status);
    void eventProcessed(const QString& deviceId, const QString& eventType);
    void errorOccurred(const QString& deviceId, const QString& error);

private slots:
    // OBU设备事件处理方法 - 对应原Delphi代码中的各种事件
    void onRSUError(quint8 errorStatus);
    void onGetB0(quint8 rsctl, const TB0Info& b0Info);
    void onGetB1(quint8 rsctl, const TB1Info& b1Info);
    void onGetOBUID(quint8 rsctl, quint32 obuId, const TB2Info& b2Info);
    void onGetB4(quint8 rsctl, quint32 obuId, const TB4Info& b4Info);
    void onGetB5(quint8 rsctl, quint32 obuId, const TB5Info& b5Info);
    void onGetOBUVehicle(quint8 rsctl, quint32 obuId, const QString& plate, quint8 plateCode,
                         int vehicleClass, quint8 userType, const TVClassConversion& conversion);
    void onGetCPUInfo(quint8 rsctl, quint32 obuId, quint32 remainMoney, 
                      const TIC0012& ic0012, const TIC0015& ic0015);
    void onGetCPUInfoNew(quint8 rsctl, quint32 obuId, quint8 obuType, quint8 antennaId,
                         const TB4Content0and1& content0and1, const TIC0019& ic0019,
                         const TB4Content1Station& station1, const TB4Content2& content2);
    void onOBUSessionEnd(quint8 rsctl, quint32 obuId, const QDateTime& sessionTime,
                         quint32 tac, quint16 payCardTranSN, quint32 psamTranSN,
                         const QString& rsuTerminalId, quint8 keyVersion, quint8 transType);
    void onOBUConsumed(quint8 rsctl, quint32 obuId, const QDateTime& purchaseTime,
                       quint32 iccid, quint32 tac, quint32 psamTranSN, quint8 keyVersion);
    void onOBUB7(quint8 rsctl, quint32 obuId, quint8 recordNum, const QString& recordList);
    void onOBUBA(quint8 rsctl, const TBAInfo& baInfo);
    void onOBUBB(quint8 rsctl, quint32 obuId, quint8 errorCode, const QVector<TAuthResult>& authResults);
    void onOBUD0(quint8 rsctl, quint32 obuId, quint8 errorCode, int x, int y);
    void onOBUError(quint8 rsctl, quint32 obuId, quint8 state, quint8 errorCode);
    void onRSUReset(quint8 rsctl, const QString& terminalId, quint8 algId, quint16 manuId, 
                    const QString& rsuId, quint16 version);
    void onRSUHeart();
    void onPurTimeOut(quint8 rsctl, quint32 obuId, quint8 state, quint8 errorCode,
                      quint16 payCardTranSN, quint32 psamTranSN, const QString& rsuTerminalId,
                      quint8 transType);
    void onPSAMInit(quint8 rsctl, const QString& psamNo, quint8 psamVersion,
                    const QString& areaCode, const QString& randCode);
    void onPSAMResult(quint8 rsctl, quint16 sw1sw2);

private:
    void writeLog(const QString& message);
    void dispatchEvent(EventType eventType, std::shared_ptr<EventData> eventData);
    void updateEventStatistics(EventType eventType);

    std::shared_ptr<OBUDevice> obuDevice;
    EventManager* eventManager;
    QString deviceId;
    bool enabled;
    bool connected;
    
    // 事件统计
    int eventCount;
    QDateTime lastEventTime;
    QMap<EventType, int> eventTypeCount;
};

/**
 * OBU设备管理器类
 * 对应原Delphi代码中的TOBUDeviceManager
 * 管理多个OBU设备连接器
 */
class OBUDeviceManager : public QObject {
    Q_OBJECT

public:
    explicit OBUDeviceManager(QObject* parent = nullptr);
    ~OBUDeviceManager();

    // 设备管理
    QString addDevice(std::shared_ptr<OBUDevice> obuDevice, const QString& deviceId);
    bool removeDevice(const QString& deviceId);
    OBUEventConnector* getDevice(const QString& deviceId);
    int getDeviceCount() const;
    QStringList getDeviceList() const;

    // 批量操作
    void startAllDevices();
    void stopAllDevices();
    void connectAllDevices();
    void disconnectAllDevices();
    void enableAllDevices(bool enabled = true);

    // 配置管理
    bool loadConfiguration(const QString& configFile);
    bool saveConfiguration(const QString& configFile);
    void setConfigFile(const QString& configFile) { this->configFile = configFile; }
    QString getConfigFile() const { return configFile; }

    // 状态监控
    void generateStatusReport(const QString& reportFile);
    QJsonObject getStatusSummary() const;
    int getActiveDeviceCount() const;
    int getConnectedDeviceCount() const;
    
    // 事件统计
    QMap<QString, int> getDeviceEventCounts() const;
    int getTotalEventCount() const;
    void resetAllEventCounts();

signals:
    void deviceAdded(const QString& deviceId);
    void deviceRemoved(const QString& deviceId);
    void deviceStatusChanged(const QString& deviceId, const QString& status);
    void allDevicesStarted();
    void allDevicesStopped();

private slots:
    void onDeviceStatusChanged(const QString& deviceId, const QString& status);
    void onDeviceEventProcessed(const QString& deviceId, const QString& eventType);
    void onDeviceError(const QString& deviceId, const QString& error);
    void onStatusUpdateTimer();

private:
    void setupStatusUpdateTimer();
    void updateDeviceStatistics();
    QJsonObject deviceConfigToJson(const OBUDeviceConfig& config) const;
    OBUDeviceConfig deviceConfigFromJson(const QJsonObject& json) const;
    void writeLog(const QString& message);

    QMap<QString, OBUEventConnector*> connectors;
    QString configFile;
    mutable QMutex deviceMutex;
    
    // 状态更新定时器
    QTimer* statusUpdateTimer;
    
    // 统计信息
    QMap<QString, QDateTime> lastStatusUpdate;
    QMap<QString, int> deviceErrorCounts;
    int totalEventCount;
    
    static const int STATUS_UPDATE_INTERVAL = 10000;  // 10秒更新一次状态
};

/**
 * OBU设备配置构建器
 * 提供流式API来构建设备配置
 */
class OBUDeviceConfigBuilder {
public:
    OBUDeviceConfigBuilder() = default;
    
    // 基础配置
    OBUDeviceConfigBuilder& setDeviceId(const QString& deviceId);
    OBUDeviceConfigBuilder& setDeviceType(OBUDeviceType type);
    OBUDeviceConfigBuilder& setAutoReconnect(bool autoReconnect);
    OBUDeviceConfigBuilder& setReconnectInterval(int intervalMs);
    
    // 串口配置
    OBUDeviceConfigBuilder& setSerialPort(const QString& portName);
    OBUDeviceConfigBuilder& setBaudRate(int baudRate);
    OBUDeviceConfigBuilder& setDataBits(int dataBits);
    OBUDeviceConfigBuilder& setStopBits(int stopBits);
    OBUDeviceConfigBuilder& setParity(const QString& parity);
    
    // 网络配置
    OBUDeviceConfigBuilder& setServerIP(const QString& ip);
    OBUDeviceConfigBuilder& setServerPort(int port);
    OBUDeviceConfigBuilder& setTimeout(int timeoutMs);
    
    // 设备参数
    OBUDeviceConfigBuilder& setRSCTL(quint8 rsctl);
    OBUDeviceConfigBuilder& setTerminalId(const QString& terminalId);
    
    // 构建配置
    OBUDeviceConfig build() const;
    
    // 预定义配置
    static OBUDeviceConfigBuilder forSerialDevice(const QString& deviceId, const QString& portName);
    static OBUDeviceConfigBuilder forTCPDevice(const QString& deviceId, const QString& serverIP, int port);
    static OBUDeviceConfigBuilder forUDPDevice(const QString& deviceId, const QString& serverIP, int port);

private:
    OBUDeviceConfig config;
};

/**
 * OBU设备状态监控器
 * 提供设备状态的实时监控和报告功能
 */
class OBUDeviceMonitor : public QObject {
    Q_OBJECT

public:
    explicit OBUDeviceMonitor(OBUDeviceManager* deviceManager, QObject* parent = nullptr);
    ~OBUDeviceMonitor();

    // 监控控制
    void startMonitoring(int intervalMs = 5000);
    void stopMonitoring();
    bool isMonitoring() const { return monitoring; }

    // 报告生成
    QString generateTextReport() const;
    QJsonObject generateJsonReport() const;
    void saveReportToFile(const QString& fileName, const QString& format = "json") const;

    // 阈值设置
    void setErrorThreshold(int threshold) { errorThreshold = threshold; }
    void setResponseTimeThreshold(int thresholdMs) { responseTimeThreshold = thresholdMs; }
    
signals:
    void deviceHealthChanged(const QString& deviceId, bool healthy);
    void systemHealthChanged(bool healthy);
    void alertTriggered(const QString& deviceId, const QString& alert);

private slots:
    void onMonitorTimer();
    void checkDeviceHealth();
    void updatePerformanceMetrics();

private:
    void analyzeDevicePerformance(const QString& deviceId);
    void checkSystemThresholds();
    void writeLog(const QString& message) const;

    OBUDeviceManager* deviceManager;
    QTimer* monitorTimer;
    bool monitoring;
    
    // 监控阈值
    int errorThreshold;
    int responseTimeThreshold;
    
    // 性能指标
    QMap<QString, qint64> lastResponseTimes;
    QMap<QString, int> errorCounts;
    QMap<QString, bool> deviceHealthStatus;
    
    // 历史数据
    QMap<QString, QList<qint64>> responseTimeHistory;
    QMap<QString, QList<int>> errorCountHistory;
};

} // namespace ETC

#endif // OBUEVENTCONNECTOR_H
