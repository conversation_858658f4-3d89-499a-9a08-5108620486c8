# RSU设备控制系统启动方式修改总结

## 修改概述

根据需求，对RSU设备控制系统的启动过程进行了全面修改，现在支持四种启动方式：

### 1. 工作目录路径指定方式
```bash
myApp --workdir D:/Devices/RSU001
```
- 在指定目录中查找 `config.json` 或 `config.ini` 文件
- 如果找不到配置文件，记录日志并退出

### 2. 配置文件指定方式
```bash
myApp --configFile D:/Devices/RSU001/config.json
```
- 直接加载指定的配置文件
- 支持 INI 和 JSON 格式
- 如果文件不存在或解析失败，打印错误信息并退出

### 3. 键值对参数启动（保持兼容）
```bash
myApp --GantryGBID=xxx --RSUPort=9527
```
- 保持原有的键值对参数启动方式

### 4. 无参数启动
```bash
myApp
```
- 在当前目录查找 `config.json` 或 `config.ini` 文件
- 如果查找失败或解析失败，打印错误并退出

## 主要修改内容

### 1. 命令行参数解析修改 (main.cpp)
- 添加了 `--workdir` 参数：指定工作目录路径
- 添加了 `--configFile` 参数：指定配置文件路径
- 保留了 `--config` 参数以保持向后兼容
- 更新了帮助信息，显示新的启动方式

### 2. ConfigManager类扩展
#### 新增方法：
- `loadFromWorkDir(const QString& workDir)`: 在指定目录中查找并加载配置文件

#### 修改方法：
- `loadDefaultConfig()`: 简化为只查找 `config.json` 和 `config.ini`

### 3. 配置数据结构扩展 (ConfigData)
#### 新增字段：
- `ServerPort`: 服务器端口
- `WorkDir`: 工作目录路径
- `DeviceNo`: 设备编号
- `HeartbeatInterval`: 心跳间隔

### 4. 配置文件格式支持
完全支持提供的INI和JSON格式示例，包括所有字段的正确解析。

## 文件修改列表

### 修改的文件：
1. `src/main.cpp`
   - 修改 `setupCommandLineParser()` 函数
   - 修改 `loadSystemConfig()` 函数

2. `include/core/ConfigManager.h`
   - 扩展 `ConfigData` 结构
   - 添加 `loadFromWorkDir()` 方法声明

3. `src/core/ConfigManager.cpp`
   - 实现 `loadFromWorkDir()` 方法
   - 修改 `loadDefaultConfig()` 方法
   - 更新 INI 和 JSON 解析代码
   - 更新 `ConfigData` 构造函数和 `printConfig()` 方法

### 新增的测试文件：
1. `config.ini` - INI格式测试配置文件
2. `config.json` - JSON格式测试配置文件
3. `test_workdir/config.ini` - 工作目录测试配置文件
4. `test_startup.bat` - 启动方式测试脚本

## 配置文件示例

### INI格式 (config.ini)
```ini
[Common]
ServerPort=9999
WorkDir=D:\Devices\RSU001
DeviceNo=RSU001
HeartbeatInterval=30
GantryGBID=G006036006003710010
GantryGBHex=380A26
RoadCode=G006036
RoadName=G006036
StationCode=006003710
StationName=006003710
StationType=04
LaneNo=G006036006003710010
LaneType=1

[PSAMUrl]
ip=http://************
port=8950
AuthUrl=http://************:8950/psam-auth-platform/v1/service/api/psam/auth
signUrl=http://************:8950//psam-auth-platform/v1/service/api/psam/sign
resultUrl=http://************:8950//psam-auth-platform/v1/service/api/psam/result
AuthUrlBak=http://************:8950/psam-auth-platform/v1/service/api/psam/auth
signUrlBak=http://************:8950//psam-auth-platform/v1/service/api/psam/sign
resultUrlBak=http://************:8950//psam-auth-platform/v1/service/api/psam/result

[RSU]
Port=9527
Power=30
IP=127.0.0.1
Channel=1
TransferType=2
Used=1
```

### JSON格式 (config.json)
```json
{
  "Common": {
    "ServerPort": 9999,
    "WorkDir": "D:\\Devices\\RSU001",
    "DeviceNo": "RSU001",
    "HeartbeatInterval": 30,
    "GantryGBID": "G006036006003710010",
    "GantryGBHex": "380A26",
    "RoadCode": "G006036",
    "RoadName": "G006036",
    "StationCode": "006003710",
    "StationName": "006003710",
    "StationType": "04",
    "LaneNo": "G006036006003710010",
    "LaneType": "1"
  },
  "PSAMUrl": {
    "ip": "http://************",
    "port": 8950,
    "AuthUrl": "http://************:8950/psam-auth-platform/v1/service/api/psam/auth",
    "signUrl": "http://************:8950//psam-auth-platform/v1/service/api/psam/sign",
    "resultUrl": "http://************:8950//psam-auth-platform/v1/service/api/psam/result",
    "AuthUrlBak": "http://************:8950/psam-auth-platform/v1/service/api/psam/auth",
    "signUrlBak": "http://************:8950//psam-auth-platform/v1/service/api/psam/sign",
    "resultUrlBak": "http://************:8950//psam-auth-platform/v1/service/api/psam/result"
  },
  "RSU": {
    "Port": 9527,
    "Power": 30,
    "IP": "127.0.0.1",
    "Channel": 1,
    "TransferType": 2,
    "Used": 1
  }
}
```

## 测试方法

1. 编译项目：
   ```bash
   make clean && make
   ```

2. 测试各种启动方式：
   ```bash
   # 无参数启动
   ./RSU
   
   # 工作目录启动
   ./RSU --workdir test_workdir
   
   # 配置文件启动
   ./RSU --configFile config.ini
   ./RSU --configFile config.json
   
   # 查看帮助
   ./RSU --help
   ```

## 向后兼容性

- 保留了原有的 `--config` 参数
- 保留了键值对参数启动方式
- 保留了原有的配置数据结构字段
- 新增字段都有合理的默认值

## 注意事项

1. 配置文件查找优先级：`config.json` > `config.ini`
2. 所有配置文件都支持UTF-8编码
3. JSON格式支持数字和字符串类型的值
4. 工作目录必须存在，否则会报错退出
5. 配置文件解析失败会导致程序退出并显示错误信息
