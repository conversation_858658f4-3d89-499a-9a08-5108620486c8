/**
 * @file OBUDevice.cpp
 * @brief ETC系统OBU设备管理模块实现
 */

#include "core/OBUDevice.h"
#include <QDebug>
#include <QDir>
#include <QStandardPaths>
#include <QMutexLocker>
#include <QSerialPortInfo>
#include <QHostAddress>
#include <QDataStream>
#include <algorithm>

namespace ETC {

// CRC16计算表 - 对应原Delphi代码中的CRCTable16
const quint16 CRCTable16[256] = {
    0x0000, 0x8005, 0x800F, 0x000A, 0x801B, 0x001E, 0x0014, 0x8011, 0x8033, 0x0036, 0x003C, 0x8039, 0x0028, 0x802D, 0x8027, 0x0022, 0x8063, 0x0066, 0x006C, 0x8069, 0x0078, 0x807D, 0x80D7, 0x00D2, 0x00F0, 0x80F5, 0x80FF, 0x00FA, 0x80EB, 0x00EE, 0x00E4, 0x80E1, 0x00A0, 0x80A5, 0x80AF, 0x00AA, 0x80BB, 0x00BE, 0x00B4, 0x80B1, 0x8093, 0x0096, 0x009C, 0x8099, 0x0088, 0x808D, 0x8087, 0x0082, 0x8183, 0x0186, 0x018C,
    0x8189, 0x0198, 0x019D, 0x8197, 0x0192, 0x01B0, 0x81B5, 0x81BF, 0x01BA, 0x81AB, 0x01AE, 0x01A4, 0x81A1, 0x01E0, 0x81E5, 0x81EF, 0x01EA, 0x81FB, 0x01FE, 0x01F4, 0x81F1, 0x81D3, 0x01D6, 0x01DC, 0x81D9, 0x01C8, 0x81CD, 0x81C7, 0x01C2, 0x0140, 0x8145, 0x814F, 0x014A, 0x815B, 0x015E, 0x0154, 0x8151, 0x8173, 0x0176, 0x017C, 0x8179, 0x0168, 0x816D, 0x8167, 0x0162, 0x8123, 0x8126, 0x812C, 0x8129, 0x0138, 0x813D, 0x8137, 0x0132, 0x0110, 0x8115, 0x811F, 0x011A, 0x810B, 0x810E, 0x8104, 0x8101, 0x8303, 0x0306, 0x030C, 0x8309, 0x0318, 0x831D, 0x8317, 0x0312, 0x0330, 0x8335, 0x833F,
    0x033A, 0x832B, 0x832E, 0x8324, 0x8321, 0x0360, 0x8365, 0x836F, 0x036A, 0x837B, 0x837E, 0x8374, 0x8371, 0x8353, 0x8356, 0x835C, 0x8359, 0x8348, 0x834D, 0x8347, 0x8342, 0x03C0, 0x83C5, 0x83CF, 0x03CA, 0x83DB, 0x83DE, 0x83D4, 0x83D1, 0x83F3, 0x83F6, 0x83FC, 0x83F9, 0x03E8, 0x83ED, 0x83E7, 0x83E2, 0x83A3, 0x83A6, 0x83AC, 0x83A9, 0x83B8, 0x83BD, 0x83B7, 0x83B2, 0x8390, 0x8395, 0x839F, 0x839A, 0x838B, 0x838E, 0x8384, 0x8381, 0x0280, 0x8285, 0x828F, 0x028A, 0x829B, 0x829E, 0x8294, 0x8291, 0x82B3, 0x82B6, 0x82BC, 0x82B9, 0x02A8, 0x82AD, 0x82A7, 0x02A2, 0x82E3, 0x82E6, 0x82EC,
    0x82E9, 0x02F8, 0x82FD, 0x82F7, 0x02F2, 0x02D0, 0x82D5, 0x82DF, 0x02DA, 0x82CB, 0x02CE, 0x02C4, 0x82C1, 0x8243, 0x8246, 0x824C, 0x8249, 0x8258, 0x825D, 0x8257, 0x8252, 0x8270, 0x8275, 0x827F, 0x827A, 0x826B, 0x826E, 0x8264, 0x8261, 0x0220, 0x8225, 0x822F, 0x022A, 0x823B, 0x823E, 0x8234, 0x8231, 0x8213, 0x8216, 0x821C, 0x8219, 0x0208, 0x820D, 0x8207, 0x8202
};

// OBUDevice 实现
OBUDevice::OBUDevice(const OBUDeviceConfig& config, QObject* parent)
    : QObject(parent)
    , config(config)
    , status(OBUDeviceStatus::Disconnected)
    , serialPort(nullptr)
    , tcpClient(nullptr)
    , udpSocket(nullptr)
    , bytesSent(0)
    , bytesReceived(0)
    , commandCount(0)
{
    // 创建重连定时器
    reconnectTimer = new QTimer(this);
    reconnectTimer->setSingleShot(true);
    connect(reconnectTimer, &QTimer::timeout, this, &OBUDevice::onReconnectTimer);
    
    // 创建心跳定时器
    heartbeatTimer = new QTimer(this);
    heartbeatTimer->setInterval(HEARTBEAT_INTERVAL);
    connect(heartbeatTimer, &QTimer::timeout, this, [this]() {
        if (isConnected()) {
            rsuHeart();
        }
    });
    
    writeLog(QString("创建OBU设备: %1, 类型: %2")
             .arg(config.deviceId)
             .arg(static_cast<int>(config.deviceType)));
}

OBUDevice::~OBUDevice() {
    closeDevice();
    writeLog(QString("销毁OBU设备: %1").arg(config.deviceId));
}

bool OBUDevice::openDevice() {
    if (isConnected()) {
        return true;
    }
    
    writeLog(QString("打开OBU设备: %1").arg(config.deviceId));
    setStatus(OBUDeviceStatus::Connecting);
    
    bool result = false;
    
    switch (config.deviceType) {
        case OBUDeviceType::SerialPort:
            setupSerialPort();
            result = serialPort && serialPort->open(QIODevice::ReadWrite);
            break;
            
        case OBUDeviceType::TCPClient:
            setupTcpConnection();
            if (tcpClient) {
                tcpClient->setServerIP(config.serverIP);
                tcpClient->setPort(config.serverPort);
                tcpClient->setActive(true);
                result = true;  // TCP连接是异步的
            }
            break;
            
        case OBUDeviceType::UDPClient:
            setupUdpConnection();
            result = udpSocket && udpSocket->bind();
            break;
    }
    
    if (result) {
        setStatus(OBUDeviceStatus::Connected);
        lastActivityTime = QDateTime::currentDateTime();
        heartbeatTimer->start();
        writeLog(QString("OBU设备连接成功: %1").arg(config.deviceId));
        emit deviceConnected();
    } else {
        setStatus(OBUDeviceStatus::Error);
        writeLog(QString("OBU设备连接失败: %1").arg(config.deviceId));
        
        if (config.autoReconnect) {
            reconnectTimer->start(config.reconnectInterval);
        }
    }
    
    return result;
}

bool OBUDevice::closeDevice() {
    if (!isConnected()) {
        return true;
    }
    
    writeLog(QString("关闭OBU设备: %1").arg(config.deviceId));
    
    heartbeatTimer->stop();
    reconnectTimer->stop();
    
    switch (config.deviceType) {
        case OBUDeviceType::SerialPort:
            if (serialPort) {
                serialPort->close();
                serialPort->deleteLater();
                serialPort = nullptr;
            }
            break;
            
        case OBUDeviceType::TCPClient:
            if (tcpClient) {
                tcpClient->setActive(false);
                tcpClient->deleteLater();
                tcpClient = nullptr;
            }
            break;
            
        case OBUDeviceType::UDPClient:
            if (udpSocket) {
                udpSocket->close();
                udpSocket->deleteLater();
                udpSocket = nullptr;
            }
            break;
    }
    
    setStatus(OBUDeviceStatus::Disconnected);
    emit deviceDisconnected();
    
    return true;
}

bool OBUDevice::isConnected() const {
    switch (config.deviceType) {
        case OBUDeviceType::SerialPort:
            return serialPort && serialPort->isOpen();
            
        case OBUDeviceType::TCPClient:
            return tcpClient && tcpClient->isConnected();
            
        case OBUDeviceType::UDPClient:
            return udpSocket && udpSocket->state() == QAbstractSocket::BoundState;
            
        default:
            return false;
    }
}

OBUDeviceStatus OBUDevice::getStatus() const {
    return status;
}

QString OBUDevice::getStatusText() const {
    switch (status) {
        case OBUDeviceStatus::Disconnected: return "未连接";
        case OBUDeviceStatus::Connecting: return "连接中";
        case OBUDeviceStatus::Connected: return "已连接";
        case OBUDeviceStatus::Error: return "错误";
        case OBUDeviceStatus::Busy: return "忙碌";
        default: return "未知";
    }
}

void OBUDevice::setConfig(const OBUDeviceConfig& newConfig) {
    bool needReconnect = isConnected() && 
                        (config.deviceType != newConfig.deviceType ||
                         config.serialPortName != newConfig.serialPortName ||
                         config.serverIP != newConfig.serverIP ||
                         config.serverPort != newConfig.serverPort);
    
    config = newConfig;
    
    if (needReconnect) {
        closeDevice();
        openDevice();
    }
}

// RSU操作方法
bool OBUDevice::rsuReset() {
    writeLog("发送RSU重置命令");
    QByteArray command = formatCommand(0x01);  // RSU重置命令
    return sendCommand(command);
}

bool OBUDevice::rsuHeart() {
    writeLog("发送RSU心跳命令", 2);  // 低级别日志
    QByteArray command = formatCommand(0x02);  // RSU心跳命令
    return sendCommand(command);
}

bool OBUDevice::getB0Info(quint8 rsctl) {
    writeLog(QString("获取B0信息, RSCTL: %1").arg(rsctl));
    QByteArray data;
    data.append(rsctl);
    QByteArray command = formatCommand(0x10, data);  // 获取B0信息命令
    return sendCommand(command);
}

bool OBUDevice::getB1Info(quint8 rsctl) {
    writeLog(QString("获取B1信息, RSCTL: %1").arg(rsctl));
    QByteArray data;
    data.append(rsctl);
    QByteArray command = formatCommand(0x11, data);  // 获取B1信息命令
    return sendCommand(command);
}

// OBU操作方法
bool OBUDevice::getOBUID(quint8 rsctl) {
    writeLog(QString("获取OBU ID, RSCTL: %1").arg(rsctl));
    QByteArray data;
    data.append(rsctl);
    QByteArray command = formatCommand(0x20, data);  // 获取OBU ID命令
    return sendCommand(command);
}

bool OBUDevice::getB4Info(quint8 rsctl, quint32 obuId) {
    writeLog(QString("获取B4信息, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    QByteArray command = formatCommand(0x24, data);  // 获取B4信息命令
    return sendCommand(command);
}

bool OBUDevice::getB5Info(quint8 rsctl, quint32 obuId) {
    writeLog(QString("获取B5信息, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    QByteArray command = formatCommand(0x25, data);  // 获取B5信息命令
    return sendCommand(command);
}

bool OBUDevice::getOBUVehicleInfo(quint8 rsctl, quint32 obuId) {
    writeLog(QString("获取OBU车辆信息, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    QByteArray command = formatCommand(0x26, data);  // 获取OBU车辆信息命令
    return sendCommand(command);
}

bool OBUDevice::getCPUInfo(quint8 rsctl, quint32 obuId) {
    writeLog(QString("获取CPU卡信息, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    QByteArray command = formatCommand(0x30, data);  // 获取CPU卡信息命令
    return sendCommand(command);
}

bool OBUDevice::getCPUInfoNew(quint8 rsctl, quint32 obuId) {
    writeLog(QString("获取新版CPU卡信息, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    QByteArray command = formatCommand(0x31, data);  // 获取新版CPU卡信息命令
    return sendCommand(command);
}

// IC卡操作方法
bool OBUDevice::readIC0012(quint8 rsctl, quint32 obuId, TIC0012& ic0012) {
    writeLog(QString("读取IC0012数据, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    QByteArray command = formatCommand(0x40, data);  // 读取IC0012命令
    
    // 这里应该实现同步等待响应的逻辑
    // 为简化，目前只发送命令
    return sendCommand(command);
}

bool OBUDevice::writeIC0012(quint8 rsctl, quint32 obuId, const TIC0012& ic0012) {
    writeLog(QString("写入IC0012数据, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    data.append(reinterpret_cast<const char*>(&ic0012), sizeof(ic0012));
    QByteArray command = formatCommand(0x41, data);  // 写入IC0012命令
    return sendCommand(command);
}

bool OBUDevice::readIC0015(quint8 rsctl, quint32 obuId, TIC0015& ic0015) {
    writeLog(QString("读取IC0015数据, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    QByteArray command = formatCommand(0x42, data);  // 读取IC0015命令
    return sendCommand(command);
}

bool OBUDevice::writeIC0015(quint8 rsctl, quint32 obuId, const TIC0015& ic0015) {
    writeLog(QString("写入IC0015数据, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    data.append(reinterpret_cast<const char*>(&ic0015), sizeof(ic0015));
    QByteArray command = formatCommand(0x43, data);  // 写入IC0015命令
    return sendCommand(command);
}

bool OBUDevice::readIC0019(quint8 rsctl, quint32 obuId, TIC0019& ic0019) {
    writeLog(QString("读取IC0019数据, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    QByteArray command = formatCommand(0x44, data);  // 读取IC0019命令
    return sendCommand(command);
}

bool OBUDevice::writeIC0019(quint8 rsctl, quint32 obuId, const TIC0019& ic0019) {
    writeLog(QString("写入IC0019数据, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    data.append(reinterpret_cast<const char*>(&ic0019), sizeof(ic0019));
    QByteArray command = formatCommand(0x45, data);  // 写入IC0019命令
    return sendCommand(command);
}

// 交易操作方法
bool OBUDevice::startTransaction(quint8 rsctl, quint32 obuId, quint32 fee) {
    writeLog(QString("开始交易, RSCTL: %1, OBUID: %2, 费用: %3").arg(rsctl).arg(obuId).arg(fee));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    data.append(reinterpret_cast<const char*>(&fee), sizeof(fee));
    QByteArray command = formatCommand(0x50, data);  // 开始交易命令
    return sendCommand(command);
}

bool OBUDevice::endTransaction(quint8 rsctl, quint32 obuId) {
    writeLog(QString("结束交易, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    QByteArray command = formatCommand(0x51, data);  // 结束交易命令
    return sendCommand(command);
}

bool OBUDevice::cancelTransaction(quint8 rsctl, quint32 obuId) {
    writeLog(QString("取消交易, RSCTL: %1, OBUID: %2").arg(rsctl).arg(obuId));
    QByteArray data;
    data.append(rsctl);
    data.append(reinterpret_cast<const char*>(&obuId), sizeof(obuId));
    QByteArray command = formatCommand(0x52, data);  // 取消交易命令
    return sendCommand(command);
}

// PSAM操作方法
bool OBUDevice::psamInit(quint8 rsctl, quint8 psamNo) {
    writeLog(QString("PSAM初始化, RSCTL: %1, PSAM: %2").arg(rsctl).arg(psamNo));
    QByteArray data;
    data.append(rsctl);
    data.append(psamNo);
    QByteArray command = formatCommand(0x60, data);  // PSAM初始化命令
    return sendCommand(command);
}

bool OBUDevice::psamAuth(quint8 rsctl, quint8 psamNo, const QByteArray& authData) {
    writeLog(QString("PSAM认证, RSCTL: %1, PSAM: %2").arg(rsctl).arg(psamNo));
    QByteArray data;
    data.append(rsctl);
    data.append(psamNo);
    data.append(authData);
    QByteArray command = formatCommand(0x61, data);  // PSAM认证命令
    return sendCommand(command);
}

bool OBUDevice::getPSAMInfo(quint8 rsctl, quint8 psamNo) {
    writeLog(QString("获取PSAM信息, RSCTL: %1, PSAM: %2").arg(rsctl).arg(psamNo));
    QByteArray data;
    data.append(rsctl);
    data.append(psamNo);
    QByteArray command = formatCommand(0x62, data);  // 获取PSAM信息命令
    return sendCommand(command);
}

// 数据发送方法
bool OBUDevice::sendCommand(const QByteArray& command) {
    if (!isConnected()) {
        writeLog("设备未连接，无法发送命令");
        return false;
    }
    
    bool result = sendData(command.constData(), command.size());
    if (result) {
        commandCount++;
        lastActivityTime = QDateTime::currentDateTime();
    }
    
    return result;
}

bool OBUDevice::sendData(const char* data, int length) {
    if (!isConnected() || !data || length <= 0) {
        return false;
    }
    
    qint64 bytesWritten = 0;
    
    switch (config.deviceType) {
        case OBUDeviceType::SerialPort:
            if (serialPort) {
                bytesWritten = serialPort->write(data, length);
                serialPort->flush();
            }
            break;
            
        case OBUDeviceType::TCPClient:
            if (tcpClient) {
                tcpClient->sendDevData(data, length);
                bytesWritten = length;  // TCP发送是异步的
            }
            break;
            
        case OBUDeviceType::UDPClient:
            if (udpSocket) {
                bytesWritten = udpSocket->writeDatagram(data, length, 
                                                       QHostAddress(config.serverIP), 
                                                       config.serverPort);
            }
            break;
    }
    
    if (bytesWritten > 0) {
        bytesSent += bytesWritten;
        writeLog(QString("发送数据成功，长度: %1").arg(bytesWritten), 2);
        return true;
    } else {
        writeLog(QString("发送数据失败，错误: %1").arg(bytesWritten));
        return false;
    }
}

// 工具方法
quint16 OBUDevice::calculateCRC16(const QByteArray& data) {
    quint16 crc = 0x0000;
    
    for (int i = 0; i < data.size(); ++i) {
        quint8 byte = static_cast<quint8>(data[i]);
        crc = ((crc << 8) ^ CRCTable16[((crc >> 8) ^ byte) & 0xFF]) & 0xFFFF;
    }
    
    return crc;
}

QByteArray OBUDevice::formatCommand(quint8 cmd, const QByteArray& data) {
    QByteArray command;
    
    // 命令格式: [起始标志][长度][命令][数据][CRC16][结束标志]
    command.append(0x7E);  // 起始标志
    
    quint16 length = 1 + data.size() + 2;  // 命令字节 + 数据长度 + CRC长度
    command.append(reinterpret_cast<const char*>(&length), sizeof(length));
    
    command.append(cmd);   // 命令字节
    command.append(data);  // 数据
    
    // 计算CRC（不包括起始和结束标志）
    QByteArray crcData = command.mid(1);  // 从长度字段开始
    quint16 crc = calculateCRC16(crcData);
    command.append(reinterpret_cast<const char*>(&crc), sizeof(crc));
    
    command.append(0x7E);  // 结束标志
    
    return command;
}

QString OBUDevice::formatICCardInfo(const TIC0015& ic0015) {
    return QString("IC0015[卡类型:%1, 卡号:%2, 车牌:%3]")
           .arg(ic0015.cardType)
           .arg(QString(QByteArray(reinterpret_cast<const char*>(ic0015.cardNo), 8).toHex()))
           .arg(QString::fromLatin1(ic0015.vehiclePlate, 16).trimmed());
}

QString OBUDevice::formatICCardInfo(const TIC0012& ic0012) {
    return QString("IC0012[入口站:%1, 车道:%2, 车型:%3, 车牌:%4]")
           .arg(ic0012.entryStation)
           .arg(ic0012.entryLane)
           .arg(ic0012.vClass)
           .arg(QString::fromLatin1(ic0012.vehiclePlate, 16).trimmed());
}

// 私有槽函数
void OBUDevice::onSerialDataReceived() {
    if (!serialPort) {
        return;
    }
    
    QByteArray data = serialPort->readAll();
    if (!data.isEmpty()) {
        bytesReceived += data.size();
        lastActivityTime = QDateTime::currentDateTime();
        processReceivedData(data);
    }
}

void OBUDevice::onSerialError() {
    if (serialPort) {
        QString error = serialPort->errorString();
        writeLog(QString("串口错误: %1").arg(error));
        emit deviceError(error);
        setStatus(OBUDeviceStatus::Error);
        
        if (config.autoReconnect) {
            reconnectTimer->start(config.reconnectInterval);
        }
    }
}

void OBUDevice::onTcpDataReceived(const QByteArray& data) {
    if (!data.isEmpty()) {
        bytesReceived += data.size();
        lastActivityTime = QDateTime::currentDateTime();
        processReceivedData(data);
    }
}

void OBUDevice::onTcpConnected() {
    setStatus(OBUDeviceStatus::Connected);
    writeLog("TCP连接建立");
    emit deviceConnected();
}

void OBUDevice::onTcpDisconnected() {
    setStatus(OBUDeviceStatus::Disconnected);
    writeLog("TCP连接断开");
    emit deviceDisconnected();
    
    if (config.autoReconnect) {
        reconnectTimer->start(config.reconnectInterval);
    }
}

void OBUDevice::onTcpError(const QString& error) {
    writeLog(QString("TCP错误: %1").arg(error));
    emit deviceError(error);
    setStatus(OBUDeviceStatus::Error);
    
    if (config.autoReconnect) {
        reconnectTimer->start(config.reconnectInterval);
    }
}

void OBUDevice::onUdpDataReceived() {
    if (!udpSocket) {
        return;
    }
    
    while (udpSocket->hasPendingDatagrams()) {
        QByteArray data;
        data.resize(static_cast<int>(udpSocket->pendingDatagramSize()));
        
        QHostAddress sender;
        quint16 senderPort;
        
        qint64 bytesRead = udpSocket->readDatagram(data.data(), data.size(), &sender, &senderPort);
        
        if (bytesRead > 0) {
            data.resize(static_cast<int>(bytesRead));
            bytesReceived += bytesRead;
            lastActivityTime = QDateTime::currentDateTime();
            processReceivedData(data);
        }
    }
}

void OBUDevice::onReconnectTimer() {
    if (!isConnected() && config.autoReconnect) {
        writeLog("尝试自动重连");
        openDevice();
    }
}

// 私有方法
void OBUDevice::setupSerialPort() {
    if (serialPort) {
        serialPort->deleteLater();
    }
    
    serialPort = new QSerialPort(this);
    serialPort->setPortName(config.serialPortName);
    serialPort->setBaudRate(config.baudRate);
    serialPort->setDataBits(static_cast<QSerialPort::DataBits>(config.dataBits));
    serialPort->setStopBits(static_cast<QSerialPort::StopBits>(config.stopBits));
    
    // 设置校验位
    if (config.parity == "Even") {
        serialPort->setParity(QSerialPort::EvenParity);
    } else if (config.parity == "Odd") {
        serialPort->setParity(QSerialPort::OddParity);
    } else {
        serialPort->setParity(QSerialPort::NoParity);
    }
    
    connect(serialPort, &QSerialPort::readyRead, this, &OBUDevice::onSerialDataReceived);
    connect(serialPort, &QSerialPort::errorOccurred, this, &OBUDevice::onSerialError);
}

void OBUDevice::setupTcpConnection() {
    if (tcpClient) {
        tcpClient->deleteLater();
    }
    
    tcpClient = new DevTCPClient(this);
    tcpClient->setThreadFileName(config.deviceId);
    
    connect(tcpClient, &DevTCPClient::connected, this, &OBUDevice::onTcpConnected);
    connect(tcpClient, &DevTCPClient::disconnected, this, &OBUDevice::onTcpDisconnected);
    connect(tcpClient, &DevTCPClient::dataReceived, this, &OBUDevice::onTcpDataReceived);
    connect(tcpClient, &DevTCPClient::errorOccurred, this, &OBUDevice::onTcpError);
}

void OBUDevice::setupUdpConnection() {
    if (udpSocket) {
        udpSocket->deleteLater();
    }
    
    udpSocket = new QUdpSocket(this);
    connect(udpSocket, &QUdpSocket::readyRead, this, &OBUDevice::onUdpDataReceived);
}

void OBUDevice::processReceivedData(const QByteArray& data) {
    QMutexLocker locker(&bufferMutex);
    
    receiveBuffer.append(data);
    
    // 限制缓冲区大小
    if (receiveBuffer.size() > MAX_BUFFER_SIZE) {
        receiveBuffer = receiveBuffer.right(MAX_BUFFER_SIZE / 2);
        writeLog("接收缓冲区溢出，清理部分数据");
    }
    
    // 解析完整的消息
    while (receiveBuffer.size() >= 6) {  // 最小消息长度
        int startIndex = receiveBuffer.indexOf(0x7E);
        if (startIndex < 0) {
            receiveBuffer.clear();
            break;
        }
        
        if (startIndex > 0) {
            receiveBuffer.remove(0, startIndex);
        }
        
        if (receiveBuffer.size() < 6) {
            break;
        }
        
        // 读取长度字段
        quint16 length = *reinterpret_cast<const quint16*>(receiveBuffer.constData() + 1);
        int totalLength = 1 + 2 + length + 1;  // 起始标志 + 长度字段 + 数据 + 结束标志
        
        if (receiveBuffer.size() < totalLength) {
            break;  // 数据不完整，等待更多数据
        }
        
        // 检查结束标志
        if (static_cast<uint8_t>(receiveBuffer[totalLength - 1]) != 0x7E) {
            receiveBuffer.remove(0, 1);  // 移除错误的起始标志
            continue;
        }
        
        // 提取完整消息
        QByteArray message = receiveBuffer.left(totalLength);
        receiveBuffer.remove(0, totalLength);
        
        // 解析消息
        parseMessage(message);
    }
}

void OBUDevice::parseMessage(const QByteArray& message) {
    if (message.size() < 6) {
        return;
    }
    
    // 验证CRC
    QByteArray dataForCRC = message.mid(1, message.size() - 4);  // 去掉起始、结束标志和CRC
    quint16 expectedCRC = calculateCRC16(dataForCRC);
    quint16 receivedCRC = *reinterpret_cast<const quint16*>(message.constData() + message.size() - 3);
    
    if (expectedCRC != receivedCRC) {
        writeLog("CRC校验失败");
        return;
    }
    
    // 提取命令和数据
    quint8 cmd = static_cast<quint8>(message[3]);
    QByteArray data = message.mid(4, message.size() - 7);
    
    writeLog(QString("收到消息, 命令: 0x%1, 数据长度: %2").arg(cmd, 2, 16, QChar('0')).arg(data.size()));
    
    // 根据命令类型分发处理
    if (cmd >= 0x01 && cmd <= 0x0F) {
        handleRSUMessage(cmd, data);
    } else if (cmd >= 0x10 && cmd <= 0x5F) {
        handleOBUMessage(cmd, data);
    } else if (cmd >= 0x60 && cmd <= 0x6F) {
        handlePSAMMessage(cmd, data);
    }
}

void OBUDevice::handleRSUMessage(quint8 cmd, const QByteArray& data) {
    switch (cmd) {
        case 0x01:  // RSU错误
            if (data.size() >= 1) {
                quint8 errorStatus = static_cast<quint8>(data[0]);
                emit rsuError(errorStatus);
            }
            break;
            
        case 0x02:  // RSU心跳响应
            emit rsuHeart();
            break;
            
        case 0x03:  // RSU重置完成
            if (data.size() >= 15) {
                // 解析RSU重置信息
                quint8 rsctl = static_cast<quint8>(data[0]);
                QString terminalId = QString::fromLatin1(data.mid(1, 8));
                quint8 algId = static_cast<quint8>(data[9]);
                quint16 manuId = *reinterpret_cast<const quint16*>(data.constData() + 10);
                QString rsuId = QString::fromLatin1(data.mid(12, 8));
                quint16 version = *reinterpret_cast<const quint16*>(data.constData() + 20);
                
                emit rsuReset(rsctl, terminalId, algId, manuId, rsuId, version);
            }
            break;
    }
}

void OBUDevice::handleOBUMessage(quint8 cmd, const QByteArray& data) {
    switch (cmd) {
        case 0x10:  // B0信息响应
            if (data.size() >= sizeof(TB0Info) + 1) {
                quint8 rsctl = static_cast<quint8>(data[0]);
                TB0Info b0Info = *reinterpret_cast<const TB0Info*>(data.constData() + 1);
                emit getB0(rsctl, b0Info);
            }
            break;
            
        case 0x20:  // OBU ID响应
            if (data.size() >= sizeof(TB2Info) + 5) {
                quint8 rsctl = static_cast<quint8>(data[0]);
                quint32 obuId = *reinterpret_cast<const quint32*>(data.constData() + 1);
                TB2Info b2Info = *reinterpret_cast<const TB2Info*>(data.constData() + 5);
                emit getOBUID(rsctl, obuId, b2Info);
            }
            break;
            
        case 0x50:  // OBU错误
            if (data.size() >= 7) {
                quint8 rsctl = static_cast<quint8>(data[0]);
                quint32 obuId = *reinterpret_cast<const quint32*>(data.constData() + 1);
                quint8 state = static_cast<quint8>(data[5]);
                quint8 errorCode = static_cast<quint8>(data[6]);
                emit obuError(rsctl, obuId, state, errorCode);
            }
            break;
            
        // 其他OBU消息的处理...
    }
}

void OBUDevice::handlePSAMMessage(quint8 cmd, const QByteArray& data) {
    switch (cmd) {
        case 0x60:  // PSAM初始化响应
            if (data.size() >= 20) {
                quint8 rsctl = static_cast<quint8>(data[0]);
                QString psamNo = QString::fromLatin1(data.mid(1, 8));
                quint8 psamVersion = static_cast<quint8>(data[9]);
                QString areaCode = QString::fromLatin1(data.mid(10, 4));
                QString randCode = QString::fromLatin1(data.mid(14, 8));
                
                emit psamInit(rsctl, psamNo, psamVersion, areaCode, randCode);
            }
            break;
            
        case 0x61:  // PSAM认证结果
            if (data.size() >= 3) {
                quint8 rsctl = static_cast<quint8>(data[0]);
                quint16 sw1sw2 = *reinterpret_cast<const quint16*>(data.constData() + 1);
                emit psamResult(rsctl, sw1sw2);
            }
            break;
            
        // 其他PSAM消息的处理...
    }
}

void OBUDevice::writeLog(const QString& message, int level) {
    Q_UNUSED(level)

    QString logMessage = QString("[%1] %2: %3")
                        .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                        .arg(config.deviceId)
                        .arg(message);
    
    qDebug() << logMessage;
    
    // 可以在这里添加文件日志记录
}

void OBUDevice::setStatus(OBUDeviceStatus newStatus) {
    if (status != newStatus) {
        status = newStatus;
        emit statusChanged(status);
    }
}

// OBUDeviceFactory 实现
std::shared_ptr<OBUDevice> OBUDeviceFactory::createDevice(const OBUDeviceConfig& config) {
    validateConfig(config);
    return std::make_shared<OBUDevice>(config);
}

OBUDeviceConfig OBUDeviceFactory::createSerialConfig(const QString& portName, int baudRate) {
    OBUDeviceConfig config;
    config.deviceType = OBUDeviceType::SerialPort;
    config.serialPortName = portName;
    config.baudRate = baudRate;
    return config;
}

OBUDeviceConfig OBUDeviceFactory::createTcpConfig(const QString& serverIP, int port) {
    OBUDeviceConfig config;
    config.deviceType = OBUDeviceType::TCPClient;
    config.serverIP = serverIP;
    config.serverPort = port;
    return config;
}

OBUDeviceConfig OBUDeviceFactory::createUdpConfig(const QString& serverIP, int port) {
    OBUDeviceConfig config;
    config.deviceType = OBUDeviceType::UDPClient;
    config.serverIP = serverIP;
    config.serverPort = port;
    return config;
}

void OBUDeviceFactory::validateConfig(const OBUDeviceConfig& config) {
    if (config.deviceId.isEmpty()) {
        throw std::invalid_argument("设备ID不能为空");
    }
    
    switch (config.deviceType) {
        case OBUDeviceType::SerialPort:
            if (config.serialPortName.isEmpty()) {
                throw std::invalid_argument("串口名称不能为空");
            }
            break;
            
        case OBUDeviceType::TCPClient:
        case OBUDeviceType::UDPClient:
            if (config.serverIP.isEmpty()) {
                throw std::invalid_argument("服务器IP不能为空");
            }
            if (config.serverPort <= 0 || config.serverPort > 65535) {
                throw std::invalid_argument("端口号无效");
            }
            break;
    }
}

} // namespace ETC
