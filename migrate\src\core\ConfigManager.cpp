/**
 * @file ConfigManager.cpp
 * @brief RSU设备控制系统配置管理器实现
 * 
 * <AUTHOR>
 * @date 2024-12-20
 * @version 1.0.0
 */

#include "core/ConfigManager.h"
#include <QSettings>
#include <QJsonDocument>
#include <QJsonObject>
#include <QFile>
#include <QDir>
#include <QDebug>
#include <QRegularExpression>
#include <QCoreApplication>

/**
 * @brief ConfigData构造函数，设置默认值
 */
ConfigManager::ConfigData::ConfigData()
{
    // 设置默认值
    ServerPort = "9999";
    WorkDir = "D:\\Devices\\RSU001";
    DeviceNo = "RSU001";
    HeartbeatInterval = "30";
    GantryGBID = "G006036006003710010";
    GantryGBHex = "380A26";
    RoadCode = "G006036";
    RoadName = "G006036";
    StationCode = "006003710";
    StationName = "006003710";
    StationType = "04";
    LaneNo = "G006036006003710010";
    LaneType = "1";

    PSAMUrlIp = "http://************";
    PSAMUrlPort = "8950";
    AuthUrlBak = "http://************:8950/psam-auth-platform/v1/service/api/psam/auth";
    SignUrlBak = "http://************:8950//psam-auth-platform/v1/service/api/psam/sign";
    ResultUrlBak = "http://************:8950//psam-auth-platform/v1/service/api/psam/result";
    AuthListUrl = "https://************:8890/api/v1/upload";

    RSUPort = "9527";
    RSUPower = "30";
    RSUIp = "127.0.0.1";
    RSUChannel = "1";
    RSUTransferType = "2";
    RSUUsed = "1";

    // 设置默认工作目录
    directory = QDir::currentPath();
}

/**
 * @brief 验证配置数据完整性
 * @return 验证是否通过
 */
bool ConfigManager::ConfigData::validate() const
{
    // 检查必要字段是否为空
    if (GantryGBID.isEmpty() || GantryGBHex.isEmpty() || 
        RoadCode.isEmpty() || StationCode.isEmpty() ||
        PSAMUrlIp.isEmpty() || PSAMUrlPort.isEmpty() ||
        RSUIp.isEmpty() || RSUPort.isEmpty()) {
        return false;
    }
    
    // 检查GantryGBID格式（应该是18位）
    if (GantryGBID.length() != 18) {
        qWarning() << "GantryGBID长度不正确，应为18位:" << GantryGBID;
        return false;
    }
    
    // 检查端口号格式
    bool ok;
    int port = RSUPort.toInt(&ok);
    if (!ok || port <= 0 || port > 65535) {
        qWarning() << "RSU端口号格式不正确:" << RSUPort;
        return false;
    }
    
    port = PSAMUrlPort.toInt(&ok);
    if (!ok || port <= 0 || port > 65535) {
        qWarning() << "PSAM端口号格式不正确:" << PSAMUrlPort;
        return false;
    }
    
    return true;
}

/**
 * @brief 打印配置信息
 */
void ConfigManager::ConfigData::printConfig() const
{
    qInfo() << "=== 系统配置信息 ===";
    qInfo() << "[Common]";
    qInfo() << "  ServerPort:" << ServerPort;
    qInfo() << "  WorkDir:" << WorkDir;
    qInfo() << "  DeviceNo:" << DeviceNo;
    qInfo() << "  HeartbeatInterval:" << HeartbeatInterval;
    qInfo() << "  GantryGBID:" << GantryGBID;
    qInfo() << "  GantryGBHex:" << GantryGBHex;
    qInfo() << "  RoadCode:" << RoadCode;
    qInfo() << "  RoadName:" << RoadName;
    qInfo() << "  StationCode:" << StationCode;
    qInfo() << "  StationName:" << StationName;
    qInfo() << "  StationType:" << StationType;
    qInfo() << "  LaneNo:" << LaneNo;
    qInfo() << "  LaneType:" << LaneType;
    qInfo() << "  directory:" << directory;
    
    qInfo() << "[PSAMUrl]";
    qInfo() << "  ip:" << PSAMUrlIp;
    qInfo() << "  port:" << PSAMUrlPort;
    qInfo() << "  AuthUrlBak:" << AuthUrlBak;
    qInfo() << "  signUrlBak:" << SignUrlBak;
    qInfo() << "  resultUrlBak:" << ResultUrlBak;
    qInfo() << "  authListUrl:" << AuthListUrl;
    
    qInfo() << "[RSU]";
    qInfo() << "  Port:" << RSUPort;
    qInfo() << "  Power:" << RSUPower;
    qInfo() << "  Ip:" << RSUIp;
    qInfo() << "  Channel:" << RSUChannel;
    qInfo() << "  TransferType:" << RSUTransferType;
    qInfo() << "  Used:" << RSUUsed;
    qInfo() << "===================";
}

/**
 * @brief 获取单例实例
 * @return ConfigManager实例引用
 */
ConfigManager& ConfigManager::getInstance()
{
    static ConfigManager instance;
    return instance;
}

/**
 * @brief 从配置文件加载配置
 * @param configFilePath 配置文件路径
 * @return 加载是否成功
 */
bool ConfigManager::loadFromFile(const QString& configFilePath)
{
    if (configFilePath.isEmpty()) {
        qWarning() << "配置文件路径为空";
        return false;
    }
    
    QFile file(configFilePath);
    if (!file.exists()) {
        qWarning() << "配置文件不存在:" << configFilePath;
        return false;
    }
    
    m_configFilePath = configFilePath;
    
    // 根据文件扩展名判断格式
    if (configFilePath.endsWith(".json", Qt::CaseInsensitive)) {
        return loadFromJsonFile(configFilePath);
    } else if (configFilePath.endsWith(".ini", Qt::CaseInsensitive) || 
               configFilePath.endsWith(".conf", Qt::CaseInsensitive)) {
        return loadFromIniFile(configFilePath);
    } else {
        // 尝试按JSON格式解析
        if (loadFromJsonFile(configFilePath)) {
            return true;
        }
        // JSON解析失败，尝试INI格式
        return loadFromIniFile(configFilePath);
    }
}

/**
 * @brief 从命令行参数加载配置
 * @param args 命令行参数列表
 * @return 加载是否成功
 */
bool ConfigManager::loadFromCommandLine(const QStringList& args)
{
    QMap<QString, QString> keyValues = parseKeyValueArgs(args);
    if (keyValues.isEmpty()) {
        qWarning() << "未找到有效的键值对参数";
        return false;
    }
    
    applyKeyValues(keyValues);
    generateDerivedFields();
    
    m_loaded = true;
    qInfo() << "从命令行参数加载配置成功，共" << keyValues.size() << "个参数";
    return true;
}

/**
 * @brief 查找并加载默认配置文件
 * @return 加载是否成功
 */
bool ConfigManager::loadDefaultConfig()
{
    QStringList defaultFiles = {
        "config.json",
        "config.ini"
    };
    
    QString currentDir = QDir::currentPath();
    
    for (const QString& fileName : defaultFiles) {
        QString fullPath = QDir(currentDir).absoluteFilePath(fileName);
        if (QFile::exists(fullPath)) {
            qInfo() << "找到默认配置文件:" << fullPath;
            if (loadFromFile(fullPath)) {
                return true;
            }
        }
    }
    
    qWarning() << "未找到默认配置文件，使用内置默认配置";
    qWarning() << "查找路径:" << currentDir;
    qWarning() << "查找文件:" << defaultFiles.join(", ");
    
    // 使用默认配置
    m_config = ConfigData();
    m_loaded = true;
    return false; // 返回false表示未找到文件，但系统可以继续运行
}

/**
 * @brief 在指定目录中查找并加载配置文件
 * @param workDir 工作目录路径
 * @return 加载是否成功
 */
bool ConfigManager::loadFromWorkDir(const QString& workDir)
{
    QStringList configFiles = {
        "config.json",
        "config.ini"
    };

    QDir dir(workDir);
    if (!dir.exists()) {
        qCritical() << "指定的工作目录不存在:" << workDir;
        return false;
    }

    for (const QString& fileName : configFiles) {
        QString fullPath = dir.absoluteFilePath(fileName);
        if (QFile::exists(fullPath)) {
            qInfo() << "在工作目录中找到配置文件:" << fullPath;
            if (loadFromFile(fullPath)) {
                return true;
            } else {
                qWarning() << "配置文件加载失败:" << fullPath;
            }
        }
    }

    qCritical() << "在工作目录中未找到有效的配置文件:" << workDir;
    qCritical() << "查找的文件:" << configFiles.join(", ");
    return false;
}

/**
 * @brief 获取配置值
 * @param key 配置键名
 * @param defaultValue 默认值
 * @return 配置值
 */
QVariant ConfigManager::getValue(const QString& key, const QVariant& defaultValue) const
{
    return m_values.value(key, defaultValue);
}

/**
 * @brief 设置配置值
 * @param key 配置键名
 * @param value 配置值
 */
void ConfigManager::setValue(const QString& key, const QVariant& value)
{
    m_values[key] = value;
}

/**
 * @brief 从INI文件加载配置
 * @param filePath INI文件路径
 * @return 加载是否成功
 */
bool ConfigManager::loadFromIniFile(const QString& filePath)
{
    QSettings settings(filePath, QSettings::IniFormat);
    settings.setIniCodec("UTF-8");
    
    if (settings.status() != QSettings::NoError) {
        qWarning() << "INI文件读取失败:" << filePath;
        return false;
    }
    
    // 读取Common配置
    settings.beginGroup("Common");
    m_config.ServerPort = settings.value("ServerPort", m_config.ServerPort).toString();
    m_config.WorkDir = settings.value("WorkDir", m_config.WorkDir).toString();
    m_config.DeviceNo = settings.value("DeviceNo", m_config.DeviceNo).toString();
    m_config.HeartbeatInterval = settings.value("HeartbeatInterval", m_config.HeartbeatInterval).toString();
    m_config.GantryGBID = settings.value("GantryGBID", m_config.GantryGBID).toString();
    m_config.GantryGBHex = settings.value("GantryGBHex", m_config.GantryGBHex).toString();
    m_config.RoadCode = settings.value("RoadCode", m_config.RoadCode).toString();
    m_config.RoadName = settings.value("RoadName", m_config.RoadName).toString();
    m_config.StationCode = settings.value("StationCode", m_config.StationCode).toString();
    m_config.StationName = settings.value("StationName", m_config.StationName).toString();
    m_config.StationType = settings.value("StationType", m_config.StationType).toString();
    m_config.LaneNo = settings.value("LaneNo", m_config.LaneNo).toString();
    m_config.LaneType = settings.value("LaneType", m_config.LaneType).toString();
    m_config.directory = settings.value("directory", m_config.directory).toString();
    settings.endGroup();
    
    // 读取PSAMUrl配置
    settings.beginGroup("PSAMUrl");
    m_config.PSAMUrlIp = settings.value("ip", m_config.PSAMUrlIp).toString();
    m_config.PSAMUrlPort = settings.value("port", m_config.PSAMUrlPort).toString();
    m_config.AuthUrlBak = settings.value("AuthUrlBak", m_config.AuthUrlBak).toString();
    m_config.SignUrlBak = settings.value("signUrlBak", m_config.SignUrlBak).toString();
    m_config.ResultUrlBak = settings.value("resultUrlBak", m_config.ResultUrlBak).toString();
    m_config.AuthListUrl = settings.value("authListUrl", m_config.AuthListUrl).toString();
    settings.endGroup();
    
    // 读取RSU配置
    settings.beginGroup("RSU");
    m_config.RSUPort = settings.value("Port", m_config.RSUPort).toString();
    m_config.RSUPower = settings.value("Power", m_config.RSUPower).toString();
    m_config.RSUIp = settings.value("IP", m_config.RSUIp).toString();
    m_config.RSUChannel = settings.value("Channel", m_config.RSUChannel).toString();
    m_config.RSUTransferType = settings.value("TransferType", m_config.RSUTransferType).toString();
    m_config.RSUUsed = settings.value("Used", m_config.RSUUsed).toString();
    settings.endGroup();
    
    generateDerivedFields();
    
    if (!m_config.validate()) {
        qWarning() << "INI配置文件数据验证失败";
        return false;
    }
    
    m_loaded = true;
    qInfo() << "从INI文件加载配置成功:" << filePath;
    return true;
}

/**
 * @brief 从JSON文件加载配置
 * @param filePath JSON文件路径
 * @return 加载是否成功
 */
bool ConfigManager::loadFromJsonFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "无法打开JSON文件:" << filePath;
        return false;
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON解析失败:" << error.errorString();
        return false;
    }
    
    QJsonObject root = doc.object();
    
    // 读取Common配置
    if (root.contains("Common")) {
        QJsonObject common = root["Common"].toObject();
        m_config.ServerPort = common.value("ServerPort").toString(m_config.ServerPort);
        m_config.WorkDir = common.value("WorkDir").toString(m_config.WorkDir);
        m_config.DeviceNo = common.value("DeviceNo").toString(m_config.DeviceNo);
        m_config.HeartbeatInterval = common.value("HeartbeatInterval").toString(m_config.HeartbeatInterval);
        m_config.GantryGBID = common.value("GantryGBID").toString(m_config.GantryGBID);
        m_config.GantryGBHex = common.value("GantryGBHex").toString(m_config.GantryGBHex);
        m_config.RoadCode = common.value("RoadCode").toString(m_config.RoadCode);
        m_config.RoadName = common.value("RoadName").toString(m_config.RoadName);
        m_config.StationCode = common.value("StationCode").toString(m_config.StationCode);
        m_config.StationName = common.value("StationName").toString(m_config.StationName);
        m_config.StationType = common.value("StationType").toString(m_config.StationType);
        m_config.LaneNo = common.value("LaneNo").toString(m_config.LaneNo);
        m_config.LaneType = common.value("LaneType").toString(m_config.LaneType);
        m_config.directory = common.value("directory").toString(m_config.directory);
    }
    
    // 读取PSAMUrl配置
    if (root.contains("PSAMUrl")) {
        QJsonObject psamUrl = root["PSAMUrl"].toObject();
        m_config.PSAMUrlIp = psamUrl.value("ip").toString(m_config.PSAMUrlIp);
        m_config.PSAMUrlPort = psamUrl.value("port").toString(m_config.PSAMUrlPort);
        m_config.AuthUrlBak = psamUrl.value("AuthUrlBak").toString(m_config.AuthUrlBak);
        m_config.SignUrlBak = psamUrl.value("signUrlBak").toString(m_config.SignUrlBak);
        m_config.ResultUrlBak = psamUrl.value("resultUrlBak").toString(m_config.ResultUrlBak);
        m_config.AuthListUrl = psamUrl.value("authListUrl").toString(m_config.AuthListUrl);
    }
    
    // 读取RSU配置
    if (root.contains("RSU")) {
        QJsonObject rsu = root["RSU"].toObject();
        m_config.RSUPort = rsu.value("Port").toString(m_config.RSUPort);
        m_config.RSUPower = rsu.value("Power").toString(m_config.RSUPower);
        m_config.RSUIp = rsu.value("Ip").toString(m_config.RSUIp);
        m_config.RSUChannel = rsu.value("Channel").toString(m_config.RSUChannel);
        m_config.RSUTransferType = rsu.value("TransferType").toString(m_config.RSUTransferType);
        m_config.RSUUsed = rsu.value("Used").toString(m_config.RSUUsed);
    }
    
    generateDerivedFields();
    
    if (!m_config.validate()) {
        qWarning() << "JSON配置文件数据验证失败";
        return false;
    }
    
    m_loaded = true;
    qInfo() << "从JSON文件加载配置成功:" << filePath;
    return true;
}

/**
 * @brief 解析命令行键值对参数
 * @param args 参数列表
 * @return 解析的键值对映射
 */
QMap<QString, QString> ConfigManager::parseKeyValueArgs(const QStringList& args)
{
    QMap<QString, QString> keyValues;
    
    // 匹配 --key=value 格式的正则表达式
    QRegularExpression regex(R"(^--([^=]+)=(.*)$)");
    
    for (const QString& arg : args) {
        QRegularExpressionMatch match = regex.match(arg);
        if (match.hasMatch()) {
            QString key = match.captured(1);
            QString value = match.captured(2);
            keyValues[key] = value;
            qDebug() << "解析参数:" << key << "=" << value;
        }
    }
    
    return keyValues;
}

/**
 * @brief 应用键值对到配置数据
 * @param keyValues 键值对映射
 */
void ConfigManager::applyKeyValues(const QMap<QString, QString>& keyValues)
{
    // 应用Common配置
    if (keyValues.contains("GantryGBID")) m_config.GantryGBID = keyValues["GantryGBID"];
    if (keyValues.contains("GantryGBHex")) m_config.GantryGBHex = keyValues["GantryGBHex"];
    if (keyValues.contains("RoadCode")) m_config.RoadCode = keyValues["RoadCode"];
    if (keyValues.contains("RoadName")) m_config.RoadName = keyValues["RoadName"];
    if (keyValues.contains("StationCode")) m_config.StationCode = keyValues["StationCode"];
    if (keyValues.contains("StationName")) m_config.StationName = keyValues["StationName"];
    if (keyValues.contains("StationType")) m_config.StationType = keyValues["StationType"];
    if (keyValues.contains("LaneNo")) m_config.LaneNo = keyValues["LaneNo"];
    if (keyValues.contains("LaneType")) m_config.LaneType = keyValues["LaneType"];
    if (keyValues.contains("directory")) m_config.directory = keyValues["directory"];
    
    // 应用PSAM URL配置
    if (keyValues.contains("PSAMUrlip")) m_config.PSAMUrlIp = keyValues["PSAMUrlip"];
    if (keyValues.contains("port")) m_config.PSAMUrlPort = keyValues["port"];
    if (keyValues.contains("AuthUrlBak")) m_config.AuthUrlBak = keyValues["AuthUrlBak"];
    if (keyValues.contains("signUrlBak")) m_config.SignUrlBak = keyValues["signUrlBak"];
    if (keyValues.contains("resultUrlBak")) m_config.ResultUrlBak = keyValues["resultUrlBak"];
    if (keyValues.contains("authListUrl")) m_config.AuthListUrl = keyValues["authListUrl"];
    
    // 应用RSU配置
    if (keyValues.contains("RSUPort")) m_config.RSUPort = keyValues["RSUPort"];
    if (keyValues.contains("RSUPower")) m_config.RSUPower = keyValues["RSUPower"];
    if (keyValues.contains("RSUIp")) m_config.RSUIp = keyValues["RSUIp"];
    if (keyValues.contains("RSUChannel")) m_config.RSUChannel = keyValues["RSUChannel"];
    if (keyValues.contains("RSUTransferType")) m_config.RSUTransferType = keyValues["RSUTransferType"];
    if (keyValues.contains("RSUUsed")) m_config.RSUUsed = keyValues["RSUUsed"];
}

/**
 * @brief 从配置数据生成路段和站点信息
 */
void ConfigManager::generateDerivedFields()
{
    // 如果RoadCode为空，从GantryGBID提取前7位
    if (m_config.RoadCode.isEmpty() && m_config.GantryGBID.length() >= 7) {
        m_config.RoadCode = m_config.GantryGBID.left(7);
    }
    
    // 如果RoadName为空，使用RoadCode
    if (m_config.RoadName.isEmpty()) {
        m_config.RoadName = m_config.RoadCode;
    }
    
    // 如果StationCode为空，从GantryGBID提取第8-16位
    if (m_config.StationCode.isEmpty() && m_config.GantryGBID.length() >= 16) {
        m_config.StationCode = m_config.GantryGBID.mid(7, 9);
    }
    
    // 如果StationName为空，使用StationCode
    if (m_config.StationName.isEmpty()) {
        m_config.StationName = m_config.StationCode;
    }
    
    // 如果LaneNo为空，使用GantryGBID
    if (m_config.LaneNo.isEmpty()) {
        m_config.LaneNo = m_config.GantryGBID;
    }
}