/**
 * @file OBUEventConnector.cpp
 * @brief OBU设备事件连接器实现
 */

#include "core/OBUEventConnector.h"
#include <QDebug>
#include <QJsonArray>
#include <QJsonDocument>
#include <QFile>
#include <QDir>
#include <QTextStream>
#include <QMutexLocker>
#include <QStandardPaths>
#include <QCoreApplication>

namespace ETC {

// OBUEventConnector 实现
OBUEventConnector::OBUEventConnector(std::shared_ptr<OBUDevice> obuDevice, 
                                     const QString& deviceId, 
                                     QObject* parent)
    : QObject(parent)
    , obuDevice(obuDevice)
    , deviceId(deviceId)
    , enabled(true)
    , connected(false)
    , eventCount(0)
{
    eventManager = EventManager::getInstance();
    writeLog(QString("创建OBU设备连接器: %1").arg(deviceId));
}

OBUEventConnector::~OBUEventConnector() {
    disconnect();
    writeLog(QString("销毁OBU设备连接器: %1").arg(deviceId));
}

void OBUEventConnector::connect() {
    if (!obuDevice) {
        writeLog("OBU设备对象未分配");
        return;
    }

    if (connected) {
        return;
    }

    writeLog(QString("连接OBU设备事件: %1").arg(deviceId));

    // 连接所有设备信号到对应的槽函数
    // 使用明确的信号签名避免重载问题
    QObject::connect(obuDevice.get(), 
                     static_cast<void(OBUDevice::*)(quint8)>(&OBUDevice::rsuError), 
                     this, &OBUEventConnector::onRSUError);
    QObject::connect(obuDevice.get(), 
                     static_cast<void(OBUDevice::*)(quint8, const TB0Info&)>(&OBUDevice::getB0), 
                     this, &OBUEventConnector::onGetB0);
    QObject::connect(obuDevice.get(), 
                     static_cast<void(OBUDevice::*)(quint8, const TB1Info&)>(&OBUDevice::getB1), 
                     this, &OBUEventConnector::onGetB1);
    QObject::connect(obuDevice.get(), 
                     static_cast<void(OBUDevice::*)(quint8, quint32, const TB2Info&)>(&OBUDevice::getOBUID), 
                     this, &OBUEventConnector::onGetOBUID);
    QObject::connect(obuDevice.get(), 
                     static_cast<void(OBUDevice::*)(quint8, quint32, const TB4Info&)>(&OBUDevice::getB4), 
                     this, &OBUEventConnector::onGetB4);
    QObject::connect(obuDevice.get(), 
                     static_cast<void(OBUDevice::*)(quint8, quint32, const TB5Info&)>(&OBUDevice::getB5), 
                     this, &OBUEventConnector::onGetB5);
    QObject::connect(obuDevice.get(), 
                     static_cast<void(OBUDevice::*)(quint8, quint32, const QString&, quint8, int, quint8, const TVClassConversion&)>(&OBUDevice::getOBUVehicle), 
                     this, &OBUEventConnector::onGetOBUVehicle);
    QObject::connect(obuDevice.get(), 
                     static_cast<void(OBUDevice::*)(quint8, quint32, quint32, const TIC0012&, const TIC0015&)>(&OBUDevice::getCPUInfo), 
                     this, &OBUEventConnector::onGetCPUInfo);
    QObject::connect(obuDevice.get(), 
                     static_cast<void(OBUDevice::*)(quint8, quint32, quint8, quint8, const TB4Content0and1&, const TIC0019&, const TB4Content1Station&, const TB4Content2&)>(&OBUDevice::getCPUInfoNew), 
                     this, &OBUEventConnector::onGetCPUInfoNew);
    QObject::connect(obuDevice.get(), &OBUDevice::obuSessionEnd, this, &OBUEventConnector::onOBUSessionEnd);
    QObject::connect(obuDevice.get(), &OBUDevice::obuConsumed, this, &OBUEventConnector::onOBUConsumed);
    QObject::connect(obuDevice.get(), &OBUDevice::obuB7, this, &OBUEventConnector::onOBUB7);
    QObject::connect(obuDevice.get(), &OBUDevice::obuBA, this, &OBUEventConnector::onOBUBA);
    QObject::connect(obuDevice.get(), &OBUDevice::obuBB, this, &OBUEventConnector::onOBUBB);
    QObject::connect(obuDevice.get(), &OBUDevice::obuD0, this, &OBUEventConnector::onOBUD0);
    QObject::connect(obuDevice.get(), &OBUDevice::obuError, this, &OBUEventConnector::onOBUError);
    QObject::connect(obuDevice.get(), 
                     static_cast<void(OBUDevice::*)(quint8, const QString&, quint8, quint16, const QString&, quint16)>(&OBUDevice::rsuReset), 
                     this, &OBUEventConnector::onRSUReset);
    // 注意：如果rsuHeart信号不存在，可能需要在OBUDevice.h中添加 void rsuHeart() 信号
    // 暂时注释掉这个连接直到确认信号存在
    // QObject::connect(obuDevice.get(), &OBUDevice::rsuHeart, this, &OBUEventConnector::onRSUHeart);
    QObject::connect(obuDevice.get(), &OBUDevice::purTimeOut, this, &OBUEventConnector::onPurTimeOut);
    QObject::connect(obuDevice.get(), 
                     static_cast<void(OBUDevice::*)(quint8, const QString&, quint8, const QString&, const QString&)>(&OBUDevice::psamInit), 
                     this, &OBUEventConnector::onPSAMInit);
    QObject::connect(obuDevice.get(), &OBUDevice::psamResult, this, &OBUEventConnector::onPSAMResult);

    connected = true;
    writeLog(QString("OBU设备事件连接完成: %1").arg(deviceId));
}

void OBUEventConnector::disconnect() {
    if (!obuDevice || !connected) {
        return;
    }

    writeLog(QString("断开OBU设备事件: %1").arg(deviceId));

    // 断开所有信号连接
    QObject::disconnect(obuDevice.get(), nullptr, this, nullptr);

    connected = false;
    writeLog(QString("OBU设备事件断开完成: %1").arg(deviceId));
}

bool OBUEventConnector::startDevice() {
    if (!obuDevice) {
        return false;
    }

    writeLog(QString("启动OBU设备: %1").arg(deviceId));
    bool result = obuDevice->openDevice();
    
    if (result) {
        emit deviceStatusChanged(deviceId, "已启动");
    } else {
        emit errorOccurred(deviceId, "设备启动失败");
    }
    
    return result;
}

bool OBUEventConnector::stopDevice() {
    if (!obuDevice) {
        return false;
    }

    writeLog(QString("停止OBU设备: %1").arg(deviceId));
    bool result = obuDevice->closeDevice();
    
    if (result) {
        emit deviceStatusChanged(deviceId, "已停止");
    }
    
    return result;
}

QString OBUEventConnector::getDeviceStatus() const {
    if (!obuDevice) {
        return "设备对象为空";
    }
    
    return obuDevice->getStatusText();
}

// 事件处理槽函数实现
void OBUEventConnector::onRSUError(quint8 errorStatus) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<RSUErrorEventData>(errorStatus);
    dispatchEvent(EventType::RSUError, eventData);
    updateEventStatistics(EventType::RSUError);
}

void OBUEventConnector::onGetB0(quint8 rsctl, const TB0Info& b0Info) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<GetB0EventData>(rsctl, b0Info);
    dispatchEvent(EventType::GetB0, eventData);
    updateEventStatistics(EventType::GetB0);
}

void OBUEventConnector::onGetB1(quint8 rsctl, const TB1Info& b1Info) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<GetB1EventData>(rsctl, b1Info);
    dispatchEvent(EventType::GetB1, eventData);
    updateEventStatistics(EventType::GetB1);
}

void OBUEventConnector::onGetOBUID(quint8 rsctl, quint32 obuId, const TB2Info& b2Info) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<GetB2EventData>(rsctl, obuId, b2Info);
    dispatchEvent(EventType::GetOBUID, eventData);
    updateEventStatistics(EventType::GetOBUID);
}

void OBUEventConnector::onGetB4(quint8 rsctl, quint32 obuId, const TB4Info& b4Info) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<GetB4EventData>(rsctl, obuId, b4Info);
    dispatchEvent(EventType::GetB4, eventData);
    updateEventStatistics(EventType::GetB4);
}

void OBUEventConnector::onGetB5(quint8 rsctl, quint32 obuId, const TB5Info& b5Info) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<GetB5EventData>(rsctl, obuId, b5Info);
    dispatchEvent(EventType::GetB5, eventData);
    updateEventStatistics(EventType::GetB5);
}

void OBUEventConnector::onGetOBUVehicle(quint8 rsctl, quint32 obuId, const QString& plate, 
                                        quint8 plateCode, int vehicleClass, quint8 userType, 
                                        const TVClassConversion& conversion) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<GetOBUVehicleEventData>(rsctl, obuId, plate, plateCode, 
                                                              vehicleClass, userType, conversion);
    dispatchEvent(EventType::GetOBUVehicle, eventData);
    updateEventStatistics(EventType::GetOBUVehicle);
}

void OBUEventConnector::onGetCPUInfo(quint8 rsctl, quint32 obuId, quint32 remainMoney, 
                                     const TIC0012& ic0012, const TIC0015& ic0015) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<GetCPUInfoEventData>(rsctl, obuId, remainMoney, ic0012, ic0015);
    dispatchEvent(EventType::GetCPUInfo, eventData);
    updateEventStatistics(EventType::GetCPUInfo);
}

void OBUEventConnector::onGetCPUInfoNew(quint8 rsctl, quint32 obuId, quint8 obuType, quint8 antennaId,
                                        const TB4Content0and1& content0and1, const TIC0019& ic0019,
                                        const TB4Content1Station& station1, const TB4Content2& content2) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<GetCPUInfoNewEventData>(rsctl, obuId, obuType, antennaId,
                                                              content0and1, ic0019, station1, content2);
    dispatchEvent(EventType::GetCPUInfoNew, eventData);
    updateEventStatistics(EventType::GetCPUInfoNew);
}

void OBUEventConnector::onOBUSessionEnd(quint8 rsctl, quint32 obuId, const QDateTime& sessionTime,
                                        quint32 tac, quint16 payCardTranSN, quint32 psamTranSN,
                                        const QString& rsuTerminalId, quint8 keyVersion, quint8 transType) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<OBUSessionEndEventData>(rsctl, obuId, sessionTime, tac,
                                                              payCardTranSN, psamTranSN, rsuTerminalId,
                                                              keyVersion, transType);
    dispatchEvent(EventType::OBUSessionEnd, eventData);
    updateEventStatistics(EventType::OBUSessionEnd);
}

void OBUEventConnector::onOBUConsumed(quint8 rsctl, quint32 obuId, const QDateTime& purchaseTime,
                                      quint32 iccid, quint32 tac, quint32 psamTranSN, quint8 keyVersion) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<OBUConsumedEventData>(rsctl, obuId, purchaseTime, iccid,
                                                            tac, psamTranSN, keyVersion);
    dispatchEvent(EventType::OBUConsumed, eventData);
    updateEventStatistics(EventType::OBUConsumed);
}

void OBUEventConnector::onOBUB7(quint8 rsctl, quint32 obuId, quint8 recordNum, const QString& recordList) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<OBUB7EventData>(rsctl, obuId, recordNum, recordList);
    dispatchEvent(EventType::OBUB7, eventData);
    updateEventStatistics(EventType::OBUB7);
}

void OBUEventConnector::onOBUBA(quint8 rsctl, const TBAInfo& baInfo) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<GetBAEventData>(rsctl, baInfo);
    dispatchEvent(EventType::OBUBA, eventData);
    updateEventStatistics(EventType::OBUBA);
}

void OBUEventConnector::onOBUBB(quint8 rsctl, quint32 obuId, quint8 errorCode, 
                                const QVector<TAuthResult>& authResults) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<GetBBEventData>(rsctl, obuId, errorCode, authResults);
    dispatchEvent(EventType::OBUBB, eventData);
    updateEventStatistics(EventType::OBUBB);
}

void OBUEventConnector::onOBUD0(quint8 rsctl, quint32 obuId, quint8 errorCode, int x, int y) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<OBUD0EventData>(rsctl, obuId, errorCode, x, y);
    dispatchEvent(EventType::OBUD0, eventData);
    updateEventStatistics(EventType::OBUD0);
}

void OBUEventConnector::onOBUError(quint8 rsctl, quint32 obuId, quint8 state, quint8 errorCode) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<OBUErrorEventData>(rsctl, obuId, state, errorCode);
    dispatchEvent(EventType::OBUError, eventData);
    updateEventStatistics(EventType::OBUError);
    
    emit errorOccurred(deviceId, QString("OBU错误: 状态=%1, 错误码=%2").arg(state).arg(errorCode));
}

void OBUEventConnector::onRSUReset(quint8 rsctl, const QString& terminalId, quint8 algId, 
                                   quint16 manuId, const QString& rsuId, quint16 version) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<RSUResetEventData>(rsctl, terminalId, algId, manuId, rsuId, version);
    dispatchEvent(EventType::RSUReset, eventData);
    updateEventStatistics(EventType::RSUReset);
}

void OBUEventConnector::onRSUHeart() {
    if (!enabled) return;
    
    auto eventData = std::make_shared<EventData>(EventType::RSUHeart);
    dispatchEvent(EventType::RSUHeart, eventData);
    updateEventStatistics(EventType::RSUHeart);
}

void OBUEventConnector::onPurTimeOut(quint8 rsctl, quint32 obuId, quint8 state, quint8 errorCode,
                                     quint16 payCardTranSN, quint32 psamTranSN, const QString& rsuTerminalId,
                                     quint8 transType) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<PurTimeOutEventData>(rsctl, obuId, state, errorCode,
                                                           payCardTranSN, psamTranSN, rsuTerminalId, transType);
    dispatchEvent(EventType::PurTimeOut, eventData);
    updateEventStatistics(EventType::PurTimeOut);
}

void OBUEventConnector::onPSAMInit(quint8 rsctl, const QString& psamNo, quint8 psamVersion,
                                   const QString& areaCode, const QString& randCode) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<PSAMInitEventData>(rsctl, psamNo, psamVersion, areaCode, randCode);
    dispatchEvent(EventType::PSAMInit, eventData);
    updateEventStatistics(EventType::PSAMInit);
}

void OBUEventConnector::onPSAMResult(quint8 rsctl, quint16 sw1sw2) {
    if (!enabled) return;
    
    auto eventData = std::make_shared<PSAMResultEventData>(rsctl, sw1sw2);
    dispatchEvent(EventType::PSAMResult, eventData);
    updateEventStatistics(EventType::PSAMResult);
}

void OBUEventConnector::writeLog(const QString& message) {
    QString logMessage = QString("[%1] OBUConnector[%2]: %3")
                        .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                        .arg(deviceId)
                        .arg(message);
    qDebug() << logMessage;
}

void OBUEventConnector::dispatchEvent(EventType eventType, std::shared_ptr<EventData> eventData) {
    if (eventManager) {
        eventManager->dispatchEvent(eventData);
        emit eventProcessed(deviceId, QString::number(static_cast<int>(eventType)));
    }
}

void OBUEventConnector::updateEventStatistics(EventType eventType) {
    eventCount++;
    lastEventTime = QDateTime::currentDateTime();
    eventTypeCount[eventType]++;
}

// OBUDeviceManager 实现
OBUDeviceManager::OBUDeviceManager(QObject* parent)
    : QObject(parent)
    , totalEventCount(0)
{
    setupStatusUpdateTimer();
    writeLog("创建OBU设备管理器");
}

OBUDeviceManager::~OBUDeviceManager() {
    stopAllDevices();
    
    // 释放所有连接器的内存
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        delete it.value();
    }
    connectors.clear();
    
    writeLog("销毁OBU设备管理器");
}

QString OBUDeviceManager::addDevice(std::shared_ptr<OBUDevice> obuDevice, const QString& deviceId) {
    QMutexLocker locker(&deviceMutex);
    
    if (connectors.contains(deviceId)) {
        return QString("设备ID '%1' 已存在").arg(deviceId);
    }
    
    if (!obuDevice) {
        return QString("OBU设备对象为空");
    }
    
    auto connector = new OBUEventConnector(obuDevice, deviceId, this);
    
    // 连接信号
    QObject::connect(connector, &OBUEventConnector::deviceStatusChanged,
                     this, &OBUDeviceManager::onDeviceStatusChanged);
    QObject::connect(connector, &OBUEventConnector::eventProcessed,
                     this, &OBUDeviceManager::onDeviceEventProcessed);
    QObject::connect(connector, &OBUEventConnector::errorOccurred,
                     this, &OBUDeviceManager::onDeviceError);
    
    connector->connect();
    
    // 添加设备连接器到映射表
    if (connectors.contains(deviceId)) {
        delete connectors[deviceId];  // 删除旧的连接器
        connectors.remove(deviceId);
    }
    connectors[deviceId] = connector;
    
    writeLog(QString("添加设备: %1").arg(deviceId));
    emit deviceAdded(deviceId);
    
    return QString();  // 成功返回空字符串
}

bool OBUDeviceManager::removeDevice(const QString& deviceId) {
    QMutexLocker locker(&deviceMutex);
    
    if (!connectors.contains(deviceId)) {
        return false;
    }
    
    // 使用迭代器获取指针并清理
    auto it = connectors.find(deviceId);
    if (it != connectors.end()) {
        auto connector = it.value();
        if (connector) {
            connector->stopDevice();
            connector->disconnect();
            delete connector;  // 释放内存
        }
    }
    
    connectors.remove(deviceId);
    
    writeLog(QString("移除设备: %1").arg(deviceId));
    emit deviceRemoved(deviceId);
    
    return true;
}

OBUEventConnector* OBUDeviceManager::getDevice(const QString& deviceId) {
    QMutexLocker locker(&deviceMutex);
    
    auto it = connectors.find(deviceId);
    return (it != connectors.end()) ? it.value() : nullptr;
}

int OBUDeviceManager::getDeviceCount() const {
    QMutexLocker locker(&deviceMutex);
    return connectors.size();
}

QStringList OBUDeviceManager::getDeviceList() const {
    QMutexLocker locker(&deviceMutex);
    return connectors.keys();
}

void OBUDeviceManager::startAllDevices() {
    QMutexLocker locker(&deviceMutex);
    
    writeLog("启动所有设备");
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        if (it.value()) it.value()->startDevice();
    }
    
    emit allDevicesStarted();
}

void OBUDeviceManager::stopAllDevices() {
    QMutexLocker locker(&deviceMutex);
    
    writeLog("停止所有设备");
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        if (it.value()) it.value()->stopDevice();
    }
    
    emit allDevicesStopped();
}

void OBUDeviceManager::connectAllDevices() {
    QMutexLocker locker(&deviceMutex);
    
    writeLog("连接所有设备事件");
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        if (it.value()) it.value()->connect();
    }
}

void OBUDeviceManager::disconnectAllDevices() {
    QMutexLocker locker(&deviceMutex);
    
    writeLog("断开所有设备事件");
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        if (it.value()) it.value()->disconnect();
    }
}

void OBUDeviceManager::enableAllDevices(bool enabled) {
    QMutexLocker locker(&deviceMutex);
    
    writeLog(QString("%1所有设备").arg(enabled ? "启用" : "禁用"));
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        if (it.value()) it.value()->setEnabled(enabled);
    }
}

bool OBUDeviceManager::loadConfiguration(const QString& configFile) {
    this->configFile = configFile;
    
    QFile file(configFile);
    if (!file.open(QIODevice::ReadOnly)) {
        writeLog(QString("无法打开配置文件: %1").arg(configFile));
        return false;
    }
    
    QByteArray data = file.readAll();
    QJsonDocument doc = QJsonDocument::fromJson(data);
    
    if (!doc.isObject()) {
        writeLog("配置文件格式错误");
        return false;
    }
    
    QJsonObject root = doc.object();
    QJsonArray devices = root["devices"].toArray();
    
    int loadedCount = 0;
    for (const QJsonValue& deviceValue : devices) {
        if (!deviceValue.isObject()) {
            continue;
        }
        
        QJsonObject deviceObj = deviceValue.toObject();
        QString deviceId = deviceObj["deviceId"].toString();
        
        if (deviceId.isEmpty()) {
            continue;
        }
        
        try {
            OBUDeviceConfig config = deviceConfigFromJson(deviceObj);
            auto obuDevice = OBUDeviceFactory::createDevice(config);
            
            QString error = addDevice(obuDevice, deviceId);
            if (error.isEmpty()) {
                loadedCount++;
            } else {
                writeLog(QString("加载设备失败 %1: %2").arg(deviceId).arg(error));
            }
        } catch (const std::exception& e) {
            writeLog(QString("解析设备配置失败 %1: %2").arg(deviceId).arg(e.what()));
        }
    }
    
    writeLog(QString("配置加载完成，成功加载 %1 个设备").arg(loadedCount));
    return loadedCount > 0;
}

bool OBUDeviceManager::saveConfiguration(const QString& configFile) {
    this->configFile = configFile;
    
    QJsonObject root;
    QJsonArray devices;
    
    QMutexLocker locker(&deviceMutex);
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        const QString& deviceId = it.key();
        const auto connector = it.value();
        
        if (connector && connector->getOBUDevice()) {
            QJsonObject deviceObj = deviceConfigToJson(connector->getOBUDevice()->getConfig());
            deviceObj["deviceId"] = deviceId;
            devices.append(deviceObj);
        }
    }
    locker.unlock();
    
    root["devices"] = devices;
    root["version"] = "1.0";
    root["saveTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    QJsonDocument doc(root);
    
    QFile file(configFile);
    if (!file.open(QIODevice::WriteOnly)) {
        writeLog(QString("无法创建配置文件: %1").arg(configFile));
        return false;
    }
    
    qint64 bytesWritten = file.write(doc.toJson());
    if (bytesWritten <= 0) {
        writeLog("配置文件写入失败");
        return false;
    }
    
    writeLog(QString("配置保存完成: %1").arg(configFile));
    return true;
}

void OBUDeviceManager::generateStatusReport(const QString& reportFile) {
    QJsonObject report = getStatusSummary();
    
    QJsonDocument doc(report);
    
    QFile file(reportFile);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        writeLog(QString("状态报告已生成: %1").arg(reportFile));
    } else {
        writeLog(QString("无法创建状态报告文件: %1").arg(reportFile));
    }
}

QJsonObject OBUDeviceManager::getStatusSummary() const {
    QMutexLocker locker(&deviceMutex);
    
    QJsonObject summary;
    summary["totalDevices"] = connectors.size();
    summary["activeDevices"] = getActiveDeviceCount();
    summary["connectedDevices"] = getConnectedDeviceCount();
    summary["totalEvents"] = getTotalEventCount();
    summary["reportTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    QJsonArray deviceList;
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        const QString& deviceId = it.key();
        const auto& connector = it.value();
        
        QJsonObject deviceInfo;
        deviceInfo["deviceId"] = deviceId;
        deviceInfo["status"] = connector->getDeviceStatus();
        deviceInfo["enabled"] = connector->isEnabled();
        deviceInfo["connected"] = connector->isConnected();
        deviceInfo["eventCount"] = connector->getEventCount();
        deviceInfo["lastEventTime"] = connector->getLastEventTime().toString(Qt::ISODate);
        
        deviceList.append(deviceInfo);
    }
    
    summary["devices"] = deviceList;
    return summary;
}

int OBUDeviceManager::getActiveDeviceCount() const {
    QMutexLocker locker(&deviceMutex);
    
    int count = 0;
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        const auto connector = it.value();
        if (connector && connector->isEnabled() && connector->getOBUDevice() && 
            connector->getOBUDevice()->getStatus() == OBUDeviceStatus::Connected) {
            count++;
        }
    }
    return count;
}

int OBUDeviceManager::getConnectedDeviceCount() const {
    QMutexLocker locker(&deviceMutex);
    
    int count = 0;
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        const auto connector = it.value();
        if (connector && connector->isConnected()) {
            count++;
        }
    }
    return count;
}

QMap<QString, int> OBUDeviceManager::getDeviceEventCounts() const {
    QMutexLocker locker(&deviceMutex);
    
    QMap<QString, int> counts;
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        const auto connector = it.value();
        counts[it.key()] = connector ? connector->getEventCount() : 0;
    }
    return counts;
}

int OBUDeviceManager::getTotalEventCount() const {
    QMutexLocker locker(&deviceMutex);
    
    int total = 0;
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        const auto connector = it.value();
        if (connector) total += connector->getEventCount();
    }
    return total;
}

void OBUDeviceManager::resetAllEventCounts() {
    QMutexLocker locker(&deviceMutex);
    
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        if (it.value()) it.value()->resetEventCount();
    }
    totalEventCount = 0;
}

// 私有槽函数实现
void OBUDeviceManager::onDeviceStatusChanged(const QString& deviceId, const QString& status) {
    lastStatusUpdate[deviceId] = QDateTime::currentDateTime();
    emit deviceStatusChanged(deviceId, status);
}

void OBUDeviceManager::onDeviceEventProcessed(const QString& deviceId, const QString& eventType) {
    Q_UNUSED(eventType)
    Q_UNUSED(deviceId)
    totalEventCount++;
}

void OBUDeviceManager::onDeviceError(const QString& deviceId, const QString& error) {
    deviceErrorCounts[deviceId]++;
    writeLog(QString("设备错误 %1: %2").arg(deviceId).arg(error));
}

void OBUDeviceManager::onStatusUpdateTimer() {
    updateDeviceStatistics();
}

void OBUDeviceManager::setupStatusUpdateTimer() {
    statusUpdateTimer = new QTimer(this);
    statusUpdateTimer->setInterval(STATUS_UPDATE_INTERVAL);
    connect(statusUpdateTimer, &QTimer::timeout, this, &OBUDeviceManager::onStatusUpdateTimer);
    statusUpdateTimer->start();
}

void OBUDeviceManager::updateDeviceStatistics() {
    // 更新设备统计信息
    QDateTime now = QDateTime::currentDateTime();
    
    QMutexLocker locker(&deviceMutex);
    for (auto it = connectors.begin(); it != connectors.end(); ++it) {
        const QString& deviceId = it.key();
        
        // 检查设备是否长时间无响应
        if (lastStatusUpdate.contains(deviceId)) {
            qint64 timeSinceLastUpdate = lastStatusUpdate[deviceId].secsTo(now);
            if (timeSinceLastUpdate > 300) {  // 5分钟无响应
                writeLog(QString("设备 %1 长时间无响应: %2 秒").arg(deviceId).arg(timeSinceLastUpdate));
            }
        }
    }
}

QJsonObject OBUDeviceManager::deviceConfigToJson(const OBUDeviceConfig& config) const {
    QJsonObject obj;
    
    obj["deviceType"] = static_cast<int>(config.deviceType);
    obj["serialPortName"] = config.serialPortName;
    obj["baudRate"] = config.baudRate;
    obj["dataBits"] = config.dataBits;
    obj["stopBits"] = config.stopBits;
    obj["parity"] = config.parity;
    obj["serverIP"] = config.serverIP;
    obj["serverPort"] = config.serverPort;
    obj["timeoutMs"] = config.timeoutMs;
    obj["rsctl"] = static_cast<int>(config.rsctl);
    obj["terminalId"] = config.terminalId;
    obj["autoReconnect"] = config.autoReconnect;
    obj["reconnectInterval"] = config.reconnectInterval;
    
    return obj;
}

OBUDeviceConfig OBUDeviceManager::deviceConfigFromJson(const QJsonObject& json) const {
    OBUDeviceConfig config;
    
    config.deviceId = json["deviceId"].toString();
    config.deviceType = static_cast<OBUDeviceType>(json["deviceType"].toInt());
    config.serialPortName = json["serialPortName"].toString();
    config.baudRate = json["baudRate"].toInt(115200);
    config.dataBits = json["dataBits"].toInt(8);
    config.stopBits = json["stopBits"].toInt(1);
    config.parity = json["parity"].toString("None");
    config.serverIP = json["serverIP"].toString();
    config.serverPort = json["serverPort"].toInt(8000);
    config.timeoutMs = json["timeoutMs"].toInt(5000);
    config.rsctl = static_cast<quint8>(json["rsctl"].toInt(0));
    config.terminalId = json["terminalId"].toString();
    config.autoReconnect = json["autoReconnect"].toBool(true);
    config.reconnectInterval = json["reconnectInterval"].toInt(5000);
    
    return config;
}

void OBUDeviceManager::writeLog(const QString& message) {
    QString logMessage = QString("[%1] OBUDeviceManager: %2")
                        .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                        .arg(message);
    qDebug() << logMessage;
}

// OBUDeviceConfigBuilder 实现
OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setDeviceId(const QString& deviceId) {
    config.deviceId = deviceId;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setDeviceType(OBUDeviceType type) {
    config.deviceType = type;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setAutoReconnect(bool autoReconnect) {
    config.autoReconnect = autoReconnect;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setReconnectInterval(int intervalMs) {
    config.reconnectInterval = intervalMs;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setSerialPort(const QString& portName) {
    config.serialPortName = portName;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setBaudRate(int baudRate) {
    config.baudRate = baudRate;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setDataBits(int dataBits) {
    config.dataBits = dataBits;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setStopBits(int stopBits) {
    config.stopBits = stopBits;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setParity(const QString& parity) {
    config.parity = parity;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setServerIP(const QString& ip) {
    config.serverIP = ip;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setServerPort(int port) {
    config.serverPort = port;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setTimeout(int timeoutMs) {
    config.timeoutMs = timeoutMs;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setRSCTL(quint8 rsctl) {
    config.rsctl = rsctl;
    return *this;
}

OBUDeviceConfigBuilder& OBUDeviceConfigBuilder::setTerminalId(const QString& terminalId) {
    config.terminalId = terminalId;
    return *this;
}

OBUDeviceConfig OBUDeviceConfigBuilder::build() const {
    return config;
}

OBUDeviceConfigBuilder OBUDeviceConfigBuilder::forSerialDevice(const QString& deviceId, const QString& portName) {
    return OBUDeviceConfigBuilder()
           .setDeviceId(deviceId)
           .setDeviceType(OBUDeviceType::SerialPort)
           .setSerialPort(portName);
}

OBUDeviceConfigBuilder OBUDeviceConfigBuilder::forTCPDevice(const QString& deviceId, 
                                                            const QString& serverIP, int port) {
    return OBUDeviceConfigBuilder()
           .setDeviceId(deviceId)
           .setDeviceType(OBUDeviceType::TCPClient)
           .setServerIP(serverIP)
           .setServerPort(port);
}

OBUDeviceConfigBuilder OBUDeviceConfigBuilder::forUDPDevice(const QString& deviceId, 
                                                            const QString& serverIP, int port) {
    return OBUDeviceConfigBuilder()
           .setDeviceId(deviceId)
           .setDeviceType(OBUDeviceType::UDPClient)
           .setServerIP(serverIP)
           .setServerPort(port);
}

// OBUDeviceMonitor 实现
OBUDeviceMonitor::OBUDeviceMonitor(OBUDeviceManager* deviceManager, QObject* parent)
    : QObject(parent)
    , deviceManager(deviceManager)
    , monitoring(false)
    , errorThreshold(10)
    , responseTimeThreshold(5000)
    , monitorTimer(new QTimer(this))
{
    // 连接定时器
    connect(monitorTimer, &QTimer::timeout, this, &OBUDeviceMonitor::onMonitorTimer);
    
    writeLog("OBU设备监控器已创建");
}

OBUDeviceMonitor::~OBUDeviceMonitor() {
    stopMonitoring();
    writeLog("OBU设备监控器已销毁");
}

void OBUDeviceMonitor::startMonitoring(int intervalMs) {
    if (monitoring) {
        writeLog("监控已经在运行中");
        return;
    }
    
    monitoring = true;
    monitorTimer->setInterval(intervalMs);
    monitorTimer->start();
    
    writeLog(QString("开始设备监控，间隔: %1ms").arg(intervalMs));
}

void OBUDeviceMonitor::stopMonitoring() {
    if (!monitoring) {
        return;
    }
    
    monitoring = false;
    monitorTimer->stop();
    
    writeLog("停止设备监控");
}

QString OBUDeviceMonitor::generateTextReport() const {
    QStringList report;
    report << "=== OBU设备监控报告 ===";
    report << QString("生成时间: %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    report << QString("监控状态: %1").arg(monitoring ? "运行中" : "已停止");
    report << QString("错误阈值: %1").arg(errorThreshold);
    report << QString("响应时间阈值: %1ms").arg(responseTimeThreshold);
    report << "";
    
    if (deviceManager) {
        report << QString("设备总数: %1").arg(deviceManager->getDeviceCount());
        report << QString("活跃设备数: %1").arg(deviceManager->getActiveDeviceCount());
        report << QString("已连接设备数: %1").arg(deviceManager->getConnectedDeviceCount());
        report << QString("总事件数: %1").arg(deviceManager->getTotalEventCount());
        report << "";
        
        // 设备详细信息
        QStringList deviceList = deviceManager->getDeviceList();
        for (const QString& deviceId : deviceList) {
            OBUEventConnector* connector = deviceManager->getDevice(deviceId);
            if (connector) {
                report << QString("设备 %1:").arg(deviceId);
                report << QString("  状态: %1").arg(connector->getDeviceStatus());
                report << QString("  启用: %1").arg(connector->isEnabled() ? "是" : "否");
                report << QString("  已连接: %1").arg(connector->isConnected() ? "是" : "否");
                report << QString("  事件数: %1").arg(connector->getEventCount());
                report << QString("  最后事件时间: %1").arg(connector->getLastEventTime().toString("yyyy-MM-dd hh:mm:ss"));
                report << "";
            }
        }
    }
    
    return report.join("\n");
}

QJsonObject OBUDeviceMonitor::generateJsonReport() const {
    QJsonObject report;
    
    report["reportTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    report["monitoring"] = monitoring;
    report["errorThreshold"] = errorThreshold;
    report["responseTimeThreshold"] = responseTimeThreshold;
    
    if (deviceManager) {
        report["deviceCount"] = deviceManager->getDeviceCount();
        report["activeDeviceCount"] = deviceManager->getActiveDeviceCount();
        report["connectedDeviceCount"] = deviceManager->getConnectedDeviceCount();
        report["totalEventCount"] = deviceManager->getTotalEventCount();
        
        QJsonArray devices;
        QStringList deviceList = deviceManager->getDeviceList();
        for (const QString& deviceId : deviceList) {
            OBUEventConnector* connector = deviceManager->getDevice(deviceId);
            if (connector) {
                QJsonObject deviceInfo;
                deviceInfo["deviceId"] = deviceId;
                deviceInfo["status"] = connector->getDeviceStatus();
                deviceInfo["enabled"] = connector->isEnabled();
                deviceInfo["connected"] = connector->isConnected();
                deviceInfo["eventCount"] = connector->getEventCount();
                deviceInfo["lastEventTime"] = connector->getLastEventTime().toString(Qt::ISODate);
                devices.append(deviceInfo);
            }
        }
        report["devices"] = devices;
    }
    
    return report;
}

void OBUDeviceMonitor::saveReportToFile(const QString& fileName, const QString& format) const {
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        writeLog(QString("无法创建报告文件: %1").arg(fileName));
        return;
    }
    
    QTextStream out(&file);
    out.setCodec("UTF-8");
    
    if (format.toLower() == "json") {
        QJsonObject report = generateJsonReport();
        QJsonDocument doc(report);
        out << doc.toJson();
    } else {
        out << generateTextReport();
    }
    
    file.close();
    writeLog(QString("监控报告已保存: %1").arg(fileName));
}

void OBUDeviceMonitor::onMonitorTimer() {
    if (!monitoring || !deviceManager) {
        return;
    }
    
    checkDeviceHealth();
    updatePerformanceMetrics();
}

void OBUDeviceMonitor::checkDeviceHealth() {
    QStringList deviceList = deviceManager->getDeviceList();
    bool systemHealthy = true;
    
    for (const QString& deviceId : deviceList) {
        OBUEventConnector* connector = deviceManager->getDevice(deviceId);
        if (!connector) {
            continue;
        }
        
        bool deviceHealthy = true;
        QString alertMessage;
        
        // 检查设备连接状态
        if (!connector->isConnected()) {
            deviceHealthy = false;
            alertMessage = "设备连接丢失";
        }
        
        // 检查设备是否启用
        if (!connector->isEnabled()) {
            deviceHealthy = false;
            alertMessage = "设备已禁用";
        }
        
        // 检查最后事件时间
        QDateTime lastEventTime = connector->getLastEventTime();
        if (lastEventTime.isValid()) {
            qint64 timeSinceLastEvent = lastEventTime.secsTo(QDateTime::currentDateTime());
            if (timeSinceLastEvent > responseTimeThreshold / 1000) {  // 转换为秒
                deviceHealthy = false;
                alertMessage = QString("设备响应超时: %1秒").arg(timeSinceLastEvent);
            }
        }
        
        // 发出设备健康状态信号
        emit deviceHealthChanged(deviceId, deviceHealthy);
        
        if (!deviceHealthy) {
            systemHealthy = false;
            emit alertTriggered(deviceId, alertMessage);
            writeLog(QString("设备健康检查警告 - %1: %2").arg(deviceId).arg(alertMessage));
        }
        
        // 分析设备性能
        analyzeDevicePerformance(deviceId);
    }
    
    // 发出系统健康状态信号
    emit systemHealthChanged(systemHealthy);
}

void OBUDeviceMonitor::updatePerformanceMetrics() {
    if (!deviceManager) {
        return;
    }
    
    // 更新性能指标
    int totalDevices = deviceManager->getDeviceCount();
    int activeDevices = deviceManager->getActiveDeviceCount();
    int connectedDevices = deviceManager->getConnectedDeviceCount();
    int totalEvents = deviceManager->getTotalEventCount();
    
    writeLog(QString("性能指标更新 - 总设备: %1, 活跃: %2, 已连接: %3, 总事件: %4")
             .arg(totalDevices).arg(activeDevices).arg(connectedDevices).arg(totalEvents));
    
    checkSystemThresholds();
}

void OBUDeviceMonitor::analyzeDevicePerformance(const QString& deviceId) {
    OBUEventConnector* connector = deviceManager->getDevice(deviceId);
    if (!connector) {
        return;
    }
    
    // 分析设备性能指标
    int eventCount = connector->getEventCount();
    QDateTime lastEventTime = connector->getLastEventTime();
    
    // 如果事件数量过低，可能表示设备性能问题
    if (eventCount < 10 && lastEventTime.isValid() && 
        lastEventTime.secsTo(QDateTime::currentDateTime()) > 3600) {  // 1小时内事件数过低
        writeLog(QString("设备性能警告 - %1: 事件数量过低 (%2)").arg(deviceId).arg(eventCount));
    }
}

void OBUDeviceMonitor::checkSystemThresholds() {
    if (!deviceManager) {
        return;
    }
    
    int totalDevices = deviceManager->getDeviceCount();
    int activeDevices = deviceManager->getActiveDeviceCount();
    
    if (totalDevices > 0) {
        double activeRatio = static_cast<double>(activeDevices) / totalDevices;
        if (activeRatio < 0.5) {  // 活跃设备比例低于50%
            writeLog(QString("系统性能警告: 活跃设备比例过低 (%1%)").arg(activeRatio * 100, 0, 'f', 1));
        }
    }
}

void OBUDeviceMonitor::writeLog(const QString& message) const {
    QString logMessage = QString("[%1] OBUDeviceMonitor: %2")
                        .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                        .arg(message);
    qDebug() << logMessage;
}

} // namespace ETC

