/**
 * @file SecurityModule.cpp
 * @brief ETC系统安全验证模块实现
 */

#include "security/SecurityModule.h"
#include <QDebug>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QJsonArray>
#include <QJsonParseError>
#include <QUrlQuery>
#include <QFile>
#include <QSettings>
#include <QCryptographicHash>
#include <QEventLoop>
#include <QMutexLocker>
#include <QThread>
#include <QRegularExpression>
#include <QSslConfiguration>
#include <QRandomGenerator>

namespace ETC {

// PSAMSignData 实现
QJsonObject PSAMSignData::toJson() const {
    QJsonObject json;
    json["psamNo"] = psamNo;
    json["terminalNo"] = terminalNo;
    json["provinceCode"] = provinceCode;
    json["roadCode"] = roadCode;
    json["roadName"] = roadName;
    json["stationCode"] = stationCode;
    json["stationName"] = stationName;
    json["stationType"] = stationType;
    json["laneNo"] = laneNo;
    json["laneType"] = laneType;
    json["terminalTime"] = terminalTime;
    return json;
}

PSAMSignData PSAMSignData::fromJson(const QJsonObject& json) {
    PSAMSignData data;
    data.psamNo = json["psamNo"].toString();
    data.terminalNo = json["terminalNo"].toString();
    data.provinceCode = json["provinceCode"].toString();
    data.roadCode = json["roadCode"].toString();
    data.roadName = json["roadName"].toString();
    data.stationCode = json["stationCode"].toString();
    data.stationName = json["stationName"].toString();
    data.stationType = json["stationType"].toString();
    data.laneNo = json["laneNo"].toString();
    data.laneType = json["laneType"].toString();
    data.terminalTime = json["terminalTime"].toString();
    return data;
}

bool PSAMSignData::isValid() const {
    return !psamNo.isEmpty() && !terminalNo.isEmpty() && 
           !provinceCode.isEmpty() && !stationCode.isEmpty();
}

QString PSAMSignData::toString() const {
    return QString("PSAMSign[PSAM:%1, Terminal:%2, Province:%3, Station:%4]")
           .arg(psamNo).arg(terminalNo).arg(provinceCode).arg(stationCode);
}

// PSAMAuthData 实现
QJsonObject PSAMAuthData::toJson() const {
    QJsonObject json;
    json["psamNo"] = psamNo;
    json["random"] = random;
    json["provinceCode"] = provinceCode;
    json["roadCode"] = roadCode;
    json["roadName"] = roadName;
    json["stationCode"] = stationCode;
    json["stationName"] = stationName;
    json["stationType"] = stationType;
    json["laneNo"] = laneNo;
    json["laneType"] = laneType;
    json["terminalTime"] = terminalTime;
    return json;
}

PSAMAuthData PSAMAuthData::fromJson(const QJsonObject& json) {
    PSAMAuthData data;
    data.psamNo = json["psamNo"].toString();
    data.random = json["random"].toString();
    data.provinceCode = json["provinceCode"].toString();
    data.roadCode = json["roadCode"].toString();
    data.roadName = json["roadName"].toString();
    data.stationCode = json["stationCode"].toString();
    data.stationName = json["stationName"].toString();
    data.stationType = json["stationType"].toString();
    data.laneNo = json["laneNo"].toString();
    data.laneType = json["laneType"].toString();
    data.terminalTime = json["terminalTime"].toString();
    return data;
}

bool PSAMAuthData::isValid() const {
    return !psamNo.isEmpty() && !random.isEmpty() && 
           !provinceCode.isEmpty() && !stationCode.isEmpty();
}

QString PSAMAuthData::toString() const {
    return QString("PSAMAuth[PSAM:%1, Random:%2, Province:%3, Station:%4]")
           .arg(psamNo).arg(random).arg(provinceCode).arg(stationCode);
}

// PSAMResultData 实现
QJsonObject PSAMResultData::toJson() const {
    QJsonObject json;
    json["psamNo"] = psamNo;
    json["terminalNo"] = terminalNo;
    json["resultCode"] = resultCode;
    json["resultMessage"] = resultMessage;
    json["signTime"] = signTime;
    json["authTime"] = authTime;
    json["resultTime"] = resultTime;
    return json;
}

PSAMResultData PSAMResultData::fromJson(const QJsonObject& json) {
    PSAMResultData data;
    data.psamNo = json["psamNo"].toString();
    data.terminalNo = json["terminalNo"].toString();
    data.resultCode = json["resultCode"].toString();
    data.resultMessage = json["resultMessage"].toString();
    data.signTime = json["signTime"].toString();
    data.authTime = json["authTime"].toString();
    data.resultTime = json["resultTime"].toString();
    return data;
}

bool PSAMResultData::isValid() const {
    return !psamNo.isEmpty() && !terminalNo.isEmpty() && !resultCode.isEmpty();
}

QString PSAMResultData::toString() const {
    return QString("PSAMResult[PSAM:%1, Terminal:%2, Code:%3, Message:%4]")
           .arg(psamNo).arg(terminalNo).arg(resultCode).arg(resultMessage);
}

// PSAMSignResultResponse 实现
void PSAMSignResultResponse::clear() {
    code.clear();
    message.clear();
    responseTime = QDateTime::currentDateTime();
}

bool PSAMSignResultResponse::isSuccess() const {
    return code == "0000" || code == "200";
}

QString PSAMSignResultResponse::toString() const {
    return QString("PSAMSignResult[Code:%1, Message:%2, Time:%3]")
           .arg(code).arg(message).arg(responseTime.toString());
}

// PSAMAuthResponse 实现
void PSAMAuthResponse::clear() {
    code.clear();
    message.clear();
    listNo.clear();
    authInstruction.clear();
    responseTime = QDateTime::currentDateTime();
}

bool PSAMAuthResponse::isSuccess() const {
    return code == "0000" || code == "200";
}

QString PSAMAuthResponse::toString() const {
    return QString("PSAMAuthResp[Code:%1, Message:%2, ListNo:%3, Time:%4]")
           .arg(code).arg(message).arg(listNo).arg(responseTime.toString());
}

// SecurityVerifier 实现
SecurityVerifier::SecurityVerifier(QObject* parent)
    : QObject(parent)
    , networkManager(new QNetworkAccessManager(this))
    , currentReply(nullptr)
    , retryTimer(new QTimer(this))
    , busy(false)
    , lastError(SecurityError::NoError)
    , successCount(0)
    , failureCount(0)
    , currentRetryCount(0)
{
    // 配置网络管理器
    connect(networkManager,
            static_cast<void(QNetworkAccessManager::*)(QNetworkReply*, const QList<QSslError>&)>(&QNetworkAccessManager::sslErrors),
            this,
            [this](QNetworkReply* reply, const QList<QSslError>& errors) {
                this->onSslErrors(reply, errors);
            });
    
    // 配置重试定时器
    retryTimer->setSingleShot(true);
    connect(retryTimer, &QTimer::timeout, this, &SecurityVerifier::onRetryTimer);
    
    // 注册元类型
    qRegisterMetaType<SecurityError>("SecurityError");
    qRegisterMetaType<PSAMSignResultResponse>("PSAMSignResultResponse");
    qRegisterMetaType<PSAMAuthResponse>("PSAMAuthResponse");
    
    writeLog("创建安全验证器");
}

SecurityVerifier::SecurityVerifier(const SecurityConfig& config, QObject* parent)
    : SecurityVerifier(parent)
{
    setConfig(config);
}

SecurityVerifier::~SecurityVerifier() {
    if (currentReply) {
        currentReply->abort();
    }
    writeLog("销毁安全验证器");
}

void SecurityVerifier::setConfig(const SecurityConfig& newConfig) {
    QMutexLocker locker(&configMutex);
    config = newConfig;
    
    // 设置网络超时
    networkManager->setNetworkAccessible(QNetworkAccessManager::Accessible);
}

SecurityConfig SecurityVerifier::getConfig() const {
    QMutexLocker locker(&configMutex);
    return config;
}

void SecurityVerifier::setUrls(const URLParams& params) {
    QMutexLocker locker(&configMutex);
    config.urls = params;
}

URLParams SecurityVerifier::getUrls() const {
    QMutexLocker locker(&configMutex);
    return config.urls;
}

int SecurityVerifier::sendPSAMSign(const PSAMSignData& signData, PSAMSignResultResponse& response) {
    if (busy) {
        handleError(SecurityError::NetworkError, "验证器正忙，请稍后重试");
        return -1;
    }
    
    if (!signData.isValid()) {
        handleError(SecurityError::InvalidData, "PSAM签到数据无效");
        return -2;
    }
    
    writeLog(QString("发送PSAM签到请求: %1").arg(signData.toString()));
    
    try {
        busy = true;
        emit requestStarted("PSAMSign");
        
        QString url = formatUrl(config.urls.ip + ":" + config.urls.port, config.urls.psamSign);
        QJsonObject data = signData.toJson();
        
        QString responseStr = sendRequestWithRetry(url, data);
        processSignResponse(responseStr, response);
        
        if (response.isSuccess()) {
            successCount++;
            writeLog("PSAM签到成功");
            emit requestFinished("PSAMSign", true);
            busy = false;  // 在return前重置busy状态
            return 0;
        } else {
            failureCount++;
            handleError(SecurityError::ServerError, QString("签到失败: %1").arg(response.message));
            emit requestFinished("PSAMSign", false);
            busy = false;  // 在return前重置busy状态
            return -3;
        }
        
    } catch (const std::exception& e) {
        failureCount++;
        handleError(SecurityError::NetworkError, QString("签到异常: %1").arg(e.what()));
        emit requestFinished("PSAMSign", false);
        busy = false;  // 在return前重置busy状态
        return -4;
    }
}

int SecurityVerifier::sendPSAMAuth(const PSAMAuthData& authData, PSAMAuthResponse& response) {
    if (busy) {
        handleError(SecurityError::NetworkError, "验证器正忙，请稍后重试");
        return -1;
    }
    
    if (!authData.isValid()) {
        handleError(SecurityError::InvalidData, "PSAM认证数据无效");
        return -2;
    }
    
    writeLog(QString("发送PSAM认证请求: %1").arg(authData.toString()));
    
    try {
        busy = true;
        emit requestStarted("PSAMAuth");
        
        QString url = formatUrl(config.urls.ip + ":" + config.urls.port, config.urls.psamAuth);
        QJsonObject data = authData.toJson();
        
        QString responseStr = sendRequestWithRetry(url, data);
        processAuthResponse(responseStr, response);
        
        if (response.isSuccess()) {
            successCount++;
            writeLog("PSAM认证成功");
            emit requestFinished("PSAMAuth", true);
            busy = false;  // 在return前重置busy状态
            return 0;
        } else {
            failureCount++;
            handleError(SecurityError::AuthenticationFailed, QString("认证失败: %1").arg(response.message));
            emit requestFinished("PSAMAuth", false);
            busy = false;  // 在return前重置busy状态
            return -3;
        }
        
    } catch (const std::exception& e) {
        failureCount++;
        handleError(SecurityError::NetworkError, QString("认证异常: %1").arg(e.what()));
        emit requestFinished("PSAMAuth", false);
        busy = false;  // 在return前重置busy状态
        return -4;
    }
}

int SecurityVerifier::sendPSAMResult(const PSAMResultData& resultData, PSAMSignResultResponse& response) {
    if (busy) {
        handleError(SecurityError::NetworkError, "验证器正忙，请稍后重试");
        return -1;
    }
    
    if (!resultData.isValid()) {
        handleError(SecurityError::InvalidData, "PSAM结果数据无效");
        return -2;
    }
    
    writeLog(QString("发送PSAM结果请求: %1").arg(resultData.toString()));
    
    try {
        busy = true;
        emit requestStarted("PSAMResult");
        
        QString url = formatUrl(config.urls.ip + ":" + config.urls.port, config.urls.psamResult);
        QJsonObject data = resultData.toJson();
        
        QString responseStr = sendRequestWithRetry(url, data);
        processResultResponse(responseStr, response);
        
        if (response.isSuccess()) {
            successCount++;
            writeLog("PSAM结果提交成功");
            emit requestFinished("PSAMResult", true);
            busy = false;  // 在return前重置busy状态
            return 0;
        } else {
            failureCount++;
            handleError(SecurityError::ServerError, QString("结果提交失败: %1").arg(response.message));
            emit requestFinished("PSAMResult", false);
            busy = false;  // 在return前重置busy状态
            return -3;
        }
        
    } catch (const std::exception& e) {
        failureCount++;
        handleError(SecurityError::NetworkError, QString("结果提交异常: %1").arg(e.what()));
        emit requestFinished("PSAMResult", false);
        busy = false;  // 在return前重置busy状态
        return -4;
    }
}

void SecurityVerifier::sendPSAMSignAsync(const PSAMSignData& signData) {
    // 异步实现 - 在后台线程中执行
    QThread* workerThread = QThread::create([this, signData]() {
        PSAMSignResultResponse response;
        sendPSAMSign(signData, response);
        emit psamSignCompleted(response);
    });
    
    workerThread->start();
    connect(workerThread, &QThread::finished, workerThread, &QThread::deleteLater);
}

void SecurityVerifier::sendPSAMAuthAsync(const PSAMAuthData& authData) {
    // 异步实现 - 在后台线程中执行
    QThread* workerThread = QThread::create([this, authData]() {
        PSAMAuthResponse response;
        sendPSAMAuth(authData, response);
        emit psamAuthCompleted(response);
    });
    
    workerThread->start();
    connect(workerThread, &QThread::finished, workerThread, &QThread::deleteLater);
}

void SecurityVerifier::sendPSAMResultAsync(const PSAMResultData& resultData) {
    // 异步实现 - 在后台线程中执行
    QThread* workerThread = QThread::create([this, resultData]() {
        PSAMSignResultResponse response;
        sendPSAMResult(resultData, response);
        emit psamResultCompleted(response);
    });
    
    workerThread->start();
    connect(workerThread, &QThread::finished, workerThread, &QThread::deleteLater);
}

bool SecurityVerifier::isBusy() const {
    return busy;
}

SecurityError SecurityVerifier::getLastError() const {
    return lastError;
}

QString SecurityVerifier::getLastErrorString() const {
    return lastErrorString;
}

void SecurityVerifier::resetStatistics() {
    successCount = 0;
    failureCount = 0;
}

void SecurityVerifier::onNetworkReplyFinished() {
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        return;
    }
    
    reply->deleteLater();
    
    if (reply == currentReply) {
        currentReply = nullptr;
    }
}

void SecurityVerifier::onNetworkError(QNetworkReply::NetworkError error) {
    Q_UNUSED(error)
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (reply) {
        handleError(SecurityError::NetworkError, reply->errorString());
    }
}

void SecurityVerifier::onSslErrors(QNetworkReply* reply, const QList<QSslError>& errors) {
    QString errorString;
    for (const QSslError& error : errors) {
        errorString += error.errorString() + "; ";
    }
    
    writeLog(QString("SSL错误: %1").arg(errorString));
    
    // 在生产环境中，应该谨慎处理SSL错误
    // 这里暂时忽略SSL错误以便测试
    if (reply) {
        reply->ignoreSslErrors();
    }
}

void SecurityVerifier::onRetryTimer() {
    if (currentRetryCount < config.retryCount) {
        currentRetryCount++;
        writeLog(QString("重试请求 (%1/%2): %3").arg(currentRetryCount).arg(config.retryCount).arg(retryOperation));
        
        QString response = sendRequest(retryUrl, retryData);
        
        // 处理重试响应
        if (!response.isEmpty()) {
            // 重试成功，清理重试状态
            currentRetryCount = 0;
            retryUrl.clear();
            retryData = QJsonObject();
            retryOperation.clear();
        } else if (currentRetryCount < config.retryCount) {
            // 继续重试
            retryTimer->start(config.retryIntervalMs);
        } else {
            // 重试次数用完
            handleError(SecurityError::TimeoutError, QString("重试失败，已达到最大重试次数: %1").arg(config.retryCount));
            currentRetryCount = 0;
            retryUrl.clear();
            retryData = QJsonObject();
            retryOperation.clear();
        }
    }
}

QString SecurityVerifier::sendRequest(const QString& url, const QJsonObject& data) {
    QNetworkRequest request = createRequest(url);
    
    QJsonDocument doc(data);
    QByteArray jsonData = doc.toJson(QJsonDocument::Compact);
    
    writeLog(QString("发送请求到: %1, 数据大小: %2 字节").arg(url).arg(jsonData.size()));
    
    QNetworkReply* reply = networkManager->post(request, jsonData);
    
    connect(reply, &QNetworkReply::finished, this, &SecurityVerifier::onNetworkReplyFinished);
    connect(reply, QOverload<QNetworkReply::NetworkError>::of(&QNetworkReply::error),
            this, &SecurityVerifier::onNetworkError);
    
    currentReply = reply;
    
    // 同步等待响应
    QEventLoop loop;
    connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
    
    QTimer timeoutTimer;
    timeoutTimer.setSingleShot(true);
    timeoutTimer.setInterval(config.timeoutMs);
    connect(&timeoutTimer, &QTimer::timeout, &loop, &QEventLoop::quit);
    connect(&timeoutTimer, &QTimer::timeout, [reply]() {
        reply->abort();
    });
    
    timeoutTimer.start();
    loop.exec();
    
    QString response;
    if (reply->error() == QNetworkReply::NoError) {
        response = QString::fromUtf8(reply->readAll());
        writeLog(QString("收到响应: %1 字符").arg(response.length()));
    } else {
        handleError(SecurityError::NetworkError, reply->errorString());
    }
    
    reply->deleteLater();
    currentReply = nullptr;
    
    lastRequestTime = QDateTime::currentDateTime();
    return response;
}

QString SecurityVerifier::sendRequestWithRetry(const QString& url, const QJsonObject& data) {
    QString response = sendRequest(url, data);
    
    if (response.isEmpty() && config.retryCount > 0) {
        // 设置重试参数
        retryUrl = url;
        retryData = data;
        retryOperation = "Request";
        currentRetryCount = 0;
        
        // 开始重试
        retryTimer->start(config.retryIntervalMs);
    }
    
    return response;
}

QNetworkRequest SecurityVerifier::createRequest(const QString& url) const {
    QNetworkRequest request(url);
    
    // 设置Content-Type
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    
    // 设置User-Agent
    request.setHeader(QNetworkRequest::UserAgentHeader, config.userAgent);
    
    // 设置自定义头
    for (auto it = config.headers.begin(); it != config.headers.end(); ++it) {
        request.setRawHeader(it.key().toUtf8(), it.value().toUtf8());
    }
    
    // 设置超时
    request.setAttribute(QNetworkRequest::RedirectPolicyAttribute, 
                        QNetworkRequest::NoLessSafeRedirectPolicy);
    
    return request;
}

void SecurityVerifier::processSignResponse(const QString& response, PSAMSignResultResponse& result) {
    result.clear();
    
    if (response.isEmpty()) {
        result.code = "9999";
        result.message = "空响应";
        return;
    }
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8(), &error);
    
    if (error.error != QJsonParseError::NoError) {
        result.code = "9998";
        result.message = QString("JSON解析错误: %1").arg(error.errorString());
        return;
    }
    
    QJsonObject obj = doc.object();
    result.code = obj["code"].toString();
    result.message = obj["msg"].toString();
    result.responseTime = QDateTime::currentDateTime();
}

void SecurityVerifier::processAuthResponse(const QString& response, PSAMAuthResponse& result) {
    result.clear();
    
    if (response.isEmpty()) {
        result.code = "9999";
        result.message = "空响应";
        return;
    }
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8(), &error);
    
    if (error.error != QJsonParseError::NoError) {
        result.code = "9998";
        result.message = QString("JSON解析错误: %1").arg(error.errorString());
        return;
    }
    
    QJsonObject obj = doc.object();
    result.code = obj["code"].toString();
    result.message = obj["msg"].toString();
    result.listNo = obj["listNo"].toString();
    result.authInstruction = obj["authInstruction"].toString();
    result.responseTime = QDateTime::currentDateTime();
}

void SecurityVerifier::processResultResponse(const QString& response, PSAMSignResultResponse& result) {
    // 结果响应与签到响应格式相同
    processSignResponse(response, result);
}

void SecurityVerifier::handleError(SecurityError error, const QString& message) {
    lastError = error;
    lastErrorString = message;
    
    writeLog(QString("安全验证错误 [%1]: %2").arg(static_cast<int>(error)).arg(message));
    emit errorOccurred(error, message);
}

void SecurityVerifier::writeLog(const QString& message) {
    if (config.enableLogging) {
        QString logMessage = QString("[%1] SecurityVerifier: %2")
                            .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                            .arg(message);
        qDebug() << logMessage;
    }
}

QString SecurityVerifier::formatUrl(const QString& baseUrl, const QString& path) const {
    QString url = baseUrl;
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
        url = "http://" + url;
    }
    
    if (!path.startsWith("/")) {
        url += "/";
    }
    url += path;
    
    return url;
}

// SecurityHelper 实现
bool SecurityHelper::validatePSAMNo(const QString& psamNo) {
    // PSAM卡号通常是8-16位的十六进制字符串
    QRegularExpression regex("^[0-9A-Fa-f]{8,16}$");
    return regex.match(psamNo).hasMatch();
}

bool SecurityHelper::validateTerminalNo(const QString& terminalNo) {
    // 终端号通常是8-12位的数字或字母数字组合
    QRegularExpression regex("^[0-9A-Za-z]{8,12}$");
    return regex.match(terminalNo).hasMatch();
}

bool SecurityHelper::validateProvinceCode(const QString& provinceCode) {
    // 省份代码通常是2位数字
    QRegularExpression regex("^[0-9]{2}$");
    return regex.match(provinceCode).hasMatch();
}

bool SecurityHelper::validateStationCode(const QString& stationCode) {
    // 收费站代码通常是4-6位数字
    QRegularExpression regex("^[0-9]{4,6}$");
    return regex.match(stationCode).hasMatch();
}

QString SecurityHelper::formatPSAMNo(const QString& psamNo) {
    return psamNo.toUpper().trimmed();
}

QString SecurityHelper::formatTerminalTime(const QDateTime& dateTime) {
    return dateTime.toString("yyyy-MM-dd hh:mm:ss");
}

QString SecurityHelper::generateRandomString(int length) {
    const QString chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    QString result;
    
    for (int i = 0; i < length; ++i) {
        result += chars[QRandomGenerator::global()->bounded(chars.length())];
    }
    
    return result;
}

QString SecurityHelper::getErrorDescription(SecurityError error) {
    switch (error) {
        case SecurityError::NoError: return "无错误";
        case SecurityError::NetworkError: return "网络错误";
        case SecurityError::TimeoutError: return "超时错误";
        case SecurityError::InvalidData: return "数据无效";
        case SecurityError::ServerError: return "服务器错误";
        case SecurityError::AuthenticationFailed: return "认证失败";
        case SecurityError::PSAMError: return "PSAM卡错误";
        case SecurityError::ConfigurationError: return "配置错误";
        default: return "未知错误";
    }
}

SecurityError SecurityHelper::parseErrorCode(const QString& code) {
    if (code == "0000" || code == "200") {
        return SecurityError::NoError;
    } else if (code.startsWith("1")) {
        return SecurityError::NetworkError;
    } else if (code.startsWith("2")) {
        return SecurityError::TimeoutError;
    } else if (code.startsWith("3")) {
        return SecurityError::InvalidData;
    } else if (code.startsWith("4")) {
        return SecurityError::ServerError;
    } else if (code.startsWith("5")) {
        return SecurityError::AuthenticationFailed;
    } else if (code.startsWith("6")) {
        return SecurityError::PSAMError;
    } else {
        return SecurityError::ConfigurationError;
    }
}

QString SecurityHelper::buildUrl(const QString& baseUrl, const QString& path) {
    QString url = baseUrl;
    if (!url.endsWith("/") && !path.startsWith("/")) {
        url += "/";
    } else if (url.endsWith("/") && path.startsWith("/")) {
        url = url.left(url.length() - 1);
    }
    url += path;
    return url;
}

bool SecurityHelper::isValidUrl(const QString& url) {
    QRegularExpression regex("^https?://[^\\s/$.?#].[^\\s]*$");
    return regex.match(url).hasMatch();
}

QByteArray SecurityHelper::calculateMD5(const QString& data) {
    return QCryptographicHash::hash(data.toUtf8(), QCryptographicHash::Md5);
}

QByteArray SecurityHelper::calculateSHA256(const QString& data) {
    return QCryptographicHash::hash(data.toUtf8(), QCryptographicHash::Sha256);
}

QString SecurityHelper::encodeBase64(const QByteArray& data) {
    return data.toBase64();
}

QByteArray SecurityHelper::decodeBase64(const QString& data) {
    return QByteArray::fromBase64(data.toUtf8());
}

// PSAMSecurityManager 实现
PSAMSecurityManager::PSAMSecurityManager(QObject* parent)
    : QObject(parent)
    , pendingSignCount(0)
{
    writeLog("创建PSAM安全管理器");
}

PSAMSecurityManager::~PSAMSecurityManager() {
    writeLog("销毁PSAM安全管理器");
}

void PSAMSecurityManager::addVerifier(const QString& psamNo, std::shared_ptr<SecurityVerifier> verifier) {
    if (!verifier) {
        writeLog(QString("无法添加空的验证器: %1").arg(psamNo));
        return;
    }
    
    QMutexLocker locker(&verifiersMutex);
    
    // 如果已存在，先移除旧的
    if (verifiers.contains(psamNo)) {
        writeLog(QString("替换现有验证器: %1").arg(psamNo));
        verifiers.remove(psamNo);
    }
    
    verifiers[psamNo] = verifier;
    connectVerifierSignals(verifier);
    
    writeLog(QString("添加PSAM验证器: %1").arg(psamNo));
    emit verifierAdded(psamNo);
}

void PSAMSecurityManager::removeVerifier(const QString& psamNo) {
    QMutexLocker locker(&verifiersMutex);
    
    if (!verifiers.contains(psamNo)) {
        writeLog(QString("验证器不存在: %1").arg(psamNo));
        return;
    }
    
    verifiers.remove(psamNo);
    
    writeLog(QString("移除PSAM验证器: %1").arg(psamNo));
    emit verifierRemoved(psamNo);
}

std::shared_ptr<SecurityVerifier> PSAMSecurityManager::getVerifier(const QString& psamNo) {
    QMutexLocker locker(&verifiersMutex);
    
    auto it = verifiers.find(psamNo);
    if (it != verifiers.end()) {
        return it.value();
    }
    
    return nullptr;
}

QStringList PSAMSecurityManager::getVerifierList() const {
    QMutexLocker locker(&verifiersMutex);
    return verifiers.keys();
}

void PSAMSecurityManager::signAllPSAMs(const PSAMSignData& baseData) {
    QMutexLocker locker(&verifiersMutex);
    
    if (verifiers.isEmpty()) {
        writeLog("没有可用的PSAM验证器");
        emit allSignsCompleted();
        return;
    }
    
    writeLog(QString("开始批量PSAM签到，验证器数量: %1").arg(verifiers.size()));
    
    pendingSignCount = verifiers.size();
    completedSigns.clear();
    
    for (auto it = verifiers.begin(); it != verifiers.end(); ++it) {
        const QString& psamNo = it.key();
        auto verifier = it.value();
        
        if (verifier && !verifier->isBusy()) {
            // 为每个PSAM创建单独的签到数据
            PSAMSignData signData = baseData;
            signData.psamNo = psamNo;
            
            writeLog(QString("发送PSAM签到: %1").arg(psamNo));
            verifier->sendPSAMSignAsync(signData);
        } else {
            writeLog(QString("PSAM验证器忙碌，跳过: %1").arg(psamNo));
            pendingSignCount--;
        }
    }
    
    // 检查是否所有操作都已完成
    if (pendingSignCount == 0) {
        emit allSignsCompleted();
    }
}

void PSAMSecurityManager::setGlobalConfig(const SecurityConfig& config) {
    QMutexLocker locker(&verifiersMutex);
    
    globalConfig = config;
    
    // 应用到所有验证器
    for (auto it = verifiers.begin(); it != verifiers.end(); ++it) {
        auto verifier = it.value();
        if (verifier) {
            verifier->setConfig(config);
        }
    }
    
    writeLog("设置全局安全配置");
}

int PSAMSecurityManager::getActiveVerifierCount() const {
    QMutexLocker locker(&verifiersMutex);
    
    int count = 0;
    for (auto it = verifiers.begin(); it != verifiers.end(); ++it) {
        auto verifier = it.value();
        if (verifier && !verifier->isBusy()) {
            count++;
        }
    }
    
    return count;
}

QMap<QString, SecurityError> PSAMSecurityManager::getVerifierStatus() const {
    QMutexLocker locker(&verifiersMutex);
    
    QMap<QString, SecurityError> status;
    for (auto it = verifiers.begin(); it != verifiers.end(); ++it) {
        const QString& psamNo = it.key();
        auto verifier = it.value();
        
        if (verifier) {
            status[psamNo] = verifier->getLastError();
        } else {
            status[psamNo] = SecurityError::ConfigurationError;
        }
    }
    
    return status;
}

QMap<QString, QPair<int, int>> PSAMSecurityManager::getStatistics() const {
    QMutexLocker locker(&verifiersMutex);
    
    QMap<QString, QPair<int, int>> stats;
    for (auto it = verifiers.begin(); it != verifiers.end(); ++it) {
        const QString& psamNo = it.key();
        auto verifier = it.value();
        
        if (verifier) {
            stats[psamNo] = QPair<int, int>(verifier->getSuccessCount(), verifier->getFailureCount());
        } else {
            stats[psamNo] = QPair<int, int>(0, 0);
        }
    }
    
    return stats;
}

QString PSAMSecurityManager::generateStatusReport() const {
    QMutexLocker locker(&verifiersMutex);
    
    QStringList report;
    report << "=== PSAM安全管理器状态报告 ===";
    report << QString("生成时间: %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    report << QString("验证器总数: %1").arg(verifiers.size());
    report << QString("活跃验证器数: %1").arg(getActiveVerifierCount());
    report << QString("待处理签到操作: %1").arg(pendingSignCount);
    report << QString("已完成签到数: %1").arg(completedSigns.size());
    report << "";
    
    report << "验证器详情:";
    for (auto it = verifiers.begin(); it != verifiers.end(); ++it) {
        const QString& psamNo = it.key();
        auto verifier = it.value();
        
        report << QString("PSAM %1:").arg(psamNo);
        if (verifier) {
            report << QString("  状态: %1").arg(verifier->isBusy() ? "忙碌" : "空闲");
            report << QString("  最后错误: %1").arg(SecurityHelper::getErrorDescription(verifier->getLastError()));
            report << QString("  成功次数: %1").arg(verifier->getSuccessCount());
            report << QString("  失败次数: %1").arg(verifier->getFailureCount());
        } else {
            report << "  状态: 验证器对象为空";
        }
        report << "";
    }
    
    return report.join("\n");
}

void PSAMSecurityManager::onVerifierError(SecurityError error, const QString& message) {
    SecurityVerifier* verifier = qobject_cast<SecurityVerifier*>(sender());
    if (!verifier) {
        writeLog("收到未知来源的验证器错误");
        return;
    }
    
    // 查找是哪个PSAM的验证器
    QString psamNo;
    QMutexLocker locker(&verifiersMutex);
    for (auto it = verifiers.begin(); it != verifiers.end(); ++it) {
        if (it.value().get() == verifier) {
            psamNo = it.key();
            break;
        }
    }
    locker.unlock();
    
    if (psamNo.isEmpty()) {
        psamNo = "Unknown";
    }
    
    writeLog(QString("PSAM验证器错误 [%1]: %2 - %3")
            .arg(psamNo)
            .arg(SecurityHelper::getErrorDescription(error))
            .arg(message));
    
    emit verifierError(psamNo, error, message);
}

void PSAMSecurityManager::onSignCompleted(const PSAMSignResultResponse& response) {
    SecurityVerifier* verifier = qobject_cast<SecurityVerifier*>(sender());
    if (!verifier) {
        writeLog("收到未知来源的签到完成信号");
        return;
    }
    
    // 查找是哪个PSAM的验证器
    QString psamNo;
    QMutexLocker locker(&verifiersMutex);
    for (auto it = verifiers.begin(); it != verifiers.end(); ++it) {
        if (it.value().get() == verifier) {
            psamNo = it.key();
            break;
        }
    }
    locker.unlock();
    
    if (psamNo.isEmpty()) {
        psamNo = "Unknown";
    }
    
    writeLog(QString("PSAM签到完成 [%1]: %2 - %3")
            .arg(psamNo)
            .arg(response.code)
            .arg(response.message));
    
    // 减少待处理操作计数并记录完成的签到
    pendingSignCount--;
    completedSigns.append(psamNo);
    
    // 检查是否所有签到都已完成
    if (pendingSignCount <= 0) {
        writeLog("所有PSAM签到操作已完成");
        emit allSignsCompleted();
    }
}

void PSAMSecurityManager::connectVerifierSignals(std::shared_ptr<SecurityVerifier> verifier) {
    if (!verifier) {
        return;
    }
    
    // 连接错误信号
    connect(verifier.get(), &SecurityVerifier::errorOccurred,
            this, &PSAMSecurityManager::onVerifierError);
    
    // 连接签到完成信号
    connect(verifier.get(), &SecurityVerifier::psamSignCompleted,
            this, &PSAMSecurityManager::onSignCompleted);
}

void PSAMSecurityManager::writeLog(const QString& message) {
    QString logMessage = QString("[%1] PSAMSecurityManager: %2")
                        .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"))
                        .arg(message);
    qDebug() << logMessage;
}

} // namespace ETC
