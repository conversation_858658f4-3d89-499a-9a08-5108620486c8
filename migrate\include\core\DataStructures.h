/**
 * @file DataStructures.h
 * @brief RSU设备控制系统数据结构定义
 * 
 * 本文件从Delphi UDataStru.pas迁移而来，包含了RSU设备控制系统的核心数据结构
 * 支持出入口数据记录、员工信息、费率计算等业务场景
 */

#ifndef DATASTRUCTURES_H
#define DATASTRUCTURES_H

#include <QDateTime>
#include <QString>
#include <QByteArray>
#include <cstdint>

namespace ETC {

/**
 * 车辆类型枚举
 * 0：未知(如：冲岗车等)；1：普通收费车；2：优惠客车；10：军车；
 * 11：警车；12：车队；13：防汛救灾车；14：国安车；15：紧急车；
 * 20：全免公务车；21：路段免费公务车；22：区域免费公务车；30：绿通车。
 */
enum class VehicleType : uint16_t {
    Unknown = 0,
    Normal = 1,
    Preferential = 2,
    Military = 10,
    Police = 11,
    Convoy = 12,
    FloodControl = 13,
    NationalSecurity = 14,
    Emergency = 15,
    FullFreeOfficial = 20,
    SegmentFreeOfficial = 21,
    RegionalFreeOfficial = 22,
    GreenChannel = 30
};

/**
 * 车道类型枚举
 * 1：普通MTC入口车道；2：普通MTC出口车道；3：ETC入口车道；4：ETC出口车道；
 */
enum class LaneType : uint16_t {
    MTCEntry = 1,
    MTCExit = 2,
    ETCEntry = 3,
    ETCExit = 4
};

/**
 * 卡片类型枚举
 * 1：储值卡；2：记账卡；3：OBU；
 */
enum class CardType : uint16_t {
    StoredValue = 1,
    Account = 2,
    OBU = 3
};

/**
 * 支付类型枚举
 * 0：未知；1：现金支付；2：储值式赣通卡；3：记账式赣通卡；4：银联卡支付；
 * 8：欠费；9：免费（免征车）。
 */
enum class PaymentType : uint16_t {
    Unknown = 0,
    Cash = 1,
    StoredValueCard = 2,
    AccountCard = 3,
    UnionPay = 4,
    Arrears = 8,
    Free = 9
};

/**
 * 出口数据结构
 * 包含车辆出口时的完整收费信息
 */
struct __attribute__((packed)) ExitDataStru {
    char transCode[4];              // 车道交易的编码，比如0201表示出口收费
    QDateTime exitTime;             // 出口时间
    int32_t exitStationID;          // 收费站编号
    int16_t exitLaneID;             // 车道编号
    int32_t exitWasteSN;            // 流水序列号，每次车道启动后复位
    int16_t laneType;               // 车道类型
    char laneAppVer[10];            // 车道程序版本编号
    QDateTime loginTime;            // 上班时间
    QDateTime triggerTime;          // 压线圈时间
    QDateTime workDate;             // 工作日期
    int16_t shiftID;                // 班次
    int32_t operatorID;             // 操作员工号
    uint32_t psamID;                // PSAM卡编号
    char terminalID[12];            // PSAM卡终端机编号
    uint32_t psamTranSN;            // PSAM交易序列号
    int32_t cardSN;                 // 卡序列号，默认为0，全球唯一号
    int32_t cardCounter;            // 卡片读写计数
    char cardID2[16];               // 通行卡号
    char cardNetID[4];              // 卡片网络编号
    int16_t cardType;               // 通行卡类型
    
    char obuProvider[8];            // OBU区域代码(服务商代码)
    int32_t obuID;                  // 电子标签编号
    char obuID2[20];                // 记录OBU的SerialNumber
    int16_t vType;                  // 车种
    int16_t pvClass;                // 初判车型
    int16_t bvClass;                // 变档前车型
    int16_t vClass;                 // 最终车型
    int16_t autoVClass;             // 自动识别车型
    char autoVLP[16];               // 自动识别车牌
    char vLP[16];                   // 最终车牌
    int16_t autoVLPColor;           // 自动识别车牌颜色
    int16_t vLPColor;               // 最终车牌颜色
    
    int16_t axisNum;                // 车辆总轴数
    int32_t autoAxisType;           // 自动识别轴组组成
    int32_t axisType;               // 最终收费轴组组成
    char autoAxisInfo[80];          // 自动识别轴组信息
    char axisInfo[80];              // 最终收费轴组信息
    int32_t autoTotalWeight;        // 自动识别总重(kg)
    int32_t totalWeight;            // 总重(kg)
    int32_t limitWeight;            // 额定总重(kg)
    int32_t overLoadRate;           // 超限率，精度1％
    char dealStatus[256];           // 交易状态
    char devStatus[64];             // 设备状态
    int16_t vCnt;                   // 过车数量
    
    // 入口信息
    int32_t entryStation;           // 入口站号
    int16_t entryLane;              // 入口车道编号
    QDateTime entryTime;            // 入口时间
    int32_t entryWasteSN;           // 入口序列号
    int32_t entryOperatorID;        // 入口收费员工号
    int16_t entryVClass;            // 入口车型
    int16_t entryVType;             // 入口车种
    int32_t entryAxisType;          // 入口轴组组成
    int32_t entryTotalWeight;       // 入口总重（Kg）
    char entryVLP[16];              // 入口车牌
    int16_t entryVLPColor;          // 入口车牌颜色
    uint32_t entryPSAMID;           // 入口PSAM卡号
    
    // 收费信息
    int32_t overTime;               // 超时时间(秒)
    int16_t tollMode;               // 计费方式：1-车型计费 2-计重计费
    int32_t tollDistance;           // 计费里程（米）
    char tollPathInfo[2000];        // 计费路径
    int32_t realDistance;           // 实际里程(米)
    int32_t speed;                  // 车速（千米/小时）
    int16_t payType;                // 支付类型
    int32_t baseMoney;              // 基础总金额(分)
    int32_t overWeightMoney;        // 超限加收金额(分)
    int32_t freeMoney;              // 优惠的金额(分)
    int32_t rebateMoney;            // 针对通行费，折扣优惠(分)
    int32_t unpayMoney;             // 未/欠付金额（分）
    int32_t payMoney;               // 最终支付通行费金额(分)
    int32_t cardCostMoney;          // 最终卡成本金额（分）
    
    // 发票信息
    char invoiceType[2];            // 发票类型字段（01-财政票 02-税务票）
    int32_t invoiceCode;            // 发票代码
    int32_t invoiceID;              // 发票编号
    int16_t invoiceCnt;             // 发票张数
    
    // 支付卡信息
    char issuerID[8];               // 发卡方标识
    int16_t paySN;                  // 支付序号
    char payCardID2[20];            // 超长支付卡卡号
    char payCardNetID[4];           // 支付卡发卡方网络号
    uint32_t payCardTranSN;         // 支付卡交易号
    uint32_t payCardBalanceBefore;  // 支付卡扣款前余额
    uint32_t payCardBalance;        // 支付卡余额
    int16_t payCardRebate;          // 支付卡折扣(1%)
    uint32_t tac;                   // TAC码
    char paraVer[120];              // 参数版本
    
    // 版本信息
    int32_t tollVer;                // 收费表版本
    int32_t tollRateVer;            // 费率版本
    int32_t payCardBlackListVer;    // 支付卡黑名单版本
    int16_t flagStationNum;         // 标识站数
    char originalFlagStationInfo[300];   // 原始标识信息
    char tollFlagStationInfo[300];       // 收费标识信息
    char rationalFlagStationInfo[300];   // 理论标识信息
    
    // 免费信息
    int16_t freetype;               // 免费类型
    int16_t freeMode;               // 免费方式
    char freeArea[500];             // 免费区域
    int16_t splitNum;               // 拆分次数
    char splitInfo[2000];           // 拆分信息
    int16_t freeSplitNum;           // 免费拆分次数
    char freeSplitInfo[2600];       // 免费拆分信息
    
    // 其他信息
    int32_t cpcCurrentVol;          // CPC卡当前电量
    int32_t verifyCode;             // 校验码
    int16_t modifyFlag;             // 修改标志：1原始流水 2对冲流水 3修正流水
    int16_t verifyFlag;             // 校检标志
    int16_t exceptionType;          // 异常类型
    int32_t spare1;                 // 备用1
    int32_t spare2;                 // 备用2
    int32_t spare3;                 // 备用3，特殊事件代码
    char spare4[64];                // 备用4
    int16_t transFlag;              // 传输标识
    char vehicleJpgFile[64];        // 车道存储图像文件名
    char plateJpgFile[64];          // 车牌识别图像文件名
    uint8_t keyVersion;             // 密钥版本号
    uint8_t transType;              // 交易类型
};

/**
 * 入口数据结构
 * 包含车辆入口时的基本信息
 */
struct __attribute__((packed)) EntryDataStru {
    char transCode[4];              // 车道交易的编码
    QDateTime entryTime;            // 入口时间
    int32_t entryStationID;         // 收费站编号
    int16_t entryLaneID;            // 车道编号
    int32_t entryWasteSN;           // 流水序列号
    int16_t laneType;               // 车道类型
    char laneAppVer[10];            // 车道程序版本编号
    QDateTime loginTime;            // 上班时间
    QDateTime triggerTime;          // 压线圈时间
    QDateTime workDate;             // 工作日期
    int16_t shiftID;                // 班次
    int32_t operatorID;             // 操作员工号
    uint32_t psamID;                // PSAM卡编号
    char terminalID[12];            // PSAM卡终端机编号
    uint32_t psamTranSN;            // PSAM交易序列号
    int32_t cardSN;                 // 卡序列号
    int32_t cardCounter;            // 卡片读写计数
    char cardID2[16];               // 通行卡号
    char cardNetID[4];              // 卡片网络编号
    int16_t cardType;               // 卡片类型
    
    char obuProvider[8];            // OBU区域代码
    int32_t obuID;                  // 电子标签编号
    char obuID2[20];                // OBU SerialNumber
    
    int16_t vType;                  // 车种
    int16_t pvClass;                // 初判车型
    int16_t vClass;                 // 最终车型
    char autoVLP[16];               // 自动识别车牌
    char vLP[16];                   // 最终车牌
    int16_t autoVLPColor;           // 自动识别车牌颜色
    int16_t vLPColor;               // 最终车牌颜色
    
    int16_t axisNum;                // 车辆总轴数
    int32_t autoAxisType;           // 自动识别轴组组成
    int32_t axisType;               // 最终收费轴组组成
    char autoAxisInfo[80];          // 自动识别轴组信息
    char axisInfo[80];              // 最终收费轴组信息
    int32_t autoTotalWeight;        // 自动识别总重(kg)
    int32_t totalWeight;            // 总重(kg)
    int32_t limitWeight;            // 额定总重(kg)
    int32_t overLoadRate;           // 超限率
    
    char dealStatus[256];           // 交易状态
    char devStatus[64];             // 设备状态
    int16_t vCnt;                   // 过车数量
    char keyPressInfo[1025];        // 按键信息
    int32_t speed;                  // 车速
    
    char issuerID[8];               // 发卡方标识
    char payCardID2[20];            // 超长支付卡卡号
    char payCardNetID[4];           // 支付卡发卡方网络号
    uint32_t payCardTranSN;         // 支付卡交易号
    uint32_t tac;                   // TAC码
    uint32_t payCardBalance;        // 支付卡余额
    char paraVer[120];              // 参数版本
    
    int32_t cpcCurrentVol;          // CPC卡当前电量
    int32_t verifyCode;             // 校验码
    int16_t modifyFlag;             // 修改标志
    
    int32_t spare1;                 // 备用1
    int32_t spare2;                 // 备用2
    int32_t spare3;                 // 备用3
    char spare4[64];                // 备用4
    int16_t transFlag;              // 传输标识
    char vehicleJpgFile[64];        // 车辆图像文件名
    char plateJpgFile[64];          // 车牌图像文件名
    uint8_t keyVersion;             // 密钥版本号
    uint8_t transType;              // 交易类型
};

/**
 * 员工信息结构
 */
struct __attribute__((packed)) EmployeeStru {
    int32_t stationNo;              // 收费站编号
    int32_t laneNo;                 // 车道编号
    int32_t employeeNo;             // 员工编号
    int16_t groupNo;                // 班组编号
    QString employeeName;           // 员工姓名
    uint8_t logStatus;              // 登录状态：0未登录 1收费员登录 2维修员登录
    int16_t shiftNo;                // 班次编号
    QString shiftName;              // 班次名称
    QDateTime loginTime;            // 登录时间
    QDateTime offDutyTime;          // 下班时间
    QDateTime workDate;             // 工作日期
};

/**
 * 车辆类别结构
 */
struct __attribute__((packed)) VehicleClassStru {
    uint8_t vCode;                  // 车辆代码
    QString vName;                  // 车辆名称
};

/**
 * 费率拆分数据
 */
struct __attribute__((packed)) RateSplitData {
    char freeType;                  // 免费类型
    char rateID[2];                 // 费率ID
    char onerID[3];                 // 业主ID
    char county[4];                 // 县区代码
    char distance[6];               // 距离
};

/**
 * 金额拆分数据
 */
struct __attribute__((packed)) MoneySplitData {
    int32_t ownerID;                // 业主ID
    int32_t money;                  // 金额
    int32_t distance;               // 距离
};

/**
 * 收费费率结构
 */
struct __attribute__((packed)) TollRateStru {
    int32_t baseMoney;              // 基础金额
    int32_t freeMoney;              // 免费金额
    int32_t totalToll;              // 总通行费
    char splitInfo[3200];           // 拆分信息
    int32_t splitNum;               // 拆分数量
    int32_t tollDistance;           // 收费距离
    int32_t realDistance;           // 实际距离
    int32_t freeSplitNum;           // 免费拆分数量
    char freeSplitInfo[1400];       // 免费拆分信息
    char pathInfo[2000];            // 路径信息
    char rationalFlagStationInfo[300]; // 理论标识站信息
};

/**
 * 收费路径
 */
struct TollPath {
    int32_t moneyDistance;          // 计费距离
    int32_t realDistance;           // 实际距离
    QString pathInfo;               // 路径信息
    QString splitInfo;              // 拆分信息
    int32_t splitNum;               // 拆分数量
    int32_t rationalFlagStationNum; // 理论标识站数量
    QString rationalFlagStationInfo; // 理论标识站信息
    QString directionStation;       // 方向站
};

/**
 * 公务卡结构
 */
struct __attribute__((packed)) OfficeCardStru {
    int32_t freeType;               // 免费类型
    int32_t freeAreaNum;            // 免费区域数量
    char freeArea[500];             // 免费区域
    int32_t rebate;                 // 折扣
};

/**
 * 班次汇总结构
 */
struct __attribute__((packed)) ShiftSumStru {
    QDateTime useTime;              // 上班时间
    int32_t stationID;              // 收费站ID
    int16_t laneID;                 // 车道ID
    int32_t operatorID;             // 操作员ID
    QDateTime expireTime;           // 下班时间
    int16_t shiftID;                // 班次ID
    QDateTime workDate;             // 工作日期
    int32_t vCnt;                   // 总车辆计数
    int32_t vCCnt1;                 // 客1车型计数
    int32_t vCCnt2;                 // 客2车型计数
    int32_t vCCnt3;                 // 客3车型计数
    int32_t vCCnt4;                 // 客4车型计数
    int32_t vCCnt5;                 // 货1车型计数
    int32_t vCCnt6;                 // 货2车型计数
    int32_t vCCnt7;                 // 货3车型计数
    int32_t vCCnt8;                 // 货4车型计数
    int32_t vCCnt9;                 // 货5车型计数
    int32_t vCCnt10;                // 货6车型计数
    int32_t vCCnt11;                // 货7车型计数
    int32_t vCCnt12;                // 其他车型计数
    int32_t cardCnt1;               // 通行卡计数
    int32_t cardCnt2;               // 储值卡计数
    int32_t cardCnt3;               // 记账卡计数
    int32_t cardCnt4;               // 公务卡计数
    int32_t baseMoney;              // 基础总金额(分)
    int32_t storeMoney;             // 赣通储值金额(分)
    int32_t tallyMoney;             // 赣通记账金额(分)
    int32_t unipayMoney;            // 银联金额(分)
    int32_t payMoney;               // 最终支付通行费金额(分)
    int32_t vCMoney1;               // 客1车型金额
    int32_t vCMoney2;               // 客2车型金额
    int32_t vCMoney3;               // 客3车型金额
    int32_t vCMoney4;               // 客4车型金额
    int32_t vCMoney5;               // 货1车型金额
    int32_t vCMoney6;               // 货2车型金额
    int32_t vCMoney7;               // 货3车型金额
    int32_t vCMoney8;               // 货4车型金额
    int32_t vCMoney9;               // 货5车型金额
    int32_t vCMoney12;              // 其他车型金额
    int16_t integrityFlag;          // 完整性标志
    int16_t verifyCode;             // 校验码
    int16_t transFlag;              // 传输标识
    char vLP[16];                   // 当前车辆车牌
    int32_t curMoney;               // 当前车辆收费金额
    QDateTime passTime;             // 通过时间
    char cardID2[20];               // 卡号
};

/**
 * 车辆转换参数
 */
struct __attribute__((packed)) VClassConversion {
    uint8_t vType;                  // 车种/车类
    uint16_t vLength;               // 车长
    uint8_t vHeight;                // 车头高
    uint8_t vWidth;                 // 车宽
    uint8_t wheelNum;               // 轮数
    uint8_t axleNum;                // 轴数
    uint32_t vWeight;               // 载重/座数
};

/**
 * 标识站结构
 */
struct __attribute__((packed)) FlagStationStru {
    int32_t flagStation;            // 收费站编号
    QDateTime passTime;             // 通过时间
};

/**
 * 特殊白名单车辆
 * 0：公交车；1：北斗车
 */
struct WhiteVehicle {
    QString plate;                  // 车牌号
    QDateTime lastedPassTime;       // 最后通过时间
    int32_t wClass;                 // 白名单类别
    int32_t vClass;                 // 车辆类别
    QString line;                   // 线路
};

} // namespace ETC

// CRC32相关函数声明
namespace CRC {
    uint32_t getCRC32(const QString& source);
    void calculateEntryCRC(ETC::EntryDataStru& data);
    void calculateExitCRC(ETC::ExitDataStru& data);
    void calculateShiftSumCRC(ETC::ShiftSumStru& data);
}

#endif // DATASTRUCTURES_H
