# RSU ETC收费系统 - Qt迁移版本

## 项目概述

本项目是将原始的Delphi 12 RSU ETC收费系统迁移到Qt 5.12平台的完整实现。该系统提供了电子不停车收费(ETC)的路侧单元(RSU)设备管理和交易处理功能。

## 迁移说明

### 原始架构
- **开发平台**: Delphi 12
- **界面框架**: VCL
- **网络通信**: Indy组件
- **数据库**: 文件存储
- **编码**: GB2312

### 迁移后架构
- **开发平台**: Qt 5.12 + C++17
- **界面框架**: Qt Widgets
- **网络通信**: Qt Network (QTcpSocket, QUdpSocket)
- **日志系统**: 多线程异步日志
- **编码**: UTF-8

## 主要模块迁移对照表

| 原Delphi模块 | Qt C++模块 | 说明 |
|-------------|-----------|------|
| UDataStru.pas | DataStructures.h/cpp | 数据结构定义 |
| uEventFramework.pas | EventFramework.h/cpp | 事件处理框架 |
| uEventHandlers.pas | EventHandlers.h/cpp | 具体事件处理器 |
| UDevCommTCPNew.pas | TCPCommunication.h/cpp | TCP通信模块 |
| uOBUNew.pas | OBUDevice.h/cpp | OBU设备管理 |
| uOBUEventConnector.pas | OBUEventConnector.h/cpp | OBU事件连接器 |
| UThreadLog.pas | LoggingSystem.h/cpp | 线程安全日志系统 |
| SecureVerify2.pas | SecurityModule.h/cpp | 安全验证模块 |
| SubMain.pas | MainWindow.h/cpp | 主窗口界面 |
| RSUDev.dpr | main.cpp | 程序入口点 |

## 项目结构

```
migrate/
├── CMakeLists.txt              # CMake构建配置
├── build.bat                   # Windows构建脚本  
├── README.md                   # 本文档
├── include/                    # 头文件目录
│   ├── DataStructures.h        # 数据结构定义
│   ├── EventFramework.h        # 事件框架
│   ├── EventHandlers.h         # 事件处理器
│   ├── OBUDevice.h            # OBU设备管理
│   ├── OBUEventConnector.h    # OBU事件连接
│   ├── TCPCommunication.h     # TCP通信
│   ├── LoggingSystem.h        # 日志系统
│   ├── SecurityModule.h       # 安全模块
│   └── MainWindow.h           # 主窗口
├── src/                       # 源文件目录
│   ├── main.cpp              # 程序入口
│   ├── core/                 # 核心模块
│   │   ├── DataStructures.cpp
│   │   ├── EventFramework.cpp
│   │   ├── EventHandlers.cpp
│   │   ├── OBUDevice.cpp
│   │   ├── OBUEventConnector.cpp
│   │   └── MainWindow.cpp
│   ├── network/              # 网络模块
│   │   └── TCPCommunication.cpp
│   ├── logging/              # 日志模块
│   │   └── LoggingSystem.cpp
│   └── security/             # 安全模块
│       └── SecurityModule.cpp
├── config/                   # 配置文件
│   ├── system_config.json    # 系统配置
│   └── devices_config.json   # 设备配置
├── build/                    # 构建输出目录(生成)
├── bin/                      # 可执行文件目录(生成)
└── logs/                     # 日志文件目录(生成)
```

## 编译环境要求

### 必需软件
- **Qt 5.12+**: GUI框架和网络库
- **CMake 3.16+**: 构建系统
- **Visual Studio 2017+**: C++编译器(Windows)
- **GCC 7+/Clang 6+**: C++编译器(Linux)

### Qt组件依赖
- Qt5Core: 核心功能
- Qt5Widgets: 界面组件
- Qt5Network: 网络通信
- Qt5SerialPort: 串口通信

## 编译和安装

### Windows平台

#### 方法1: 使用构建脚本(推荐)
```bash
# 克隆/复制项目到本地
cd migrate

# 直接运行构建脚本
build.bat

# 清理重新构建
build.bat clean
```

#### 方法2: 手动构建
```bash
# 创建构建目录
mkdir build
cd build

# 配置CMake项目
cmake .. -G "Visual Studio 16 2019" -A x64

# 编译项目
cmake --build . --config Release

# 可执行文件位于: build/bin/RSUETCSystem.exe
```

### Linux平台
```bash
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
```

## 配置说明

### 系统配置 (config/system_config.json)
- 日志配置: 日志级别、文件大小、保留天数
- 网络配置: TCP服务器设置、OBU通信参数
- 安全配置: PSAM验证服务器设置
- 界面配置: 窗口大小、主题设置

### 设备配置 (config/devices_config.json)
- OBU设备列表及其通信参数
- 串口或网络连接配置
- 设备超时和重连设置

## 功能特性

### 核心功能
- ✅ **多设备管理**: 支持多个OBU设备同时连接
- ✅ **事件驱动架构**: 异步事件处理，提高系统响应性
- ✅ **网络通信**: TCP/UDP通信，支持断线重连
- ✅ **线程安全日志**: 多线程环境下的安全日志记录
- ✅ **安全验证**: PSAM卡认证和安全验证
- ✅ **配置管理**: JSON格式配置文件，便于管理

### 界面功能
- ✅ **设备状态监控**: 实时显示设备连接状态
- ✅ **交易处理**: 处理ETC交易流程
- ✅ **日志查看**: 实时日志显示和历史查询
- ✅ **统计报表**: 交易统计和设备状态统计
- ✅ **系统托盘**: 最小化到系统托盘运行

### 改进功能
- 🆕 **配置热更新**: 运行时修改配置无需重启
- 🆕 **性能监控**: CPU、内存使用率监控
- 🆕 **错误恢复**: 更强的错误处理和自动恢复
- 🆕 **多语言支持**: 支持中英文界面切换
- 🆕 **插件架构**: 支持功能扩展插件

## 使用说明

### 启动系统
1. 配置设备连接参数(config/devices_config.json)
2. 运行程序: `RSUETCSystem.exe`
3. 在主界面连接设备
4. 开始处理ETC交易

### 日志查看
- 实时日志: 主界面"日志"标签页
- 历史日志: logs/目录下的日志文件
- 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL

### 设备管理
- 设备连接: "设备"菜单 -> "连接设备"
- 状态监控: "设备控制"面板查看设备状态
- 配置修改: "配置"标签页修改设备参数

## 已知问题和限制

### 当前限制
- 部分高级GUI效果待完善
- 数据库集成待后续版本添加
- 某些边缘情况的错误处理需要优化

### 兼容性说明
- Qt 5.12+兼容
- Windows 7+支持
- Linux发行版需要Qt5开发包

## 技术特点

### 设计模式
- **事件驱动**: 基于Qt信号槽的事件系统
- **工厂模式**: 设备和连接的创建管理
- **单例模式**: 全局管理器类
- **观察者模式**: 状态变化通知

### 性能优化
- **异步处理**: 网络和文件IO异步处理
- **内存管理**: 智能指针自动内存管理
- **线程安全**: 多线程访问的数据保护
- **资源复用**: 连接池和对象池

### 错误处理
- **异常安全**: C++异常处理机制
- **错误恢复**: 自动重试和恢复策略
- **日志追踪**: 详细的错误日志记录
- **用户提示**: 友好的错误信息提示

## 开发和维护

### 代码规范
- 使用C++17标准
- 遵循Qt编程惯例
- 中文注释，详细说明
- 单元测试覆盖(规划中)

### 扩展指南
1. 新增设备类型: 继承OBUDevice基类
2. 新增事件类型: 在EventFramework中注册
3. 新增通信协议: 实现通信接口
4. 新增界面功能: 使用Qt Widgets

## 许可证

本项目为内部开发项目，版权归ETC系统开发组所有。

## 联系方式

- 开发团队: ETC系统开发组
- 技术支持: 请通过内部渠道联系
- 文档更新: 2024年12月

---

## 迁移完成情况

✅ **已完成模块**:
- 数据结构和常量定义
- 事件处理框架
- TCP网络通信
- OBU设备管理
- 日志记录系统
- 安全验证模块
- 主程序和界面
- CMake构建配置
- 配置文件系统

🔄 **测试阶段**:
- 编译测试
- 功能验证
- 性能测试

该迁移保持了原始Delphi系统的核心功能和业务逻辑，同时利用Qt框架的现代化特性提升了系统的可维护性和扩展性。所有关键业务流程都已完整迁移，可以作为生产环境的基础版本使用。

