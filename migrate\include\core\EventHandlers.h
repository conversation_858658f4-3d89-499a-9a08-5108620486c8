/**
 * @file EventHandlers.h
 * @brief RSU设备控制系统具体事件处理器定义
 * 
 * 本文件从Delphi uEventHandlers.pas迁移而来，包含各种具体的事件处理器实现
 * 支持设备管理、数据获取、交易处理、认证管理等业务场景
 */

#ifndef EVENTHANDLERS_H
#define EVENTHANDLERS_H

#include "core/EventFramework.h"
#include "CommonDataStructures.h"
#include <QStringList>
#include <QTimer>
#include <functional>

namespace ETC {

// 回调函数类型定义
using ShowInfoCallback = std::function<void(const QString&)>;
using WriteErrorLogCallback = std::function<void(const QString&)>;
using UpdateDeviceStatusCallback = std::function<void(int, int)>;
using ProcessOBUErrorCallback = std::function<void(uint8_t, uint32_t, uint8_t, uint8_t)>;
using ProcessGetB0Callback = std::function<void(uint8_t, const TB0Info&)>;
using ProcessGetBACallback = std::function<void(uint8_t, const TBAInfo&)>;
using ProcessGetBBCallback = std::function<void(uint8_t, uint32_t, uint8_t, const QVector<TAuthResult>&)>;
using ProcessGetB2Callback = std::function<void(uint8_t, uint32_t, const TB2Info&)>;

/**
 * 增强的设备管理事件处理器 - 支持GUI交互
 */
class EnhancedDeviceManagerHandler : public BaseEventHandler {
    Q_OBJECT

public:
    EnhancedDeviceManagerHandler(ShowInfoCallback showInfoCallback,
                                WriteErrorLogCallback writeErrorLogCallback,
                                UpdateDeviceStatusCallback updateDeviceStatusCallback,
                                QObject* parent = nullptr);

    void setMaxRetryCount(int count) { maxRetryCount = count; }
    void setRetryInterval(int interval) { retryInterval = interval; }
    
    int getMaxRetryCount() const { return maxRetryCount; }
    int getRetryInterval() const { return retryInterval; }

protected:
    EventResult doHandleEvent(std::shared_ptr<EventData> eventData) override;

private:
    EventResult handleRSUError(std::shared_ptr<RSUErrorEventData> eventData);
    EventResult handleRSUReset(std::shared_ptr<EventData> eventData);
    EventResult handleRSUHeart(std::shared_ptr<EventData> eventData);

    int maxRetryCount;
    int retryInterval;
    ShowInfoCallback showInfoCallback;
    WriteErrorLogCallback writeErrorLogCallback;
    UpdateDeviceStatusCallback updateDeviceStatusCallback;
};

/**
 * 增强的OBU错误处理器 - 集成原始OnOBUError逻辑
 */
class EnhancedOBUErrorHandler : public BaseEventHandler {
    Q_OBJECT

public:
    EnhancedOBUErrorHandler(ProcessOBUErrorCallback processOBUErrorCallback,
                           QObject* parent = nullptr);

protected:
    EventResult doHandleEvent(std::shared_ptr<EventData> eventData) override;

private:
    EventResult handleOBUError(std::shared_ptr<OBUErrorEventData> eventData);

    ProcessOBUErrorCallback processOBUErrorCallback;
};

/**
 * 增强的B0事件处理器 - 集成原始OnGetB0逻辑
 */
class EnhancedGetB0Handler : public BaseEventHandler {
    Q_OBJECT

public:
    EnhancedGetB0Handler(ProcessGetB0Callback processGetB0Callback,
                        QObject* parent = nullptr);

protected:
    EventResult doHandleEvent(std::shared_ptr<EventData> eventData) override;

private:
    EventResult handleGetB0(std::shared_ptr<GetB0EventData> eventData);

    ProcessGetB0Callback processGetB0Callback;
};

/**
 * 增强的BA事件处理器 - 集成原始OnGetBA逻辑
 */
class EnhancedGetBAHandler : public BaseEventHandler {
    Q_OBJECT

public:
    EnhancedGetBAHandler(ProcessGetBACallback processGetBACallback,
                        QObject* parent = nullptr);

protected:
    EventResult doHandleEvent(std::shared_ptr<EventData> eventData) override;

private:
    EventResult handleGetBA(std::shared_ptr<GetBAEventData> eventData);

    ProcessGetBACallback processGetBACallback;
};

/**
 * 增强的BB事件处理器 - 集成原始OnGetBB逻辑
 */
class EnhancedGetBBHandler : public BaseEventHandler {
    Q_OBJECT

public:
    EnhancedGetBBHandler(ProcessGetBBCallback processGetBBCallback,
                        QObject* parent = nullptr);

protected:
    EventResult doHandleEvent(std::shared_ptr<EventData> eventData) override;

private:
    EventResult handleGetBB(std::shared_ptr<GetBBEventData> eventData);

    ProcessGetBBCallback processGetBBCallback;
};

/**
 * 增强的B2事件处理器 - 集成原始OnGetB2逻辑
 */
class EnhancedGetB2Handler : public BaseEventHandler {
    Q_OBJECT

public:
    EnhancedGetB2Handler(ProcessGetB2Callback processGetB2Callback,
                        QObject* parent = nullptr);

protected:
    EventResult doHandleEvent(std::shared_ptr<EventData> eventData) override;

private:
    EventResult handleGetB2(std::shared_ptr<GetB2EventData> eventData);

    ProcessGetB2Callback processGetB2Callback;
};

/**
 * 设备管理事件处理器
 */
class DeviceManagerHandler : public BaseEventHandler {
    Q_OBJECT

public:
    DeviceManagerHandler(QObject* parent = nullptr);

    void setMaxRetryCount(int count) { maxRetryCount = count; }
    void setRetryInterval(int interval) { retryInterval = interval; }
    
    int getMaxRetryCount() const { return maxRetryCount; }
    int getRetryInterval() const { return retryInterval; }

protected:
    EventResult doHandleEvent(std::shared_ptr<EventData> eventData) override;

private:
    EventResult handleRSUError(std::shared_ptr<RSUErrorEventData> eventData);
    EventResult handleRSUReset(std::shared_ptr<EventData> eventData);
    EventResult handleRSUHeart(std::shared_ptr<EventData> eventData);

    int maxRetryCount;
    int retryInterval;
};

/**
 * 数据获取事件处理器
 */
class DataAcquisitionHandler : public BaseEventHandler {
    Q_OBJECT

public:
    DataAcquisitionHandler(QObject* parent = nullptr);
    ~DataAcquisitionHandler();

    void setCacheEnabled(bool enabled) { cacheEnabled = enabled; }
    void setMaxCacheSize(int size) { maxCacheSize = size; }
    
    bool getCacheEnabled() const { return cacheEnabled; }
    int getMaxCacheSize() const { return maxCacheSize; }
    
    void clearCache();

protected:
    EventResult doHandleEvent(std::shared_ptr<EventData> eventData) override;

private:
    EventResult handleGetB0(std::shared_ptr<GetB0EventData> eventData);
    EventResult handleGetOBUID(std::shared_ptr<GetOBUIDEventData> eventData);
    EventResult handleGetB4(std::shared_ptr<EventData> eventData);
    EventResult handleGetB5(std::shared_ptr<EventData> eventData);
    EventResult handleGetOBUVehicle(std::shared_ptr<EventData> eventData);
    EventResult handleGetCPUInfo(std::shared_ptr<EventData> eventData);

    void addToCache(const QString& key, const QString& value);
    QString getFromCache(const QString& key);

    QStringList dataCache;
    bool cacheEnabled;
    int maxCacheSize;
};

/**
 * 交易处理事件处理器
 */
class TransactionHandler : public BaseEventHandler {
    Q_OBJECT

public:
    TransactionHandler(QObject* parent = nullptr);
    ~TransactionHandler();

    void setAutoSaveInterval(int interval) { autoSaveInterval = interval; }
    int getAutoSaveInterval() const { return autoSaveInterval; }
    int getTransactionCount() const { return transactionCount; }
    
    void saveLogNow();

protected:
    EventResult doHandleEvent(std::shared_ptr<EventData> eventData) override;

private slots:
    void checkAutoSave();

private:
    EventResult handleOBUSessionEnd(std::shared_ptr<EventData> eventData);
    EventResult handleOBUConsumed(std::shared_ptr<EventData> eventData);
    EventResult handlePurTimeOut(std::shared_ptr<EventData> eventData);

    void logTransaction(const QString& transactionInfo);
    void saveTransactionLog();
    QString formatTransactionInfo(std::shared_ptr<EventData> eventData);

    QStringList transactionLog;
    int autoSaveInterval;
    QDateTime lastSaveTime;
    int transactionCount;
    QTimer* autoSaveTimer;
};

/**
 * 认证管理事件处理器
 */
class AuthenticationHandler : public BaseEventHandler {
    Q_OBJECT

public:
    AuthenticationHandler(QObject* parent = nullptr);
    ~AuthenticationHandler();

    void setSecurityLevel(int level) { securityLevel = level; }
    void setMaxFailedAttempts(int attempts) { maxFailedAttempts = attempts; }
    
    int getSecurityLevel() const { return securityLevel; }
    int getMaxFailedAttempts() const { return maxFailedAttempts; }
    int getFailedAttempts() const { return failedAttempts; }

protected:
    EventResult doHandleEvent(std::shared_ptr<EventData> eventData) override;

private:
    EventResult handlePSAMInit(std::shared_ptr<EventData> eventData);
    EventResult handlePSAMResult(std::shared_ptr<EventData> eventData);

    void logAuthEvent(const QString& authInfo);
    bool validateSecurityLevel(std::shared_ptr<EventData> eventData);
    void handleFailedAuthentication();

    QStringList authLog;
    int securityLevel;
    int maxFailedAttempts;
    int failedAttempts;
};

/**
 * 错误处理事件处理器
 */
class ErrorHandler : public BaseEventHandler {
    Q_OBJECT

public:
    ErrorHandler(QObject* parent = nullptr);
    ~ErrorHandler();

    int getErrorCount() const { return errorCount; }
    void generateErrorReport(const QString& reportFile);

protected:
    EventResult doHandleEvent(std::shared_ptr<EventData> eventData) override;

private:
    EventResult handleOBUError(std::shared_ptr<OBUErrorEventData> eventData);

    void logError(const QString& errorInfo);
    void updateErrorStatistics(const QString& errorType);
    QString getErrorRecoveryAction(std::shared_ptr<EventData> eventData);

    QStringList errorLog;
    int errorCount;
    QStringList errorStatistics;
    int maxErrorLogSize;
};

/**
 * 监控统计事件处理器
 */
class MonitoringHandler : public BaseEventHandler {
    Q_OBJECT

public:
    MonitoringHandler(QObject* parent = nullptr);
    ~MonitoringHandler();

    void setMonitoringEnabled(bool enabled) { monitoringEnabled = enabled; }
    void setReportInterval(int interval) { reportInterval = interval; }
    
    bool getMonitoringEnabled() const { return monitoringEnabled; }
    int getReportInterval() const { return reportInterval; }
    
    void resetStatistics();
    void saveStatisticsReport(const QString& reportFile);

protected:
    EventResult doHandleEvent(std::shared_ptr<EventData> eventData) override;

private slots:
    void checkReportGeneration();

private:
    void updateStatistics(std::shared_ptr<EventData> eventData);
    void generatePerformanceReport();
    QString getEventStatistics(EventType eventType);

    QStringList statistics;
    bool monitoringEnabled;
    QDateTime lastReportTime;
    int reportInterval;
    QTimer* reportTimer;
};

} // namespace ETC

#endif // EVENTHANDLERS_H
